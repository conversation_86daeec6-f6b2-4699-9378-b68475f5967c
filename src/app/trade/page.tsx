'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Filter,
  Search,
  Star,
  Shield,
  Clock,
  Wallet,
  ChevronDown
} from 'lucide-react';
import { Button, Card, Icon } from '@/components/ui';
import {
  FinanceIcons,
  UserIcons
} from '@/config/icons';
import Header from '@/components/layout/Header';
import { useWallet } from '@/hooks/useWallet';
import { useTelegramAuth } from '@/hooks/useTelegramAuth';
import { useMerchantSystem } from '@/hooks/useMerchantSystem';
import { formatNumber, cn } from '@/lib/utils';
import TelegramLogin from '@/components/auth/TelegramLogin';

// HAOX代币价格数据接口
interface HAOXPriceData {
  currentPrice: number;
  priceChange1h: number;
  priceChange24h: number;
  priceChange7d: number;
  priceChange30d: number;
  volume24h: number;
  marketCap: number;
  lastUpdated: string;
}

// 商家订单类型
interface MerchantOrder {
  id: string;
  merchantName: string;
  merchantRating: number;
  merchantOrders: number;
  orderType: 'buy' | 'sell';
  price: number;
  amount: number;
  minAmount: number;
  maxAmount: number;
  paymentMethods: string[];
  completionRate: number;
  avgTime: string;
  isOnline: boolean;
  verified: boolean;
}

export default function TradePage() {
  const { walletAddress } = useWallet();
  const { user, isAuthenticated } = useTelegramAuth();
  const {
    isMerchant,
    merchantInfo,
    canApply,
    checkMerchantRequirements,
    applyForMerchant,
    isLoading: merchantLoading
  } = useMerchantSystem();

  const [activeMainTab, setActiveMainTab] = useState<'market' | 'c2c'>('market');
  const [activeTab, setActiveTab] = useState<'buy' | 'sell'>('buy');
  const [selectedCurrency, setSelectedCurrency] = useState('CNY');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('all');
  const [showMerchantApplication, setShowMerchantApplication] = useState(false);
  const [priceData, setPriceData] = useState<HAOXPriceData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // 模拟HAOX价格数据
  useEffect(() => {
    const fetchPriceData = async () => {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      setPriceData({
        currentPrice: 0.0245, // 预售结束后的初始价格
        priceChange1h: 2.34,
        priceChange24h: 5.67,
        priceChange7d: 15.67,
        priceChange30d: 45.89,
        volume24h: 125000,
        marketCap: 2450000,
        lastUpdated: new Date().toISOString(),
      });

      setIsLoading(false);
    };

    fetchPriceData();
  }, []);

  // Mock HAOX price data for C2C
  const haoxPrice = 7.2; // CNY (参考图片中的价格)
  const priceChange24h = 5.67;

  // 模拟商家订单数据
  const mockOrders: MerchantOrder[] = [
    {
      id: '1',
      merchantName: '金顶商行-24H-资金安全',
      merchantRating: 4.9,
      merchantOrders: 5474,
      orderType: 'sell',
      price: 7.2,
      amount: 16942.66,
      minAmount: 1000,
      maxAmount: 30000,
      paymentMethods: ['支付宝', '微信', '银行卡'],
      completionRate: 99.70,
      avgTime: '15分钟',
      isOnline: true,
      verified: true
    },
    {
      id: '2',
      merchantName: '鸿发币行-资金安全-实名付款-零冻结',
      merchantRating: 4.8,
      merchantOrders: 244,
      orderType: 'sell',
      price: 7.12,
      amount: 163160.65,
      minAmount: 200000,
      maxAmount: 1161703,
      paymentMethods: ['银行卡'],
      completionRate: 100.0,
      avgTime: '15分钟',
      isOnline: true,
      verified: true
    }
  ];

  const paymentMethods = [
    { id: 'all', name: '全部', icon: '💳' },
    { id: 'alipay', name: '支付宝', icon: '💰' },
    { id: 'wechat', name: '微信支付', icon: '💚' },
    { id: 'bank', name: '银行卡', icon: '🏦' },
  ];

  // 获取商家要求检查结果
  const merchantRequirements = checkMerchantRequirements();

  // 处理商家申请
  const handleMerchantApplication = async () => {
    const success = await applyForMerchant({
      businessName: `${user?.username || 'User'} Trading`,
      businessType: 'individual',
      contactEmail: `${user?.username || 'user'}@telegram.user`,
      tradingExperience: 'Experienced crypto trader',
      expectedVolume: 100000
    });

    if (success) {
      setShowMerchantApplication(false);
    }
  };

  // 过滤订单
  const filteredOrders = mockOrders.filter(order => {
    const matchesType = order.orderType === activeTab;
    const matchesSearch = order.merchantName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesPayment = selectedPaymentMethod === 'all' || 
      order.paymentMethods.some(method => 
        method.includes(paymentMethods.find(pm => pm.id === selectedPaymentMethod)?.name || '')
      );
    return matchesType && matchesSearch && matchesPayment;
  });

  return (
    <div className="min-h-screen bg-system-background">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* 页面标题 */}
        <div className="text-center mb-8">
          <h1 className="text-large-title font-sf-pro font-bold text-label mb-4">
            HAOX 交易中心
          </h1>
          <p className="text-body text-secondary-label max-w-2xl mx-auto">
            查看实时价格信息，与认证商家进行安全的C2C交易
          </p>
        </div>

        {/* 主标签切换 */}
        <div className="flex justify-center mb-8">
          <div className="bg-system-gray-6 rounded-xl p-1 flex">
            {[
              { key: 'market', label: '市场信息', icon: FinanceIcons.activity },
              { key: 'c2c', label: 'C2C交易', icon: UserIcons.users },
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveMainTab(tab.key as any)}
                className={cn(
                  'flex items-center space-x-2 px-6 py-3 rounded-lg transition-all duration-200',
                  activeMainTab === tab.key
                    ? 'bg-system-blue text-white shadow-sm'
                    : 'text-secondary-label hover:text-label'
                )}
              >
                <Icon icon={tab.icon} size="sm" />
                <span className="font-sf-pro font-medium">{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* 市场信息标签 */}
        {activeMainTab === 'market' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            {/* 当前价格卡片 */}
            <Card className="text-center">
              <div className="py-8">
                <div className="w-20 h-20 bg-gradient-to-br from-system-blue to-system-purple rounded-full flex items-center justify-center mx-auto mb-6">
                  <Icon icon={FinanceIcons.coins} size="2xl" className="text-white" />
                </div>

                <h2 className="text-title-1 font-sf-pro font-bold text-label mb-2">
                  HAOX 代币
                </h2>

                {isLoading ? (
                  <div className="animate-pulse">
                    <div className="h-12 bg-system-gray-6 rounded-lg w-48 mx-auto mb-4"></div>
                    <div className="h-6 bg-system-gray-6 rounded w-32 mx-auto"></div>
                  </div>
                ) : priceData ? (
                  <>
                    <div className="text-4xl font-sf-pro font-bold text-label mb-2">
                      ${priceData.currentPrice.toFixed(4)}
                    </div>
                    <div className="flex items-center justify-center">
                      <span className={cn(
                        'flex items-center space-x-1',
                        priceData.priceChange24h > 0 ? 'text-system-green' : 'text-system-red'
                      )}>
                        <Icon
                          icon={priceData.priceChange24h > 0 ? FinanceIcons.trendUp : FinanceIcons.trendDown}
                          size="xs"
                        />
                        <span>{priceData.priceChange24h > 0 ? '+' : ''}{priceData.priceChange24h.toFixed(2)}%</span>
                      </span>
                    </div>
                  </>
                ) : null}
              </div>
            </Card>

            {/* 价格变化统计 */}
            {priceData && (
              <div className="grid md:grid-cols-4 gap-4">
                {[
                  { label: '1小时', value: priceData.priceChange1h },
                  { label: '24小时', value: priceData.priceChange24h },
                  { label: '7天', value: priceData.priceChange7d },
                  { label: '30天', value: priceData.priceChange30d },
                ].map((item, index) => (
                  <Card key={index}>
                    <div className="text-center py-4">
                      <p className="text-caption-1 text-secondary-label mb-2">
                        {item.label}变化
                      </p>
                      <div className="text-xl font-sf-pro font-semibold">
                        <span className={cn(
                          'flex items-center justify-center space-x-1',
                          item.value > 0 ? 'text-system-green' : 'text-system-red'
                        )}>
                          <Icon
                            icon={item.value > 0 ? FinanceIcons.trendUp : FinanceIcons.trendDown}
                            size="xs"
                          />
                          <span>{item.value > 0 ? '+' : ''}{item.value.toFixed(2)}%</span>
                        </span>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}

            {/* 市场统计 */}
            {priceData && (
              <div className="grid md:grid-cols-2 gap-6">
                <Card title="24小时交易量">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-system-green/20 rounded-xl flex items-center justify-center">
                      <Icon icon={FinanceIcons.activity} size="lg" color="success" />
                    </div>
                    <div>
                      <p className="text-title-3 font-sf-pro font-bold text-label">
                        ${formatNumber(priceData.volume24h)}
                      </p>
                      <p className="text-caption-1 text-secondary-label">
                        交易量
                      </p>
                    </div>
                  </div>
                </Card>

                <Card title="市场总值">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-system-purple/20 rounded-xl flex items-center justify-center">
                      <Icon icon={FinanceIcons.pieChart} size="lg" color="secondary" />
                    </div>
                    <div>
                      <p className="text-title-3 font-sf-pro font-bold text-label">
                        ${formatNumber(priceData.marketCap)}
                      </p>
                      <p className="text-caption-1 text-secondary-label">
                        市值
                      </p>
                    </div>
                  </div>
                </Card>
              </div>
            )}
          </motion.div>
        )}

        {/* C2C交易标签 */}
        {activeMainTab === 'c2c' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            {/* 价格信息和货币选择器 */}
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-title-2 font-sf-pro font-bold text-label">C2C交易</h2>
                <div className="flex items-center space-x-2 mt-1">
                  <span className="text-headline font-sf-pro font-semibold text-label">
                    ¥{haoxPrice.toFixed(2)}
                  </span>
                  <div className={`flex items-center space-x-1 ${priceChange24h >= 0 ? 'text-system-green' : 'text-system-red'}`}>
                    <Icon
                      icon={priceChange24h >= 0 ? FinanceIcons.trendUp : FinanceIcons.trendDown}
                      size="xs"
                    />
                    <span className="text-caption-1 font-sf-pro font-medium">
                      {priceChange24h >= 0 ? '+' : ''}{priceChange24h.toFixed(2)}%
                    </span>
                  </div>
                </div>
              </div>

              {/* 货币选择器 */}
              <div className="flex items-center space-x-2">
                <button className="flex items-center space-x-2 px-4 py-2 bg-system-gray-6 rounded-lg hover:bg-system-gray-5 transition-colors">
                  <span className="text-body font-sf-pro font-medium text-label">{selectedCurrency}</span>
                  <ChevronDown className="w-4 h-4 text-secondary-label" />
                </button>
              </div>
            </div>

            {/* 买卖切换标签 */}
            <div className="flex items-center space-x-1 mb-6">
              <button
                onClick={() => setActiveTab('buy')}
                className={cn(
                  'px-6 py-3 rounded-lg font-sf-pro font-medium transition-colors',
                  activeTab === 'buy'
                    ? 'bg-system-green text-white'
                    : 'text-secondary-label hover:text-label hover:bg-system-gray-6'
                )}
              >
                买入
              </button>
              <button
                onClick={() => setActiveTab('sell')}
                className={cn(
                  'px-6 py-3 rounded-lg font-sf-pro font-medium transition-colors',
                  activeTab === 'sell'
                    ? 'bg-system-red text-white'
                    : 'text-secondary-label hover:text-label hover:bg-system-gray-6'
                )}
              >
                卖出
              </button>
            </div>

            {/* 搜索和过滤器 */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          {/* 搜索框 */}
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-secondary-label" />
            <input
              type="text"
              placeholder="搜索商家"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 bg-system-gray-6 border border-system-gray-4 rounded-lg focus:outline-none focus:ring-2 focus:ring-system-blue focus:border-transparent text-label placeholder-secondary-label"
            />
          </div>
          
          {/* 支付方式过滤器 */}
          <div className="flex items-center space-x-2">
            <span className="text-body text-secondary-label whitespace-nowrap">支付方式</span>
            <select
              value={selectedPaymentMethod}
              onChange={(e) => setSelectedPaymentMethod(e.target.value)}
              className="px-4 py-3 bg-system-gray-6 border border-system-gray-4 rounded-lg focus:outline-none focus:ring-2 focus:ring-system-blue text-label"
            >
              {paymentMethods.map((method) => (
                <option key={method.id} value={method.id}>
                  {method.name}
                </option>
              ))}
            </select>
          </div>
          
          {/* 神盾过滤器 */}
          <button className="flex items-center space-x-2 px-4 py-3 bg-system-gray-6 border border-system-gray-4 rounded-lg hover:bg-system-gray-5 transition-colors">
            <Shield className="w-4 h-4 text-secondary-label" />
            <span className="text-body text-secondary-label">神盾</span>
          </button>
          
          {/* 过滤器按钮 */}
          <button className="flex items-center space-x-2 px-4 py-3 bg-system-gray-6 border border-system-gray-4 rounded-lg hover:bg-system-gray-5 transition-colors">
            <Filter className="w-4 h-4 text-secondary-label" />
          </button>
        </div>

        {/* 商家权限检查和订单列表 */}
        {!isAuthenticated ? (
          <Card>
            <div className="text-center py-12">
              <Wallet className="w-16 h-16 text-system-blue mx-auto mb-4" />
              <h3 className="text-title-3 font-sf-pro font-semibold text-label mb-2">
                请先登录
              </h3>
              <p className="text-body text-secondary-label mb-6">
                通过Telegram登录后即可查看和参与C2C交易
              </p>
              <TelegramLogin
                size="lg"
                variant="primary"
                onSuccess={() => {
                  // 登录成功后刷新页面状态
                  window.location.reload();
                }}
                onError={(error) => {
                  console.error('登录失败:', error);
                }}
              />
            </div>
          </Card>
        ) : !isMerchant ? (
          <Card>
            <div className="text-center py-12">
              <Shield className="w-16 h-16 text-system-orange mx-auto mb-4" />
              <h3 className="text-title-3 font-sf-pro font-semibold text-label mb-2">
                成为商家
              </h3>
              <div className="space-y-4 mb-6">
                <p className="text-body text-secondary-label">
                  只有认证商家才能参与C2C交易。成为商家需要冻结{merchantRequirements.requiredDeposit} HAOX代币作为保证金。
                </p>

                {!merchantRequirements.hasEnoughFunds && (
                  <div className="bg-system-orange/10 border border-system-orange/20 rounded-lg p-4">
                    <p className="text-body text-system-orange">
                      您还需要 {formatNumber(merchantRequirements.missingDeposit)} HAOX 才能申请成为商家
                    </p>
                  </div>
                )}

                {merchantRequirements.hasEnoughFunds && canApply && (
                  <div className="bg-system-green/10 border border-system-green/20 rounded-lg p-4">
                    <p className="text-body text-system-green">
                      您已满足商家申请条件，可以立即申请！
                    </p>
                  </div>
                )}
              </div>

              <Button
                onClick={merchantRequirements.canBecomeMerchant ? handleMerchantApplication : () => setShowMerchantApplication(true)}
                disabled={merchantLoading}
                className={merchantRequirements.canBecomeMerchant ? "bg-system-green" : "bg-system-blue"}
              >
                {merchantLoading ? '处理中...' :
                 merchantRequirements.canBecomeMerchant ? '立即申请成为商家' : '了解商家要求'}
              </Button>
            </div>
          </Card>
        ) : (
          <div className="space-y-4">
            {filteredOrders.length === 0 ? (
              <Card>
                <div className="text-center py-12">
                  <Search className="w-16 h-16 text-secondary-label mx-auto mb-4" />
                  <h3 className="text-title-3 font-sf-pro font-semibold text-label mb-2">
                    暂无订单
                  </h3>
                  <p className="text-body text-secondary-label">
                    当前没有符合条件的{activeTab === 'buy' ? '买入' : '卖出'}订单
                  </p>
                </div>
              </Card>
            ) : (
              filteredOrders.map((order) => (
                <Card key={order.id} className="hover:shadow-lg transition-shadow">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-body font-sf-pro font-semibold text-label">
                          {order.merchantName}
                        </h3>
                        {order.verified && (
                          <Shield className="w-4 h-4 text-system-blue" />
                        )}
                        <div className="flex items-center space-x-1">
                          <Star className="w-4 h-4 text-system-yellow fill-current" />
                          <span className="text-caption-1 text-secondary-label">
                            {order.merchantRating}
                          </span>
                        </div>
                        <span className="text-caption-1 text-secondary-label">
                          成单量 {order.merchantOrders} ({order.completionRate}%)
                        </span>
                      </div>
                      
                      <div className="flex items-center space-x-4 text-caption-1 text-secondary-label">
                        <span>限额 {formatNumber(order.minAmount)} - {formatNumber(order.maxAmount)} CNY</span>
                        <span>可用 {formatNumber(order.amount)} USDT</span>
                        <div className="flex items-center space-x-1">
                          <Clock className="w-3 h-3" />
                          <span>{order.avgTime}</span>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2 mt-2">
                        {order.paymentMethods.map((method, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-system-gray-6 rounded text-caption-2 text-secondary-label"
                          >
                            {method}
                          </span>
                        ))}
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className="text-title-2 font-sf-pro font-bold text-label mb-1">
                        ¥ {order.price.toFixed(2)}
                        <span className="text-caption-1 text-secondary-label ml-1">/USDT</span>
                      </div>
                      <Button
                        className={cn(
                          'min-w-[80px]',
                          activeTab === 'buy' ? 'bg-system-green' : 'bg-system-red'
                        )}
                      >
                        {activeTab === 'buy' ? '买入' : '卖出'}
                      </Button>
                    </div>
                  </div>
                </Card>
              ))
            )}
          </div>
            )}
          </motion.div>
        )}
      </div>
    </div>
  );
}
