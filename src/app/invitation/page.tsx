'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Card, Icon } from '@/components/ui';
import {
  UserIcons,
  RewardIcons,
  ActionIcons,
  FinanceIcons,
  SocialIcons
} from '@/config/icons';
import Header from '@/components/layout/Header';
import TelegramLogin from '@/components/auth/TelegramLogin';
import { useTelegramAuth } from '@/hooks/useTelegramAuth';
import { cn } from '@/lib/utils';

export default function InvitationPage() {
  const { user, isAuthenticated, login, logout } = useTelegramAuth();
  const [copySuccess, setCopySuccess] = useState(false);
  const [invitationCode, setInvitationCode] = useState<string | null>(null);
  const [walletAddress, setWalletAddress] = useState<string | null>(null);

  // 当用户登录后自动生成钱包地址
  useEffect(() => {
    if (isAuthenticated && user) {
      // 基于用户ID生成模拟钱包地址
      const mockWalletAddress = `0x${user.id.toString().padStart(8, '0')}${'0'.repeat(32)}`;
      setWalletAddress(mockWalletAddress);

      // 生成邀请码
      const code = Math.random().toString(36).substring(2, 8).toUpperCase();
      setInvitationCode(code);
    }
  }, [isAuthenticated, user]);

  const handleCopyLink = async () => {
    if (invitationCode) {
      try {
        await navigator.clipboard.writeText(`https://sociomint.com/invite/${invitationCode}`);
        setCopySuccess(true);
        setTimeout(() => setCopySuccess(false), 2000);
      } catch (error) {
        console.error('Failed to copy:', error);
      }
    }
  };

  const handleGenerateCode = () => {
    // 生成模拟邀请码
    const code = Math.random().toString(36).substring(2, 8).toUpperCase();
    setInvitationCode(code);
  };

  // 检查Telegram登录状态
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-system-background">
        <Header />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <Card>
            <div className="text-center py-12">
              <Icon icon={SocialIcons.telegram} size="3xl" color="primary" className="mx-auto mb-4" />
              <h3 className="text-title-3 font-sf-pro font-semibold text-label mb-2">
                请先登录 Telegram
              </h3>
              <p className="text-body text-secondary-label mb-6">
                通过 Telegram 登录后即可查看您的邀请中心并获得专属钱包地址
              </p>
              <TelegramLogin
                size="lg"
                variant="primary"
                onSuccess={() => {
                  // 登录成功后刷新页面以显示邀请中心
                  window.location.reload();
                }}
                onError={(error) => {
                  console.error('邀请页面登录失败:', error);
                }}
              />
            </div>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-system-background">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-title-1 font-sf-pro font-bold text-label mb-2">
            邀请中心
          </h1>
          <p className="text-title-3 text-secondary-label">
            邀请好友加入 SocioMint，获得丰厚的 HAOX 代币奖励
          </p>
        </div>

        {/* 邀请码和链接 */}
        <div className="grid lg:grid-cols-3 gap-8 mb-8">
          <div className="lg:col-span-2">
            <Card title="我的邀请码">
              {invitationCode ? (
                <div className="space-y-4">
                  <div className="bg-system-gray-6 rounded-xl p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-caption-1 text-secondary-label mb-1">邀请码</p>
                        <p className="text-title-2 font-mono font-bold text-label">
                          {invitationCode}
                        </p>
                      </div>
                      <Button
                        onClick={handleCopyLink}
                        variant="outline"
                        className="flex items-center space-x-2"
                      >
                        {copySuccess ? (
                          <Icon icon={ActionIcons.checkCircle} size="sm" color="success" />
                        ) : (
                          <Icon icon={ActionIcons.copy} size="sm" />
                        )}
                        <span>{copySuccess ? '已复制' : '复制链接'}</span>
                      </Button>
                    </div>
                  </div>

                  {/* 用户钱包地址 */}
                  <div className="bg-system-gray-6 rounded-xl p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <p className="text-caption-1 text-secondary-label mb-1">您的钱包地址</p>
                        <p className="text-body text-label break-all font-mono">
                          {walletAddress}
                        </p>
                      </div>
                      <Button
                        onClick={() => {
                          if (walletAddress) {
                            navigator.clipboard.writeText(walletAddress);
                          }
                        }}
                        variant="outline"
                        size="sm"
                        className="ml-2"
                      >
                        <Icon icon={ActionIcons.copy} size="sm" />
                      </Button>
                    </div>
                  </div>

                  <div className="bg-system-gray-6 rounded-xl p-4">
                    <p className="text-caption-1 text-secondary-label mb-2">邀请链接</p>
                    <p className="text-body text-label break-all font-mono">
                      https://sociomint.com/invite/{invitationCode}
                    </p>
                  </div>

                  {/* 社交分享按钮 */}
                  <div className="flex flex-wrap gap-3">
                    <Button
                      className="bg-blue-500 hover:bg-blue-600 text-white flex items-center space-x-2"
                    >
                      <Icon icon={SocialIcons.share} size="sm" />
                      <span>分享到 Twitter</span>
                    </Button>
                    <Button
                      className="bg-blue-400 hover:bg-blue-500 text-white flex items-center space-x-2"
                    >
                      <Icon icon={SocialIcons.share} size="sm" />
                      <span>分享到 Telegram</span>
                    </Button>
                    <Button
                      className="bg-green-500 hover:bg-green-600 text-white flex items-center space-x-2"
                    >
                      <Icon icon={SocialIcons.share} size="sm" />
                      <span>分享到 WhatsApp</span>
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Icon icon={UserIcons.users} size="2xl" className="text-secondary-label mx-auto mb-4" />
                  <p className="text-body text-secondary-label mb-4">
                    您还没有邀请码，点击生成专属邀请码
                  </p>
                  <Button
                    onClick={handleGenerateCode}
                    className="bg-system-blue"
                  >
                    生成邀请码
                  </Button>
                </div>
              )}
            </Card>
          </div>

          {/* 邀请统计 */}
          <div className="space-y-6">
            <Card title="邀请统计">
              <div className="space-y-4">
                <div className="text-center p-4 bg-system-blue/10 rounded-xl">
                  <p className="text-title-1 font-sf-pro font-bold text-system-blue">
                    0
                  </p>
                  <p className="text-caption-1 text-secondary-label">成功邀请</p>
                </div>
                
                <div className="grid grid-cols-2 gap-3">
                  <div className="text-center p-3 bg-system-gray-6 rounded-lg">
                    <p className="text-headline font-sf-pro font-semibold text-label">
                      0
                    </p>
                    <p className="text-caption-2 text-secondary-label">总邀请</p>
                  </div>
                  <div className="text-center p-3 bg-system-gray-6 rounded-lg">
                    <p className="text-headline font-sf-pro font-semibold text-system-green">
                      0
                    </p>
                    <p className="text-caption-2 text-secondary-label">总奖励</p>
                  </div>
                </div>
              </div>
            </Card>

            {/* 下一个里程碑 */}
            <Card title="下一个里程碑">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-body text-secondary-label">
                    邀请 5 人
                  </span>
                  <span className="text-body font-sf-pro font-semibold text-system-green">
                    +1,000 HAOX
                  </span>
                </div>
                
                <div className="w-full bg-system-gray-5 rounded-full h-2">
                  <div
                    className="h-2 bg-gradient-to-r from-system-blue to-system-purple rounded-full transition-all duration-300"
                    style={{ width: '0%' }}
                  />
                </div>
                
                <p className="text-caption-1 text-secondary-label">
                  还需邀请 5 人
                </p>
              </div>
            </Card>
          </div>
        </div>

        {/* 奖励记录和邀请历史 */}
        <div className="grid lg:grid-cols-2 gap-8">
          {/* 奖励记录 */}
          <Card title="奖励记录">
            <div className="text-center py-8">
              <Icon icon={RewardIcons.gift} size="2xl" className="text-secondary-label mx-auto mb-4" />
              <p className="text-body text-secondary-label">
                暂无奖励记录，邀请好友即可获得奖励
              </p>
            </div>
          </Card>

          {/* 邀请历史 */}
          <Card title="邀请历史">
            <div className="text-center py-8">
              <Icon icon={UserIcons.users} size="2xl" className="text-secondary-label mx-auto mb-4" />
              <p className="text-body text-secondary-label">
                暂无邀请记录，分享您的邀请链接给好友
              </p>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}