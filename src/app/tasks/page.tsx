'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, Button, Icon } from '@/components/ui';
import {
  RewardIcons,
  UserIcons,
  FinanceIcons
} from '@/config/icons';
import Header from '@/components/layout/Header';
import SocialConnect from '@/components/social/SocialConnect';
import SocialTasks from '@/components/social/SocialTasks';
import { useWallet } from '@/hooks/useWallet';
import { useSocialTasks } from '@/hooks/useSocialTasks';
import { useSocialAccounts } from '@/hooks/useSocialAccounts';
import { formatNumber } from '@/lib/utils';

export default function TasksPage() {
  const { address } = useWallet();
  const { getCompletionStats } = useSocialTasks(address);
  const { getConnectionStatus } = useSocialAccounts(address);
  
  const [activeTab, setActiveTab] = useState<'overview' | 'tasks' | 'rewards'>('overview');

  const stats = getCompletionStats();
  const connectionStatus = getConnectionStatus();

  const achievements = [
    {
      id: 'first_task',
      title: '初出茅庐',
      description: '完成第一个社交任务',
      icon: '🎯',
      progress: stats.completedTasks > 0 ? 100 : 0,
      maxProgress: 1,
      reward: 10,
      unlocked: stats.completedTasks > 0,
    },
    {
      id: 'social_butterfly',
      title: '社交达人',
      description: '连接所有社交平台',
      icon: '🦋',
      progress: connectionStatus.total,
      maxProgress: 3,
      reward: 100,
      unlocked: connectionStatus.total === 3,
    },
    {
      id: 'task_master',
      title: '任务大师',
      description: '完成10个社交任务',
      icon: '👑',
      progress: Math.min(stats.completedTasks, 10),
      maxProgress: 10,
      reward: 200,
      unlocked: stats.completedTasks >= 10,
    },
    {
      id: 'reward_hunter',
      title: '奖励猎人',
      description: '累计获得1000 HAOX奖励',
      icon: '💎',
      progress: Math.min(stats.totalRewards, 1000),
      maxProgress: 1000,
      reward: 500,
      unlocked: stats.totalRewards >= 1000,
    },
  ];

  const weeklyGoals = [
    {
      title: '完成5个任务',
      progress: Math.min(stats.completedTasks, 5),
      maxProgress: 5,
      reward: 50,
      deadline: '本周日',
    },
    {
      title: '连接新平台',
      progress: connectionStatus.total,
      maxProgress: 3,
      reward: 75,
      deadline: '本周日',
    },
  ];

  const renderOverview = () => (
    <div className="space-y-8">
      {/* Hero Stats */}
      <Card>
        <div className="text-center py-8">
          <div className="w-20 h-20 bg-gradient-to-br from-system-orange to-system-red rounded-2xl flex items-center justify-center mx-auto mb-6">
            <Icon icon={RewardIcons.award} size="2xl" className="text-white" />
          </div>
          <h1 className="text-title-1 font-sf-pro font-bold text-label mb-4">
            社交任务中心
          </h1>
          <p className="text-title-3 text-secondary-label mb-8 max-w-2xl mx-auto">
            完成社交平台任务，赚取HAOX代币奖励
          </p>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center">
              <p className="text-title-2 font-sf-pro font-bold text-system-orange">
                {formatNumber(stats.totalRewards)}
              </p>
              <p className="text-caption-1 text-secondary-label">总奖励 (HAOX)</p>
            </div>
            <div className="text-center">
              <p className="text-title-2 font-sf-pro font-bold text-system-blue">
                {stats.completedTasks}
              </p>
              <p className="text-caption-1 text-secondary-label">已完成任务</p>
            </div>
            <div className="text-center">
              <p className="text-title-2 font-sf-pro font-bold text-system-green">
                {connectionStatus.total}
              </p>
              <p className="text-caption-1 text-secondary-label">已连接平台</p>
            </div>
            <div className="text-center">
              <p className="text-title-2 font-sf-pro font-bold text-system-purple">
                {Math.round(stats.completionRate)}%
              </p>
              <p className="text-caption-1 text-secondary-label">完成率</p>
            </div>
          </div>
        </div>
      </Card>

      {/* Weekly Goals */}
      <Card title="本周目标">
        <div className="space-y-4">
          {weeklyGoals.map((goal, index) => (
            <div key={index} className="p-4 bg-system-gray-6 rounded-xl">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-body font-sf-pro font-semibold text-label">
                  {goal.title}
                </h4>
                <div className="flex items-center space-x-2">
                  <Icon icon={RewardIcons.gift} size="sm" color="warning" />
                  <span className="text-body font-sf-pro font-medium text-system-orange">
                    {goal.reward} HAOX
                  </span>
                </div>
              </div>
              
              <div className="flex items-center justify-between mb-2">
                <span className="text-caption-1 text-secondary-label">
                  进度: {goal.progress}/{goal.maxProgress}
                </span>
                <span className="text-caption-1 text-secondary-label">
                  截止: {goal.deadline}
                </span>
              </div>
              
              <div className="w-full bg-system-gray-5 rounded-full h-2">
                <div 
                  className="bg-system-blue h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(goal.progress / goal.maxProgress) * 100}%` }}
                />
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* Achievements */}
      <Card title="成就系统">
        <div className="grid md:grid-cols-2 gap-4">
          {achievements.map((achievement, index) => (
            <motion.div
              key={achievement.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className={`p-4 rounded-xl border-2 transition-all duration-200 ${
                achievement.unlocked 
                  ? 'border-system-green bg-system-green/5' 
                  : 'border-system-gray-4'
              }`}
            >
              <div className="flex items-start space-x-4">
                <div className={`w-12 h-12 rounded-xl flex items-center justify-center text-2xl ${
                  achievement.unlocked ? 'bg-system-green/10' : 'bg-system-gray-6'
                }`}>
                  {achievement.icon}
                </div>
                
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <h4 className="text-body font-sf-pro font-semibold text-label">
                      {achievement.title}
                    </h4>
                    {achievement.unlocked && (
                      <Icon icon={RewardIcons.star} size="sm" color="warning" />
                    )}
                  </div>
                  
                  <p className="text-caption-1 text-secondary-label mb-2">
                    {achievement.description}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex-1 mr-4">
                      <div className="w-full bg-system-gray-5 rounded-full h-1.5">
                        <div 
                          className={`h-1.5 rounded-full transition-all duration-300 ${
                            achievement.unlocked ? 'bg-system-green' : 'bg-system-blue'
                          }`}
                          style={{ width: `${(achievement.progress / achievement.maxProgress) * 100}%` }}
                        />
                      </div>
                      <p className="text-xs text-secondary-label mt-1">
                        {achievement.progress}/{achievement.maxProgress}
                      </p>
                    </div>
                    
                    <div className="flex items-center space-x-1">
                      <Icon icon={RewardIcons.award} size="xs" color="warning" />
                      <span className="text-caption-1 font-sf-pro font-medium text-system-orange">
                        {achievement.reward}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </Card>

      {/* Quick Actions */}
      <div className="grid md:grid-cols-2 gap-6">
        <Card>
          <div className="text-center py-6">
            <Icon icon={UserIcons.users} size="2xl" color="primary" className="mx-auto mb-4" />
            <h3 className="text-headline font-sf-pro font-semibold text-label mb-2">
              连接社交账户
            </h3>
            <p className="text-body text-secondary-label mb-4">
              连接更多社交平台以解锁更多任务
            </p>
            <Button 
              onClick={() => setActiveTab('tasks')}
              size="sm"
            >
              立即连接
            </Button>
          </div>
        </Card>

        <Card>
          <div className="text-center py-6">
            <Icon icon={RewardIcons.target} size="2xl" color="success" className="mx-auto mb-4" />
            <h3 className="text-headline font-sf-pro font-semibold text-label mb-2">
              开始任务
            </h3>
            <p className="text-body text-secondary-label mb-4">
              完成简单的社交任务获得奖励
            </p>
            <Button 
              onClick={() => setActiveTab('tasks')}
              size="sm"
            >
              查看任务
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );

  const renderRewards = () => (
    <Card title="奖励历史">
      <div className="text-center py-8">
        <Icon icon={RewardIcons.gift} size="2xl" className="text-system-gray mx-auto mb-3" />
        <p className="text-body text-secondary-label">
          奖励历史功能开发中...
        </p>
      </div>
    </Card>
  );

  const tabs = [
    { id: 'overview', label: '概览', icon: FinanceIcons.trendUp },
    { id: 'tasks', label: '任务', icon: RewardIcons.target },
    { id: 'rewards', label: '奖励', icon: RewardIcons.gift },
  ];

  return (
    <div className="min-h-screen bg-system-background">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Tab Navigation */}
        <div className="flex justify-center mb-8">
          <div className="flex bg-system-gray-6 rounded-xl p-1">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center space-x-2 px-6 py-3 rounded-lg text-sm font-sf-pro font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-system-blue text-white'
                    : 'text-secondary-label hover:text-label'
                }`}
              >
                <Icon icon={tab.icon} size="sm" />
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Tab Content */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {activeTab === 'overview' && renderOverview()}
          {activeTab === 'tasks' && (
            <div className="grid lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <SocialTasks userId={address} />
              </div>
              <div>
                <SocialConnect userId={address} />
              </div>
            </div>
          )}
          {activeTab === 'rewards' && renderRewards()}
        </motion.div>
      </div>
    </div>
  );
}
