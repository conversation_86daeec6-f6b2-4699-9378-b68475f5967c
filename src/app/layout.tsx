import type { Metadata, Viewport } from "next";
import "./globals.css";
import { ToastProvider } from "@/components/ui";
import ErrorBoundary from "@/components/ErrorBoundary";

export const metadata: Metadata = {
  title: "SocioMint - 社交化加密货币交易平台",
  description: "基于社交平台集成的HAOX代币交易平台，支持商家认证和社交任务系统",
  keywords: "加密货币, HAOX, 代币交易, 社交平台, 区块链",
  authors: [{ name: "SocioMint Team" }],
  openGraph: {
    title: "SocioMint - 社交化加密货币交易平台",
    description: "基于社交平台集成的HAOX代币交易平台",
    type: "website",
    locale: "zh_CN",
  },
  twitter: {
    card: "summary_large_image",
    title: "SocioMint - 社交化加密货币交易平台",
    description: "基于社交平台集成的HAOX代币交易平台",
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  themeColor: "#007AFF",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <body className="font-sf-pro antialiased">
        <ErrorBoundary>
          <ToastProvider>
            {children}
          </ToastProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}
