'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Shield, 
  CheckCircle, 
  Clock, 
  XCircle, 
  Star,
  TrendingUp,
  Users,
  Award,
  FileText,
  Phone,
  Mail
} from 'lucide-react';
import { Button, Card, Input } from '@/components/ui';
import Header from '@/components/layout/Header';
import { useWallet } from '@/hooks/useWallet';

export default function MerchantsPage() {
  const { isConnected } = useWallet();
  const [activeTab, setActiveTab] = useState('overview');
  const [applicationForm, setApplicationForm] = useState({
    businessName: '',
    businessType: '',
    businessLicense: '',
    contactPerson: '',
    contactPhone: '',
    contactEmail: '',
    description: '',
  });

  // Mock merchant data
  const merchants = [
    {
      id: '1',
      name: 'CryptoTech Solutions',
      type: '科技公司',
      rating: 4.8,
      totalTrades: 1250,
      volume: 5000000,
      joinDate: '2023-06-15',
      status: 'approved',
      avatar: '🏢',
    },
    {
      id: '2',
      name: 'Digital Assets Pro',
      type: '投资机构',
      rating: 4.9,
      totalTrades: 2100,
      volume: 8500000,
      joinDate: '2023-03-20',
      status: 'approved',
      avatar: '💼',
    },
    {
      id: '3',
      name: 'Blockchain Ventures',
      type: '风险投资',
      rating: 4.7,
      totalTrades: 890,
      volume: 3200000,
      joinDate: '2023-08-10',
      status: 'approved',
      avatar: '🚀',
    },
  ];

  const benefits = [
    {
      icon: TrendingUp,
      title: '更低交易费率',
      description: '享受0.1%的超低交易手续费，比普通用户节省50%',
      color: 'text-system-green',
    },
    {
      icon: Users,
      title: '专属客户经理',
      description: '配备专业客户经理，提供一对一服务支持',
      color: 'text-system-blue',
    },
    {
      icon: Award,
      title: '优先交易权限',
      description: '在市场波动时享受优先交易执行权限',
      color: 'text-system-purple',
    },
    {
      icon: Shield,
      title: '信誉保障',
      description: '获得平台信誉认证标识，提升交易信任度',
      color: 'text-system-orange',
    },
  ];

  const requirements = [
    '具有合法的营业执照或相关资质证明',
    '在相关行业有至少1年的经营经验',
    '具备良好的信用记录和财务状况',
    '承诺遵守平台的交易规则和服务条款',
    '提供真实有效的联系方式和身份信息',
  ];

  const handleInputChange = (field: string, value: string) => {
    setApplicationForm(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmitApplication = () => {
    if (!isConnected) {
      // Show connect wallet modal
      return;
    }
    
    // Handle application submission
    console.log('Merchant application:', applicationForm);
  };

  const renderOverview = () => (
    <div className="space-y-8">
      {/* Hero Section */}
      <Card>
        <div className="text-center py-8">
          <div className="w-20 h-20 bg-gradient-to-br from-system-blue to-system-purple rounded-2xl flex items-center justify-center mx-auto mb-6">
            <Shield className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-title-1 font-sf-pro font-bold text-label mb-4">
            成为认证商家
          </h1>
          <p className="text-title-3 text-secondary-label mb-8 max-w-2xl mx-auto">
            加入SocioMint认证商家计划，享受专业级交易服务和更多特权
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg"
              onClick={() => setActiveTab('apply')}
            >
              立即申请认证
            </Button>
            <Button 
              variant="outline" 
              size="lg"
              onClick={() => setActiveTab('merchants')}
            >
              查看认证商家
            </Button>
          </div>
        </div>
      </Card>

      {/* Benefits */}
      <div>
        <h2 className="text-title-2 font-sf-pro font-bold text-label mb-6 text-center">
          认证商家特权
        </h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {benefits.map((benefit, index) => (
            <motion.div
              key={benefit.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 * index }}
              viewport={{ once: true }}
            >
              <Card className="text-center h-full">
                <div className="w-16 h-16 bg-system-gray-6 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <benefit.icon className={`w-8 h-8 ${benefit.color}`} />
                </div>
                <h3 className="text-headline font-sf-pro font-semibold text-label mb-3">
                  {benefit.title}
                </h3>
                <p className="text-body text-secondary-label">
                  {benefit.description}
                </p>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Requirements */}
      <Card title="申请要求">
        <div className="space-y-4">
          {requirements.map((requirement, index) => (
            <div key={index} className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-system-green mt-0.5 flex-shrink-0" />
              <p className="text-body text-secondary-label">
                {requirement}
              </p>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );

  const renderMerchants = () => (
    <div className="space-y-6">
      <Card>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-title-2 font-sf-pro font-bold text-label">
            认证商家列表
          </h2>
          <div className="text-right">
            <p className="text-caption-1 text-secondary-label">共 {merchants.length} 家认证商家</p>
          </div>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {merchants.map((merchant) => (
            <Card key={merchant.id} className="hover:shadow-apple-lg transition-shadow">
              <div className="text-center">
                <div className="text-4xl mb-4">{merchant.avatar}</div>
                <h3 className="text-headline font-sf-pro font-semibold text-label mb-2">
                  {merchant.name}
                </h3>
                <p className="text-body text-secondary-label mb-4">
                  {merchant.type}
                </p>

                <div className="space-y-3">
                  <div className="flex items-center justify-center space-x-1">
                    <Star className="w-4 h-4 text-system-yellow fill-current" />
                    <span className="text-body font-sf-pro font-medium text-label">
                      {merchant.rating}
                    </span>
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-center">
                    <div>
                      <p className="text-caption-1 text-secondary-label">交易次数</p>
                      <p className="text-body font-sf-pro font-semibold text-label">
                        {merchant.totalTrades.toLocaleString()}
                      </p>
                    </div>
                    <div>
                      <p className="text-caption-1 text-secondary-label">交易量</p>
                      <p className="text-body font-sf-pro font-semibold text-label">
                        ¥{(merchant.volume / 1000000).toFixed(1)}M
                      </p>
                    </div>
                  </div>

                  <div className="pt-3 border-t border-system-gray-4">
                    <p className="text-caption-1 text-secondary-label">
                      加入时间: {new Date(merchant.joinDate).toLocaleDateString('zh-CN')}
                    </p>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </Card>
    </div>
  );

  const renderApplication = () => (
    <Card title="商家认证申请">
      <div className="space-y-6">
        <div className="bg-system-blue/5 border border-system-blue/20 rounded-xl p-4">
          <div className="flex items-start space-x-3">
            <Shield className="w-5 h-5 text-system-blue mt-0.5" />
            <div>
              <h4 className="text-body font-sf-pro font-semibold text-label mb-1">
                申请须知
              </h4>
              <p className="text-caption-1 text-secondary-label">
                请确保提供的信息真实有效，虚假信息将导致申请被拒绝。审核周期通常为3-5个工作日。
              </p>
            </div>
          </div>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          <Input
            label="企业名称"
            value={applicationForm.businessName}
            onChange={(value) => handleInputChange('businessName', value)}
            placeholder="请输入企业全称"
            required
          />

          <Input
            label="企业类型"
            value={applicationForm.businessType}
            onChange={(value) => handleInputChange('businessType', value)}
            placeholder="如：科技公司、投资机构等"
            required
          />

          <Input
            label="营业执照号"
            value={applicationForm.businessLicense}
            onChange={(value) => handleInputChange('businessLicense', value)}
            placeholder="请输入营业执照注册号"
            required
          />

          <Input
            label="联系人姓名"
            value={applicationForm.contactPerson}
            onChange={(value) => handleInputChange('contactPerson', value)}
            placeholder="请输入负责人姓名"
            required
          />

          <Input
            label="联系电话"
            type="tel"
            value={applicationForm.contactPhone}
            onChange={(value) => handleInputChange('contactPhone', value)}
            placeholder="请输入联系电话"
            required
          />

          <Input
            label="联系邮箱"
            type="email"
            value={applicationForm.contactEmail}
            onChange={(value) => handleInputChange('contactEmail', value)}
            placeholder="请输入联系邮箱"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium font-sf-pro mb-2 text-label">
            企业描述 <span className="text-system-red">*</span>
          </label>
          <textarea
            value={applicationForm.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="请详细描述您的企业背景、主营业务、团队规模等信息"
            rows={4}
            className="w-full px-4 py-3 font-sf-pro text-body bg-system-background border-2 border-system-gray-4 rounded-xl transition-all duration-200 focus:outline-none focus:border-system-blue placeholder:text-system-gray resize-none"
          />
        </div>

        <div className="flex flex-col sm:flex-row gap-4">
          <Button
            onClick={handleSubmitApplication}
            disabled={!isConnected}
            className="flex-1"
          >
            {!isConnected ? '请先连接钱包' : '提交申请'}
          </Button>
          <Button
            variant="outline"
            onClick={() => setActiveTab('overview')}
            className="flex-1"
          >
            返回概览
          </Button>
        </div>
      </div>
    </Card>
  );

  const tabs = [
    { id: 'overview', label: '概览' },
    { id: 'merchants', label: '认证商家' },
    { id: 'apply', label: '申请认证' },
  ];

  return (
    <div className="min-h-screen bg-system-background">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Tab Navigation */}
        <div className="flex justify-center mb-8">
          <div className="flex bg-system-gray-6 rounded-xl p-1">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`px-6 py-2 rounded-lg text-sm font-sf-pro font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-system-blue text-white'
                    : 'text-secondary-label hover:text-label'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Tab Content */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {activeTab === 'overview' && renderOverview()}
          {activeTab === 'merchants' && renderMerchants()}
          {activeTab === 'apply' && renderApplication()}
        </motion.div>
      </div>
    </div>
  );
}
