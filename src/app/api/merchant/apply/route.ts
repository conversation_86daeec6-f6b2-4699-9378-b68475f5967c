import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    const {
      userId,
      businessName,
      businessType,
      businessDescription,
      contactEmail,
      contactPhone,
      businessLicense,
      businessLicenseUrl,
      identityDocumentUrl,
      bankAccountInfo,
      socialMediaLinks,
      tradingExperience,
      expectedVolume
    } = await request.json();

    if (!userId || !businessName || !businessType || !contactEmail) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // 1. 检查用户是否存在
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, is_merchant, merchant_applied_at')
      .eq('id', userId)
      .single();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // 2. 检查用户是否已经是商家
    if (user.is_merchant) {
      return NextResponse.json(
        { error: 'User is already a merchant' },
        { status: 400 }
      );
    }

    // 3. 检查是否已有待处理的申请
    const { data: existingApplication, error: appError } = await supabase
      .from('merchant_applications')
      .select('id, application_status')
      .eq('user_id', userId)
      .in('application_status', ['pending', 'under_review'])
      .single();

    if (existingApplication) {
      return NextResponse.json(
        { error: 'You already have a pending application' },
        { status: 400 }
      );
    }

    // 4. 创建商家申请
    const { data: application, error: createError } = await supabase
      .from('merchant_applications')
      .insert({
        user_id: userId,
        business_name: businessName,
        business_type: businessType,
        business_description: businessDescription,
        contact_email: contactEmail,
        contact_phone: contactPhone,
        business_license: businessLicense,
        business_license_url: businessLicenseUrl,
        identity_document_url: identityDocumentUrl,
        bank_account_info: bankAccountInfo,
        social_media_links: socialMediaLinks,
        trading_experience: tradingExperience,
        expected_volume: expectedVolume,
        application_status: 'pending'
      })
      .select()
      .single();

    if (createError) {
      console.error('Error creating merchant application:', createError);
      return NextResponse.json(
        { error: 'Failed to create application' },
        { status: 500 }
      );
    }

    // 5. 更新用户表的申请时间
    const { error: updateError } = await supabase
      .from('users')
      .update({ merchant_applied_at: new Date().toISOString() })
      .eq('id', userId);

    if (updateError) {
      console.error('Error updating user application time:', updateError);
    }

    return NextResponse.json({
      success: true,
      application: {
        id: application.id,
        status: application.application_status,
        created_at: application.created_at
      },
      message: 'Merchant application submitted successfully'
    });

  } catch (error) {
    console.error('Merchant application error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // 获取用户的商家申请状态
    const { data: application, error } = await supabase
      .from('merchant_applications')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw error;
    }

    // 检查用户是否已经是商家
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('is_merchant, merchant_id')
      .eq('id', userId)
      .single();

    if (userError) {
      throw userError;
    }

    let merchantInfo = null;
    if (user.is_merchant && user.merchant_id) {
      const { data: merchant, error: merchantError } = await supabase
        .from('merchants')
        .select('*')
        .eq('id', user.merchant_id)
        .single();

      if (!merchantError) {
        merchantInfo = merchant;
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        is_merchant: user.is_merchant,
        merchant_info: merchantInfo,
        latest_application: application || null,
        can_apply: !user.is_merchant && (!application || ['rejected', 'suspended'].includes(application.application_status))
      }
    });

  } catch (error) {
    console.error('Get merchant application error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
