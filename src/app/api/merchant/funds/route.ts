import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

// 冻结商家资金
export async function POST(request: NextRequest) {
  try {
    const {
      merchantId,
      freezeType,
      amount,
      reason,
      expiresAt,
      frozenBy
    } = await request.json();

    if (!merchantId || !freezeType || !amount || !reason) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // 1. 检查商家是否存在
    const { data: merchant, error: merchantError } = await supabase
      .from('merchants')
      .select('id, available_amount, frozen_amount')
      .eq('id', merchantId)
      .single();

    if (merchantError || !merchant) {
      return NextResponse.json(
        { error: 'Merchant not found' },
        { status: 404 }
      );
    }

    // 2. 检查可用资金是否足够
    if (merchant.available_amount < amount) {
      return NextResponse.json(
        { error: 'Insufficient available funds' },
        { status: 400 }
      );
    }

    // 3. 创建冻结记录
    const { data: frozenRecord, error: freezeError } = await supabase
      .from('merchant_frozen_funds')
      .insert({
        merchant_id: merchantId,
        freeze_type: freezeType,
        amount: amount,
        reason: reason,
        expires_at: expiresAt,
        frozen_by: frozenBy,
        status: 'frozen'
      })
      .select()
      .single();

    if (freezeError) {
      console.error('Error creating frozen record:', freezeError);
      return NextResponse.json(
        { error: 'Failed to freeze funds' },
        { status: 500 }
      );
    }

    // 4. 更新商家资金状态
    const { error: updateError } = await supabase
      .from('merchants')
      .update({
        available_amount: merchant.available_amount - amount,
        frozen_amount: merchant.frozen_amount + amount,
        updated_at: new Date().toISOString()
      })
      .eq('id', merchantId);

    if (updateError) {
      console.error('Error updating merchant funds:', updateError);
      return NextResponse.json(
        { error: 'Failed to update merchant funds' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      frozen_record: frozenRecord,
      message: 'Funds frozen successfully'
    });

  } catch (error) {
    console.error('Freeze funds error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// 获取商家资金状态
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const merchantId = searchParams.get('merchantId');
    const userId = searchParams.get('userId');

    let merchant = null;

    if (merchantId) {
      const { data, error } = await supabase
        .from('merchants')
        .select('*')
        .eq('id', merchantId)
        .single();

      if (error) {
        throw error;
      }
      merchant = data;
    } else if (userId) {
      const { data, error } = await supabase
        .from('merchants')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }
      merchant = data;
    } else {
      return NextResponse.json(
        { error: 'Merchant ID or User ID is required' },
        { status: 400 }
      );
    }

    if (!merchant) {
      return NextResponse.json(
        { error: 'Merchant not found' },
        { status: 404 }
      );
    }

    // 获取冻结资金记录
    const { data: frozenFunds, error: frozenError } = await supabase
      .from('merchant_frozen_funds')
      .select('*')
      .eq('merchant_id', merchant.id)
      .eq('status', 'frozen')
      .order('frozen_at', { ascending: false });

    if (frozenError) {
      console.error('Error fetching frozen funds:', frozenError);
    }

    // 获取违规记录
    const { data: violations, error: violationError } = await supabase
      .from('merchant_violations')
      .select('*')
      .eq('merchant_id', merchant.id)
      .order('created_at', { ascending: false })
      .limit(10);

    if (violationError) {
      console.error('Error fetching violations:', violationError);
    }

    // 获取商家等级配置
    const { data: levelConfig, error: levelError } = await supabase
      .from('merchant_level_configs')
      .select('*')
      .eq('level_name', merchant.merchant_level)
      .single();

    if (levelError) {
      console.error('Error fetching level config:', levelError);
    }

    return NextResponse.json({
      success: true,
      data: {
        merchant: {
          ...merchant,
          level_config: levelConfig
        },
        frozen_funds: frozenFunds || [],
        recent_violations: violations || [],
        funds_summary: {
          total_balance: merchant.available_amount + merchant.frozen_amount,
          available_amount: merchant.available_amount,
          frozen_amount: merchant.frozen_amount,
          required_deposit: levelConfig?.min_deposit || 5000
        }
      }
    });

  } catch (error) {
    console.error('Get merchant funds error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// 释放冻结资金
export async function PATCH(request: NextRequest) {
  try {
    const {
      frozenRecordId,
      releasedBy,
      notes
    } = await request.json();

    if (!frozenRecordId) {
      return NextResponse.json(
        { error: 'Frozen record ID is required' },
        { status: 400 }
      );
    }

    // 1. 获取冻结记录
    const { data: frozenRecord, error: recordError } = await supabase
      .from('merchant_frozen_funds')
      .select('*')
      .eq('id', frozenRecordId)
      .eq('status', 'frozen')
      .single();

    if (recordError || !frozenRecord) {
      return NextResponse.json(
        { error: 'Frozen record not found' },
        { status: 404 }
      );
    }

    // 2. 更新冻结记录状态
    const { error: updateRecordError } = await supabase
      .from('merchant_frozen_funds')
      .update({
        status: 'released',
        released_by: releasedBy,
        released_at: new Date().toISOString(),
        notes: notes
      })
      .eq('id', frozenRecordId);

    if (updateRecordError) {
      console.error('Error updating frozen record:', updateRecordError);
      return NextResponse.json(
        { error: 'Failed to release funds' },
        { status: 500 }
      );
    }

    // 3. 更新商家资金状态
    const { data: merchant, error: merchantError } = await supabase
      .from('merchants')
      .select('available_amount, frozen_amount')
      .eq('id', frozenRecord.merchant_id)
      .single();

    if (merchantError) {
      console.error('Error fetching merchant:', merchantError);
      return NextResponse.json(
        { error: 'Failed to update merchant funds' },
        { status: 500 }
      );
    }

    const { error: updateMerchantError } = await supabase
      .from('merchants')
      .update({
        available_amount: merchant.available_amount + frozenRecord.amount,
        frozen_amount: merchant.frozen_amount - frozenRecord.amount,
        updated_at: new Date().toISOString()
      })
      .eq('id', frozenRecord.merchant_id);

    if (updateMerchantError) {
      console.error('Error updating merchant funds:', updateMerchantError);
      return NextResponse.json(
        { error: 'Failed to update merchant funds' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Funds released successfully'
    });

  } catch (error) {
    console.error('Release funds error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
