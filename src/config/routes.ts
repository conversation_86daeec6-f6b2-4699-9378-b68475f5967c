// 路由配置和代码分割设置

import { lazy } from 'react';
import { createLazyPage } from '@/components/common/PageLoader';

// 页面组件的动态导入配置
export const lazyPages = {
  // 主要页面
  Home: createLazyPage(() => import('@/app/page')),
  Trade: createLazyPage(() => import('@/app/trade/page')),
  Tasks: createLazyPage(() => import('@/app/tasks/page')),
  Invitation: createLazyPage(() => import('@/app/invitation/page')),
  Leaderboard: createLazyPage(() => import('@/app/leaderboard/page')),
  Presale: createLazyPage(() => import('@/app/presale/page')),
  
  // 用户相关页面
  Profile: createLazyPage(() => import('@/app/profile/page')),
  Settings: createLazyPage(() => import('@/app/settings/page')),
  
  // 管理页面
  Admin: createLazyPage(() => import('@/app/admin/page')),
  
  // 错误页面
  NotFound: createLazyPage(() => import('@/app/not-found')),
};

// 组件级别的懒加载配置
export const lazyComponents = {
  // 社交组件
  SocialConnect: lazy(() => import('@/components/social/SocialConnect')),
  SocialTasks: lazy(() => import('@/components/social/SocialTasks')),
  SocialBinding: lazy(() => import('@/components/social/SocialBinding')),
  
  // 钱包组件
  WalletConnect: lazy(() => import('@/components/wallet/WalletConnect')),
  WalletInfo: lazy(() => import('@/components/wallet/WalletInfo')),
  
  // Telegram 组件
  TelegramChannelJoin: lazy(() => import('@/components/telegram/TelegramChannelJoin')),
  TelegramVerification: lazy(() => import('@/components/telegram/TelegramVerification')),
  
  // 预售组件
  PresaleSection: lazy(() => import('@/components/presale/PresaleSection')),
  
  // 图表组件
  Charts: lazy(() => import('@/components/charts')),
  
  // 高级功能组件
  Analytics: lazy(() => import('@/components/analytics')),
  Notifications: lazy(() => import('@/components/notifications')),
};

// 路由元数据配置
export interface RouteMetadata {
  title: string;
  description: string;
  keywords?: string[];
  preload?: boolean; // 是否预加载
  priority?: 'high' | 'medium' | 'low'; // 加载优先级
  requiresAuth?: boolean; // 是否需要认证
  requiresWallet?: boolean; // 是否需要钱包连接
}

export const routeMetadata: Record<string, RouteMetadata> = {
  '/': {
    title: 'SocioMint - 社交挖矿平台',
    description: '通过社交任务赚取 HAOX 代币，参与去中心化社交经济',
    keywords: ['社交挖矿', 'HAOX', '代币', '区块链', '社交任务'],
    preload: true,
    priority: 'high',
  },
  '/trade': {
    title: 'HAOX 交易中心 - SocioMint',
    description: '查看 HAOX 代币实时价格，与认证商家进行安全的 C2C 交易',
    keywords: ['HAOX交易', 'C2C交易', '代币价格', '商家交易'],
    preload: true,
    priority: 'high',
    requiresWallet: true,
  },
  '/tasks': {
    title: '社交任务 - SocioMint',
    description: '完成社交任务赚取 HAOX 代币奖励',
    keywords: ['社交任务', 'HAOX奖励', '任务完成', '代币赚取'],
    preload: true,
    priority: 'high',
    requiresAuth: true,
  },
  '/invitation': {
    title: '邀请好友 - SocioMint',
    description: '邀请好友加入 SocioMint，获得丰厚的邀请奖励',
    keywords: ['邀请奖励', '推荐好友', 'HAOX奖励', '邀请链接'],
    preload: false,
    priority: 'medium',
    requiresAuth: true,
    requiresWallet: true,
  },
  '/leaderboard': {
    title: '排行榜 - SocioMint',
    description: '查看社区排行榜，了解顶级用户的表现',
    keywords: ['排行榜', '社区排名', '用户排名', '奖励排名'],
    preload: false,
    priority: 'medium',
  },
  '/presale': {
    title: 'HAOX 代币预售 - SocioMint',
    description: '参与 HAOX 代币预售，享受早期投资者专属价格优势',
    keywords: ['HAOX预售', '代币预售', '早期投资', '预售价格'],
    preload: false,
    priority: 'medium',
    requiresWallet: true,
  },
  '/profile': {
    title: '个人资料 - SocioMint',
    description: '管理您的个人资料和账户设置',
    keywords: ['个人资料', '账户设置', '用户信息'],
    preload: false,
    priority: 'low',
    requiresAuth: true,
  },
  '/settings': {
    title: '设置 - SocioMint',
    description: '配置您的应用设置和偏好',
    keywords: ['设置', '配置', '偏好设置'],
    preload: false,
    priority: 'low',
    requiresAuth: true,
  },
};

// 预加载策略配置
export const preloadStrategy = {
  // 立即预加载的页面（首页访问时）
  immediate: ['/trade', '/tasks'],
  
  // 用户交互时预加载的页面
  onUserInteraction: ['/invitation', '/leaderboard'],
  
  // 空闲时预加载的页面
  onIdle: ['/presale', '/profile', '/settings'],
  
  // 基于路由预加载的页面
  onRouteChange: {
    '/': ['/trade', '/tasks'],
    '/trade': ['/invitation', '/leaderboard'],
    '/tasks': ['/invitation', '/profile'],
    '/invitation': ['/leaderboard'],
  },
};

// 获取路由的预加载配置
export function getPreloadConfig(currentRoute: string) {
  const metadata = routeMetadata[currentRoute];
  const relatedRoutes = preloadStrategy.onRouteChange[currentRoute] || [];
  
  return {
    shouldPreload: metadata?.preload || false,
    priority: metadata?.priority || 'low',
    relatedRoutes,
    requiresAuth: metadata?.requiresAuth || false,
    requiresWallet: metadata?.requiresWallet || false,
  };
}

// 检查路由是否需要认证
export function requiresAuthentication(route: string): boolean {
  return routeMetadata[route]?.requiresAuth || false;
}

// 检查路由是否需要钱包连接
export function requiresWalletConnection(route: string): boolean {
  return routeMetadata[route]?.requiresWallet || false;
}

// 获取页面标题
export function getPageTitle(route: string): string {
  return routeMetadata[route]?.title || 'SocioMint';
}

// 获取页面描述
export function getPageDescription(route: string): string {
  return routeMetadata[route]?.description || 'SocioMint - 社交挖矿平台';
}

// 获取页面关键词
export function getPageKeywords(route: string): string[] {
  return routeMetadata[route]?.keywords || [];
}
