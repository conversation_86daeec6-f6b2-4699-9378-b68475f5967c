/**
 * 统一的图标配置文件
 * 使用 Lucide React 图标库，确保整个项目的图标一致性
 */

import {
  // 导航和菜单
  Menu,
  X,
  Home,
  ArrowLeft,
  ArrowRight,
  ArrowUp,
  ArrowDown,
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  MoreVertical,
  QrCode,

  // 用户和认证
  User,
  Users,
  UserPlus,
  UserCheck,
  UserX,
  LogIn,
  LogOut,
  Shield,
  ShieldCheck,
  Lock,
  Unlock,

  // 钱包和金融
  Wallet,
  CreditCard,
  Coins,
  DollarSign,
  TrendingUp,
  TrendingDown,
  BarChart3,
  LineChart,
  PieChart,
  Activity,

  // 社交平台
  Twitter,
  MessageCircle,
  Youtube,
  Instagram,
  Facebook,
  Linkedin,
  Github,
  Globe,
  Link,
  Share2,

  // 操作和状态
  Plus,
  Minus,
  Edit,
  Edit2,
  Trash2,
  Save,
  Download,
  Upload,
  Copy,
  Check,
  CheckCircle,
  CheckCircle2,
  X as XIcon,
  XCircle,
  AlertCircle,
  AlertTriangle,
  Info,
  HelpCircle,

  // 搜索和过滤
  Search,
  Filter,
  SlidersHorizontal,
  Settings,
  Settings2,
  Sliders,

  // 时间和日期
  Clock,
  Calendar,
  CalendarDays,
  Timer,
  History,

  // 通知和消息
  Bell,
  BellRing,
  BellOff,
  Mail,
  MailOpen,
  MessageSquare,
  Send,

  // 文件和文档
  File,
  FileText,
  Image,
  Video,
  Music,
  Download as DownloadIcon,
  Upload as UploadIcon,

  // 商业和交易
  ShoppingCart,
  ShoppingBag,
  Store,
  Package,
  Truck,
  CreditCard as Card,

  // 奖励和成就
  Award,
  Trophy,
  Star,
  StarHalf,
  Gift,
  Target,
  Zap,
  Crown,
  Medal,

  // 技术和开发
  Code,
  Terminal,
  Database,
  Server,
  Wifi,
  WifiOff,
  Smartphone,
  Monitor,
  Tablet,

  // 其他常用
  Eye,
  EyeOff,
  Heart,
  HeartHandshake,
  ThumbsUp,
  ThumbsDown,
  Bookmark,
  Flag,
  MapPin,
  ExternalLink,
  RotateCcw,
  Power,
  Volume2,
  VolumeX,
  ArrowLeftRight,
} from 'lucide-react';

// 图标尺寸配置
export const ICON_SIZES = {
  xs: 12,
  sm: 16,
  md: 20,
  lg: 24,
  xl: 32,
  '2xl': 48,
  '3xl': 64,
} as const;

// 图标颜色配置（基于设计系统）
export const ICON_COLORS = {
  primary: 'text-system-blue',
  secondary: 'text-system-purple',
  success: 'text-system-green',
  warning: 'text-system-orange',
  error: 'text-system-red',
  muted: 'text-secondary-label',
  default: 'text-label',
} as const;

// 导航图标
export const NavigationIcons = {
  menu: Menu,
  close: X,
  home: Home,
  back: ArrowLeft,
  forward: ArrowRight,
  chevronDown: ChevronDown,
  chevronUp: ChevronUp,
  chevronLeft: ChevronLeft,
  chevronRight: ChevronRight,
  more: MoreHorizontal,
  moreVertical: MoreVertical,
} as const;

// 用户相关图标
export const UserIcons = {
  user: User,
  users: Users,
  userAdd: UserPlus,
  userCheck: UserCheck,
  userRemove: UserX,
  login: LogIn,
  logout: LogOut,
  shield: Shield,
  verified: ShieldCheck,
  lock: Lock,
  unlock: Unlock,
} as const;

// 金融相关图标
export const FinanceIcons = {
  wallet: Wallet,
  card: CreditCard,
  coins: Coins,
  dollar: DollarSign,
  trendUp: TrendingUp,
  trendDown: TrendingDown,
  trendingUp: TrendingUp,
  trendingDown: TrendingDown,
  barChart: BarChart3,
  lineChart: LineChart,
  pieChart: PieChart,
  activity: Activity,
} as const;

// 社交平台图标
export const SocialIcons = {
  twitter: Twitter,
  telegram: MessageCircle,
  youtube: Youtube,
  instagram: Instagram,
  facebook: Facebook,
  linkedin: Linkedin,
  github: Github,
  website: Globe,
  link: Link,
  share: Share2,
} as const;

// 操作图标
export const ActionIcons = {
  add: Plus,
  remove: Minus,
  edit: Edit,
  editAlt: Edit2,
  delete: Trash2,
  save: Save,
  download: Download,
  upload: Upload,
  copy: Copy,
  check: Check,
  checkCircle: CheckCircle,
  checkCircle2: CheckCircle2,
  close: XIcon,
  closeCircle: XCircle,
  alert: AlertCircle,
  warning: AlertTriangle,
  info: Info,
  help: HelpCircle,
  helpCircle: HelpCircle,
  refresh: RotateCcw,
  send: Send,
  receive: ArrowDown,
  exchange: ArrowLeftRight,
} as const;

// 功能图标
export const FeatureIcons = {
  search: Search,
  filter: Filter,
  settings: Settings,
  settingsAlt: Settings2,
  sliders: SlidersHorizontal,
  clock: Clock,
  calendar: Calendar,
  calendarDays: CalendarDays,
  timer: Timer,
  history: History,
} as const;

// 通知图标
export const NotificationIcons = {
  bell: Bell,
  bellRing: BellRing,
  bellOff: BellOff,
  mail: Mail,
  mailOpen: MailOpen,
  message: MessageSquare,
  send: Send,
} as const;

// 奖励图标
export const RewardIcons = {
  award: Award,
  trophy: Trophy,
  star: Star,
  starHalf: StarHalf,
  gift: Gift,
  target: Target,
  zap: Zap,
  crown: Crown,
  medal: Medal,
} as const;

// 商业图标
export const BusinessIcons = {
  cart: ShoppingCart,
  bag: ShoppingBag,
  store: Store,
  package: Package,
  truck: Truck,
  card: Card,
} as const;

// 媒体图标
export const MediaIcons = {
  file: File,
  fileText: FileText,
  image: Image,
  video: Video,
  music: Music,
  download: DownloadIcon,
  upload: UploadIcon,
} as const;

// 技术图标
export const TechIcons = {
  code: Code,
  terminal: Terminal,
  database: Database,
  server: Server,
  wifi: Wifi,
  wifiOff: WifiOff,
  smartphone: Smartphone,
  monitor: Monitor,
  tablet: Tablet,
} as const;

// 其他图标
export const MiscIcons = {
  eye: Eye,
  eyeOff: EyeOff,
  heart: Heart,
  handshake: HeartHandshake,
  thumbsUp: ThumbsUp,
  thumbsDown: ThumbsDown,
  bookmark: Bookmark,
  flag: Flag,
  mapPin: MapPin,
  externalLink: ExternalLink,
  rotate: RotateCcw,
  power: Power,
  volume: Volume2,
  volumeOff: VolumeX,
  qrCode: QrCode,
} as const;

// 导出所有图标的统一接口
export const Icons = {
  ...NavigationIcons,
  ...UserIcons,
  ...FinanceIcons,
  ...SocialIcons,
  ...ActionIcons,
  ...FeatureIcons,
  ...NotificationIcons,
  ...RewardIcons,
  ...BusinessIcons,
  ...MediaIcons,
  ...TechIcons,
  ...MiscIcons,
} as const;

// 图标组件的通用属性类型
export interface IconProps {
  size?: keyof typeof ICON_SIZES | number;
  color?: keyof typeof ICON_COLORS | string;
  className?: string;
}

// 获取图标尺寸的辅助函数
export function getIconSize(size: keyof typeof ICON_SIZES | number): number {
  if (typeof size === 'number') return size;
  return ICON_SIZES[size];
}

// 获取图标颜色的辅助函数
export function getIconColor(color: keyof typeof ICON_COLORS | string): string {
  if (color in ICON_COLORS) {
    return ICON_COLORS[color as keyof typeof ICON_COLORS];
  }
  return color;
}
