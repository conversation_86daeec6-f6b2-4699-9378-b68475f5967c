'use client';

import React from 'react';
import { Button, Icon } from '@/components/ui';
import { ActionIcons, NavigationIcons, MiscIcons } from '@/config/icons';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
    });

    // Log error to monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      // Example: Sentry.captureException(error, { extra: errorInfo });
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return <FallbackComponent error={this.state.error!} retry={this.handleRetry} />;
      }

      return <DefaultErrorFallback error={this.state.error!} retry={this.handleRetry} />;
    }

    return this.props.children;
  }
}

interface DefaultErrorFallbackProps {
  error: Error;
  retry: () => void;
}

const DefaultErrorFallback: React.FC<DefaultErrorFallbackProps> = ({ error, retry }) => {
  const isDevelopment = process.env.NODE_ENV === 'development';

  return (
    <div className="min-h-screen bg-system-background flex items-center justify-center p-4">
      <div className="max-w-md w-full text-center">
        <div className="w-20 h-20 bg-system-red/10 rounded-2xl flex items-center justify-center mx-auto mb-6">
          <Icon icon={ActionIcons.warning} size="2xl" color="error" />
        </div>
        
        <h1 className="text-title-2 font-sf-pro font-bold text-label mb-4">
          出现了一些问题
        </h1>
        
        <p className="text-body text-secondary-label mb-6">
          抱歉，应用程序遇到了意外错误。我们已经记录了这个问题，请稍后重试。
        </p>

        {isDevelopment && (
          <div className="mb-6 p-4 bg-system-red/5 border border-system-red/20 rounded-xl text-left">
            <h3 className="text-body font-sf-pro font-semibold text-system-red mb-2">
              开发模式错误信息:
            </h3>
            <pre className="text-caption-1 text-system-red overflow-auto">
              {error.message}
            </pre>
            {error.stack && (
              <details className="mt-2">
                <summary className="text-caption-1 text-system-red cursor-pointer">
                  查看堆栈跟踪
                </summary>
                <pre className="text-caption-1 text-system-red mt-2 overflow-auto">
                  {error.stack}
                </pre>
              </details>
            )}
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Button
            onClick={retry}
            className="flex items-center space-x-2"
          >
            <Icon icon={MiscIcons.rotate} size="sm" />
            <span>重试</span>
          </Button>
          
          <Button
            variant="outline"
            onClick={() => window.location.href = '/'}
            className="flex items-center space-x-2"
          >
            <Icon icon={NavigationIcons.home} size="sm" />
            <span>返回首页</span>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ErrorBoundary;
