'use client';

import React from 'react';
import { AlertTriangle, XCircle, Info, CheckCircle, X, RefreshCw } from 'lucide-react';
import { cn } from '@/lib/utils';

// 错误类型定义
export type ErrorType = 'error' | 'warning' | 'info' | 'success';

// 错误显示组件的属性
interface ErrorDisplayProps {
  type?: ErrorType;
  title?: string;
  message: string;
  details?: string;
  actions?: Array<{
    label: string;
    onClick: () => void;
    variant?: 'primary' | 'secondary' | 'danger';
  }>;
  onClose?: () => void;
  className?: string;
  showIcon?: boolean;
  dismissible?: boolean;
  autoClose?: boolean;
  autoCloseDelay?: number;
}

// 错误图标映射
const ErrorIcons = {
  error: XCircle,
  warning: AlertTriangle,
  info: Info,
  success: CheckCircle,
};

// 错误样式映射
const ErrorStyles = {
  error: {
    container: 'bg-red-50 border-red-200 text-red-800',
    icon: 'text-red-500',
    title: 'text-red-900',
    message: 'text-red-700',
    button: 'bg-red-100 hover:bg-red-200 text-red-800',
  },
  warning: {
    container: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    icon: 'text-yellow-500',
    title: 'text-yellow-900',
    message: 'text-yellow-700',
    button: 'bg-yellow-100 hover:bg-yellow-200 text-yellow-800',
  },
  info: {
    container: 'bg-blue-50 border-blue-200 text-blue-800',
    icon: 'text-blue-500',
    title: 'text-blue-900',
    message: 'text-blue-700',
    button: 'bg-blue-100 hover:bg-blue-200 text-blue-800',
  },
  success: {
    container: 'bg-green-50 border-green-200 text-green-800',
    icon: 'text-green-500',
    title: 'text-green-900',
    message: 'text-green-700',
    button: 'bg-green-100 hover:bg-green-200 text-green-800',
  },
};

/**
 * 统一的错误显示组件
 */
export function ErrorDisplay({
  type = 'error',
  title,
  message,
  details,
  actions = [],
  onClose,
  className,
  showIcon = true,
  dismissible = true,
  autoClose = false,
  autoCloseDelay = 5000,
}: ErrorDisplayProps) {
  const [isVisible, setIsVisible] = React.useState(true);
  const [isExpanded, setIsExpanded] = React.useState(false);

  const Icon = ErrorIcons[type];
  const styles = ErrorStyles[type];

  // 自动关闭逻辑
  React.useEffect(() => {
    if (autoClose && autoCloseDelay > 0) {
      const timer = setTimeout(() => {
        handleClose();
      }, autoCloseDelay);

      return () => clearTimeout(timer);
    }
  }, [autoClose, autoCloseDelay]);

  const handleClose = () => {
    setIsVisible(false);
    onClose?.();
  };

  const toggleDetails = () => {
    setIsExpanded(!isExpanded);
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div
      className={cn(
        'border rounded-lg p-4 shadow-sm',
        styles.container,
        className
      )}
      role="alert"
      aria-live="polite"
    >
      <div className="flex items-start">
        {/* 图标 */}
        {showIcon && (
          <div className="flex-shrink-0">
            <Icon className={cn('h-5 w-5', styles.icon)} aria-hidden="true" />
          </div>
        )}

        {/* 内容 */}
        <div className={cn('flex-1', showIcon && 'ml-3')}>
          {/* 标题 */}
          {title && (
            <h3 className={cn('text-sm font-medium', styles.title)}>
              {title}
            </h3>
          )}

          {/* 消息 */}
          <div className={cn('text-sm', title && 'mt-1', styles.message)}>
            {message}
          </div>

          {/* 详细信息 */}
          {details && (
            <div className="mt-2">
              <button
                onClick={toggleDetails}
                className={cn(
                  'text-xs underline hover:no-underline focus:outline-none',
                  styles.message
                )}
              >
                {isExpanded ? '隐藏详情' : '显示详情'}
              </button>
              
              {isExpanded && (
                <div className={cn(
                  'mt-2 p-2 bg-white/50 rounded text-xs font-mono whitespace-pre-wrap',
                  styles.message
                )}>
                  {details}
                </div>
              )}
            </div>
          )}

          {/* 操作按钮 */}
          {actions.length > 0 && (
            <div className="mt-3 flex flex-wrap gap-2">
              {actions.map((action, index) => (
                <button
                  key={index}
                  onClick={action.onClick}
                  className={cn(
                    'px-3 py-1 text-xs font-medium rounded-md transition-colors',
                    action.variant === 'primary' && styles.button,
                    action.variant === 'secondary' && 'bg-gray-100 hover:bg-gray-200 text-gray-800',
                    action.variant === 'danger' && 'bg-red-100 hover:bg-red-200 text-red-800',
                    !action.variant && styles.button
                  )}
                >
                  {action.label}
                </button>
              ))}
            </div>
          )}
        </div>

        {/* 关闭按钮 */}
        {dismissible && (
          <div className="flex-shrink-0 ml-4">
            <button
              onClick={handleClose}
              className={cn(
                'inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2',
                styles.button
              )}
              aria-label="关闭"
            >
              <X className="h-4 w-4" aria-hidden="true" />
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

/**
 * 简化的错误提示组件
 */
export function ErrorAlert({ 
  message, 
  onRetry, 
  className 
}: { 
  message: string; 
  onRetry?: () => void; 
  className?: string; 
}) {
  const actions = onRetry ? [
    {
      label: '重试',
      onClick: onRetry,
      variant: 'primary' as const,
    }
  ] : [];

  return (
    <ErrorDisplay
      type="error"
      title="操作失败"
      message={message}
      actions={actions}
      className={className}
    />
  );
}

/**
 * 成功提示组件
 */
export function SuccessAlert({ 
  message, 
  className 
}: { 
  message: string; 
  className?: string; 
}) {
  return (
    <ErrorDisplay
      type="success"
      message={message}
      autoClose={true}
      autoCloseDelay={3000}
      className={className}
    />
  );
}

/**
 * 警告提示组件
 */
export function WarningAlert({ 
  message, 
  className 
}: { 
  message: string; 
  className?: string; 
}) {
  return (
    <ErrorDisplay
      type="warning"
      message={message}
      className={className}
    />
  );
}

/**
 * 信息提示组件
 */
export function InfoAlert({ 
  message, 
  className 
}: { 
  message: string; 
  className?: string; 
}) {
  return (
    <ErrorDisplay
      type="info"
      message={message}
      className={className}
    />
  );
}

/**
 * 网络错误组件
 */
export function NetworkError({ 
  onRetry, 
  className 
}: { 
  onRetry?: () => void; 
  className?: string; 
}) {
  const actions = [
    ...(onRetry ? [{
      label: '重试',
      onClick: onRetry,
      variant: 'primary' as const,
    }] : []),
    {
      label: '刷新页面',
      onClick: () => window.location.reload(),
      variant: 'secondary' as const,
    }
  ];

  return (
    <ErrorDisplay
      type="error"
      title="网络连接错误"
      message="无法连接到服务器，请检查您的网络连接。"
      actions={actions}
      className={className}
      showIcon={true}
    />
  );
}

/**
 * 加载错误组件
 */
export function LoadingError({ 
  resource, 
  onRetry, 
  className 
}: { 
  resource?: string; 
  onRetry?: () => void; 
  className?: string; 
}) {
  const actions = onRetry ? [
    {
      label: '重新加载',
      onClick: onRetry,
      variant: 'primary' as const,
    }
  ] : [];

  return (
    <ErrorDisplay
      type="error"
      title="加载失败"
      message={`无法加载${resource || '内容'}，请稍后重试。`}
      actions={actions}
      className={className}
    />
  );
}

/**
 * 权限错误组件
 */
export function PermissionError({ 
  action, 
  className 
}: { 
  action?: string; 
  className?: string; 
}) {
  const actions = [
    {
      label: '返回首页',
      onClick: () => window.location.href = '/',
      variant: 'primary' as const,
    }
  ];

  return (
    <ErrorDisplay
      type="warning"
      title="权限不足"
      message={`您没有权限${action || '执行此操作'}，请联系管理员。`}
      actions={actions}
      className={className}
    />
  );
}

/**
 * 表单验证错误组件
 */
export function ValidationError({ 
  errors, 
  className 
}: { 
  errors: string[]; 
  className?: string; 
}) {
  return (
    <ErrorDisplay
      type="warning"
      title="表单验证失败"
      message="请修正以下错误："
      details={errors.join('\n')}
      className={className}
    />
  );
}
