'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { 
  Twitter, 
  MessageCircle, 
  Send, 
  Check, 
  Plus,
  ExternalLink,
  Shield,
  Unlink
} from 'lucide-react';
import { Button, Card, ConfirmModal } from '@/components/ui';
import { useSocialAccounts } from '@/hooks/useSocialAccounts';
import { SOCIAL_PLATFORMS } from '@/constants';
import { cn } from '@/lib/utils';

interface SocialConnectProps {
  userId?: string;
  className?: string;
  showTitle?: boolean;
}

const SocialConnect: React.FC<SocialConnectProps> = ({ 
  userId, 
  className,
  showTitle = true 
}) => {
  const {
    accounts,
    isLoading,
    isConnecting,
    connectTwitter,
    connectDiscord,
    connectTelegram,
    disconnectAccount,
    isConnected,
    getAccountByPlatform,
    getConnectionStatus,
  } = useSocialAccounts(userId);

  const [disconnectModal, setDisconnectModal] = React.useState<{
    isOpen: boolean;
    accountId: string;
    platform: string;
  }>({ isOpen: false, accountId: '', platform: '' });

  const platforms = [
    {
      id: 'twitter',
      name: 'Twitter',
      icon: Twitter,
      color: 'text-blue-500',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      description: '连接您的 Twitter 账户以完成相关任务',
      connectAction: connectTwitter,
    },
    {
      id: 'discord',
      name: 'Discord',
      icon: MessageCircle,
      color: 'text-indigo-500',
      bgColor: 'bg-indigo-50',
      borderColor: 'border-indigo-200',
      description: '连接您的 Discord 账户以加入社区',
      connectAction: connectDiscord,
    },
    {
      id: 'telegram',
      name: 'Telegram',
      icon: Send,
      color: 'text-sky-500',
      bgColor: 'bg-sky-50',
      borderColor: 'border-sky-200',
      description: '连接您的 Telegram 账户以接收通知',
      connectAction: connectTelegram,
    },
  ];

  const connectionStatus = getConnectionStatus();

  const handleDisconnect = (accountId: string, platform: string) => {
    setDisconnectModal({
      isOpen: true,
      accountId,
      platform,
    });
  };

  const confirmDisconnect = async () => {
    await disconnectAccount(disconnectModal.accountId, disconnectModal.platform);
    setDisconnectModal({ isOpen: false, accountId: '', platform: '' });
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <div className="animate-pulse space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="h-20 bg-system-gray-5 rounded-xl" />
          ))}
        </div>
      </Card>
    );
  }

  return (
    <>
      <Card className={className}>
        {showTitle && (
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-title-3 font-sf-pro font-bold text-label">
              社交账户连接
            </h3>
            <div className="flex items-center space-x-2">
              <Shield className="w-5 h-5 text-system-blue" />
              <span className="text-body font-sf-pro font-medium text-label">
                {connectionStatus.total}/3 已连接
              </span>
            </div>
          </div>
        )}

        {/* Connection Status Summary */}
        <div className="bg-system-gray-6 rounded-xl p-4 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-body font-sf-pro font-medium text-label mb-1">
                连接进度
              </p>
              <p className="text-caption-1 text-secondary-label">
                连接更多社交账户以解锁更多功能和奖励
              </p>
            </div>
            <div className="text-right">
              <div className="w-16 h-16 relative">
                <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
                  <path
                    className="text-system-gray-4"
                    stroke="currentColor"
                    strokeWidth="3"
                    fill="none"
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  />
                  <path
                    className="text-system-blue"
                    stroke="currentColor"
                    strokeWidth="3"
                    strokeDasharray={`${(connectionStatus.total / 3) * 100}, 100`}
                    strokeLinecap="round"
                    fill="none"
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  />
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-sm font-sf-pro font-bold text-label">
                    {Math.round((connectionStatus.total / 3) * 100)}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Platform List */}
        <div className="space-y-4">
          {platforms.map((platform, index) => {
            const account = getAccountByPlatform(platform.id as any);
            const connected = isConnected(platform.id as any);
            const connecting = isConnecting === platform.id;

            return (
              <motion.div
                key={platform.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className={cn(
                  'p-4 rounded-xl border-2 transition-all duration-200',
                  connected 
                    ? 'border-system-green bg-system-green/5' 
                    : 'border-system-gray-4 hover:border-system-gray-3'
                )}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className={cn(
                      'w-12 h-12 rounded-xl flex items-center justify-center',
                      connected ? 'bg-system-green/10' : platform.bgColor
                    )}>
                      <platform.icon className={cn(
                        'w-6 h-6',
                        connected ? 'text-system-green' : platform.color
                      )} />
                    </div>
                    
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="text-body font-sf-pro font-semibold text-label">
                          {platform.name}
                        </h4>
                        {connected && (
                          <Check className="w-4 h-4 text-system-green" />
                        )}
                      </div>
                      
                      {connected && account ? (
                        <p className="text-caption-1 text-secondary-label">
                          已连接: {account.platform_username}
                        </p>
                      ) : (
                        <p className="text-caption-1 text-secondary-label">
                          {platform.description}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    {connected && account ? (
                      <>
                        <button
                          onClick={() => window.open(
                            platform.id === 'twitter' ? `https://twitter.com/${account.platform_username.replace('@', '')}` :
                            platform.id === 'discord' ? 'https://discord.com' :
                            platform.id === 'telegram' ? `https://t.me/${account.platform_username.replace('@', '')}` :
                            '#',
                            '_blank'
                          )}
                          className="p-2 hover:bg-system-gray-6 rounded-lg transition-colors"
                          title="访问账户"
                        >
                          <ExternalLink className="w-4 h-4 text-secondary-label" />
                        </button>
                        
                        <button
                          onClick={() => handleDisconnect(account.id, platform.name)}
                          className="p-2 hover:bg-system-red/10 rounded-lg transition-colors"
                          title="断开连接"
                        >
                          <Unlink className="w-4 h-4 text-system-red" />
                        </button>
                      </>
                    ) : (
                      <Button
                        onClick={platform.connectAction}
                        disabled={connecting}
                        loading={connecting}
                        size="sm"
                        className="flex items-center space-x-2"
                      >
                        <Plus className="w-4 h-4" />
                        <span>连接</span>
                      </Button>
                    )}
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>

        {/* Benefits */}
        {connectionStatus.total > 0 && (
          <div className="mt-6 p-4 bg-system-blue/5 border border-system-blue/20 rounded-xl">
            <div className="flex items-start space-x-3">
              <Shield className="w-5 h-5 text-system-blue mt-0.5" />
              <div>
                <h4 className="text-body font-sf-pro font-semibold text-label mb-1">
                  连接奖励
                </h4>
                <p className="text-caption-1 text-secondary-label">
                  您已连接 {connectionStatus.total} 个社交账户，可以参与更多任务并获得额外奖励！
                </p>
              </div>
            </div>
          </div>
        )}
      </Card>

      {/* Disconnect Confirmation Modal */}
      <ConfirmModal
        isOpen={disconnectModal.isOpen}
        onClose={() => setDisconnectModal({ isOpen: false, accountId: '', platform: '' })}
        onConfirm={confirmDisconnect}
        title="断开社交账户"
        message={`确定要断开 ${disconnectModal.platform} 账户的连接吗？断开后您将无法完成相关的社交任务。`}
        confirmText="断开连接"
        cancelText="取消"
        type="warning"
      />
    </>
  );
};

export default SocialConnect;
