'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Twitter,
  Send,
  Heart,
  Repeat,
  Share
} from 'lucide-react';
import {
  RewardIcons,
  SocialIcons,
  UserIcons,
  ActionIcons,
  FeatureIcons,
  FinanceIcons
} from '@/config/icons';
import { <PERSON><PERSON>, <PERSON> } from '@/components/ui';
import { useSocialTasks } from '@/hooks/useSocialTasks';
import { useSocialAccounts } from '@/hooks/useSocialAccounts';
import { formatNumber } from '@/lib/utils';
import { cn } from '@/lib/utils';

interface SocialTasksProps {
  userId?: string;
  className?: string;
  showTitle?: boolean;
}

const SocialTasks: React.FC<SocialTasksProps> = ({ 
  userId, 
  className,
  showTitle = true 
}) => {
  const {
    tasks,
    isLoading,
    isCompleting,
    completeTask,
    isTaskCompleted,
    getCompletionStats,
    getTasksByPlatform,
  } = useSocialTasks(userId);

  const { isConnected } = useSocialAccounts(userId);
  const [activeFilter, setActiveFilter] = useState<'all' | 'twitter' | 'discord' | 'telegram'>('all');

  const stats = getCompletionStats();

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'twitter':
        return Twitter;
      case 'discord':
        return SocialIcons.telegram;
      case 'telegram':
        return Send;
      default:
        return RewardIcons.award;
    }
  };

  const getTaskTypeIcon = (taskType: string) => {
    switch (taskType) {
      case 'follow':
        return UserIcons.userAdd;
      case 'like':
        return Heart;
      case 'retweet':
        return Repeat;
      case 'join':
        return UserIcons.users;
      case 'share':
        return Share;
      default:
        return RewardIcons.award;
    }
  };

  const getTaskTypeText = (taskType: string) => {
    switch (taskType) {
      case 'follow':
        return '关注';
      case 'like':
        return '点赞';
      case 'retweet':
        return '转发';
      case 'join':
        return '加入';
      case 'share':
        return '分享';
      default:
        return '任务';
    }
  };

  const getPlatformName = (platform: string) => {
    switch (platform) {
      case 'twitter':
        return 'Twitter';
      case 'discord':
        return 'Discord';
      case 'telegram':
        return 'Telegram';
      default:
        return '未知';
    }
  };

  const filteredTasks = activeFilter === 'all' 
    ? tasks 
    : getTasksByPlatform(activeFilter);

  const handleCompleteTask = async (taskId: string, platform: string) => {
    // Check if platform is connected
    if (!isConnected(platform as any)) {
      // Show connect platform message
      return;
    }

    await completeTask(taskId);
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <div className="animate-pulse space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="h-24 bg-system-gray-5 rounded-xl" />
          ))}
        </div>
      </Card>
    );
  }

  return (
    <Card className={className}>
      {showTitle && (
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-title-3 font-sf-pro font-bold text-label">
            社交任务
          </h3>
          <div className="flex items-center space-x-2">
            <RewardIcons.award className="w-5 h-5 text-system-orange" />
            <span className="text-body font-sf-pro font-medium text-label">
              {formatNumber(stats.totalRewards)} HAOX
            </span>
          </div>
        </div>
      )}

      {/* Stats Overview */}
      <div className="grid grid-cols-3 gap-4 mb-6">
        <div className="text-center p-3 bg-system-gray-6 rounded-xl">
          <p className="text-title-3 font-sf-pro font-bold text-label">
            {stats.completedTasks}
          </p>
          <p className="text-caption-1 text-secondary-label">已完成</p>
        </div>
        <div className="text-center p-3 bg-system-gray-6 rounded-xl">
          <p className="text-title-3 font-sf-pro font-bold text-label">
            {stats.totalTasks}
          </p>
          <p className="text-caption-1 text-secondary-label">总任务</p>
        </div>
        <div className="text-center p-3 bg-system-gray-6 rounded-xl">
          <p className="text-title-3 font-sf-pro font-bold text-system-orange">
            {formatNumber(stats.totalRewards)}
          </p>
          <p className="text-caption-1 text-secondary-label">总奖励</p>
        </div>
      </div>

      {/* Filter Tabs */}
      <div className="flex bg-system-gray-6 rounded-xl p-1 mb-6">
        {[
          { key: 'all', label: '全部', icon: Award },
          { key: 'twitter', label: 'Twitter', icon: Twitter },
          { key: 'discord', label: 'Discord', icon: MessageCircle },
          { key: 'telegram', label: 'Telegram', icon: Send },
        ].map((filter) => (
          <button
            key={filter.key}
            onClick={() => setActiveFilter(filter.key as any)}
            className={cn(
              'flex-1 flex items-center justify-center space-x-2 py-2 px-3 rounded-lg text-sm font-sf-pro font-medium transition-colors',
              activeFilter === filter.key
                ? 'bg-system-blue text-white'
                : 'text-secondary-label hover:text-label'
            )}
          >
            <filter.icon className="w-4 h-4" />
            <span className="hidden sm:inline">{filter.label}</span>
          </button>
        ))}
      </div>

      {/* Task List */}
      <div className="space-y-4">
        {filteredTasks.length === 0 ? (
          <div className="text-center py-8">
            <RewardIcons.award className="w-12 h-12 text-system-gray mx-auto mb-3" />
            <p className="text-body text-secondary-label">
              暂无可用任务
            </p>
          </div>
        ) : (
          filteredTasks.map((task, index) => {
            const PlatformIcon = getPlatformIcon(task.platform);
            const TaskTypeIcon = getTaskTypeIcon(task.task_type);
            const completed = isTaskCompleted(task.id);
            const completing = isCompleting === task.id;
            const platformConnected = isConnected(task.platform as any);
            const progress = (task.current_completions / task.max_completions) * 100;

            return (
              <motion.div
                key={task.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                className={cn(
                  'p-4 rounded-xl border-2 transition-all duration-200',
                  completed 
                    ? 'border-system-green bg-system-green/5' 
                    : 'border-system-gray-4 hover:border-system-blue/50'
                )}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4 flex-1">
                    {/* Platform & Task Type Icons */}
                    <div className="relative">
                      <div className={cn(
                        'w-12 h-12 rounded-xl flex items-center justify-center',
                        completed ? 'bg-system-green/10' : 'bg-system-gray-6'
                      )}>
                        <PlatformIcon className={cn(
                          'w-6 h-6',
                          completed ? 'text-system-green' : 'text-secondary-label'
                        )} />
                      </div>
                      <div className={cn(
                        'absolute -bottom-1 -right-1 w-6 h-6 rounded-full flex items-center justify-center border-2 border-system-background',
                        completed ? 'bg-system-green' : 'bg-system-blue'
                      )}>
                        <TaskTypeIcon className="w-3 h-3 text-white" />
                      </div>
                    </div>

                    {/* Task Info */}
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="text-body font-sf-pro font-semibold text-label">
                          {task.title}
                        </h4>
                        {completed && (
                          <ActionIcons.checkCircle className="w-4 h-4 text-system-green" />
                        )}
                      </div>
                      
                      <p className="text-caption-1 text-secondary-label mb-2">
                        {task.description}
                      </p>

                      <div className="flex items-center space-x-4 text-caption-1 text-secondary-label">
                        <div className="flex items-center space-x-1">
                          <RewardIcons.award className="w-3 h-3" />
                          <span>{task.reward_amount} HAOX</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <UserIcons.users className="w-3 h-3" />
                          <span>{formatNumber(task.current_completions)}/{formatNumber(task.max_completions)}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <span className="w-2 h-2 rounded-full bg-system-blue"></span>
                          <span>{getPlatformName(task.platform)}</span>
                        </div>
                      </div>

                      {/* Progress Bar */}
                      <div className="mt-3">
                        <div className="w-full bg-system-gray-5 rounded-full h-1.5">
                          <div 
                            className="bg-system-blue h-1.5 rounded-full transition-all duration-300"
                            style={{ width: `${Math.min(progress, 100)}%` }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Action Button */}
                  <div className="ml-4">
                    {completed ? (
                      <div className="flex items-center space-x-2 text-system-green">
                        <ActionIcons.checkCircle className="w-5 h-5" />
                        <span className="text-sm font-sf-pro font-medium">已完成</span>
                      </div>
                    ) : !platformConnected ? (
                      <div className="text-center">
                        <p className="text-caption-1 text-secondary-label mb-2">
                          需要连接 {getPlatformName(task.platform)}
                        </p>
                        <Button size="sm" variant="outline">
                          连接账户
                        </Button>
                      </div>
                    ) : (
                      <Button
                        onClick={() => handleCompleteTask(task.id, task.platform)}
                        disabled={completing}
                        loading={completing}
                        size="sm"
                        className="flex items-center space-x-2"
                      >
                        {completing ? (
                          <FeatureIcons.clock className="w-4 h-4" />
                        ) : (
                          <FinanceIcons.trendUp className="w-4 h-4" />
                        )}
                        <span>{completing ? '完成中' : '完成任务'}</span>
                      </Button>
                    )}
                  </div>
                </div>
              </motion.div>
            );
          })
        )}
      </div>

      {/* Call to Action */}
      {stats.completedTasks === 0 && (
        <div className="mt-6 p-4 bg-system-blue/5 border border-system-blue/20 rounded-xl text-center">
          <RewardIcons.award className="w-8 h-8 text-system-blue mx-auto mb-2" />
          <h4 className="text-body font-sf-pro font-semibold text-label mb-1">
            开始赚取奖励
          </h4>
          <p className="text-caption-1 text-secondary-label">
            完成社交任务即可获得 HAOX 代币奖励
          </p>
        </div>
      )}
    </Card>
  );
};

export default SocialTasks;
