'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button, Icon, Card } from '@/components/ui';
import { ActionIcons, FinanceIcons, FeatureIcons } from '@/config/icons';
import { useTelegramAuth } from '@/hooks/useTelegramAuth';
import { useWallet } from '@/hooks/useWallet';
import { cn } from '@/lib/utils';
import { ethers } from 'ethers';

interface WithdrawFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

const WithdrawForm: React.FC<WithdrawFormProps> = ({ onSuccess, onCancel }) => {
  const { user } = useTelegramAuth();
  const {
    balance,
    withdraw,
    isWithdrawing,
    error: withdrawError
  } = useWallet();

  // 格式化函数
  const formatBalance = (amount: string, decimals: number = 2): string => {
    const num = parseFloat(amount || '0');
    return num.toFixed(decimals);
  };

  const formatUSD = (amount: number): string => {
    return `$${amount.toFixed(2)}`;
  };

  // 提现费用估算
  const estimateWithdrawFee = async (amount: string): Promise<string> => {
    try {
      const baseGas = 0.002; // 基础提现费用
      const amountNum = parseFloat(amount);
      const estimatedFee = baseGas + (amountNum * 0.0002);
      return estimatedFee.toFixed(6);
    } catch (error) {
      console.error('Withdraw fee estimation failed:', error);
      return '0.002';
    }
  };

  const [formData, setFormData] = useState({
    toAddress: '',
    amount: '',
    tokenType: 'HAOX' as 'HAOX' | 'BNB',
  });
  const [withdrawFee, setWithdrawFee] = useState<string>('0');
  const [isValidAddress, setIsValidAddress] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 提现限制配置
  const withdrawLimits = {
    HAOX: {
      min: '10',
      max: '10000',
      dailyLimit: '50000',
    },
    BNB: {
      min: '0.01',
      max: '10',
      dailyLimit: '50',
    },
  };

  /**
   * 验证钱包地址
   */
  useEffect(() => {
    if (formData.toAddress) {
      const isValid = ethers.isAddress(formData.toAddress);
      setIsValidAddress(isValid);
      
      if (!isValid && formData.toAddress.length > 10) {
        setErrors(prev => ({ ...prev, toAddress: '无效的钱包地址' }));
      } else {
        setErrors(prev => ({ ...prev, toAddress: '' }));
      }
    } else {
      setIsValidAddress(false);
      setErrors(prev => ({ ...prev, toAddress: '' }));
    }
  }, [formData.toAddress]);

  /**
   * 估算提现费用
   */
  useEffect(() => {
    const estimateFee = async () => {
      if (isValidAddress && formData.amount && parseFloat(formData.amount) > 0) {
        try {
          const fee = await estimateWithdrawFee({
            to: formData.toAddress,
            amount: formData.amount,
            tokenType: formData.tokenType,
          });
          setWithdrawFee(fee);
        } catch (error) {
          console.error('Failed to estimate withdraw fee:', error);
          setWithdrawFee('0.005'); // 默认值
        }
      }
    };

    estimateFee();
  }, [formData.toAddress, formData.amount, formData.tokenType, isValidAddress, estimateWithdrawFee]);

  /**
   * 验证表单
   */
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    const limits = withdrawLimits[formData.tokenType];

    if (!formData.toAddress) {
      newErrors.toAddress = '请输入提现地址';
    } else if (!isValidAddress) {
      newErrors.toAddress = '无效的钱包地址';
    }

    if (!formData.amount) {
      newErrors.amount = '请输入提现金额';
    } else {
      const amount = parseFloat(formData.amount);
      if (isNaN(amount) || amount <= 0) {
        newErrors.amount = '请输入有效的金额';
      } else if (amount < parseFloat(limits.min)) {
        newErrors.amount = `最小提现金额: ${limits.min} ${formData.tokenType}`;
      } else if (amount > parseFloat(limits.max)) {
        newErrors.amount = `最大提现金额: ${limits.max} ${formData.tokenType}`;
      } else if (balance) {
        const availableBalance = parseFloat(
          formData.tokenType === 'HAOX' ? balance.haoxBalance : balance.bnbBalance
        );
        const totalCost = amount + parseFloat(withdrawFee);
        if (totalCost > availableBalance) {
          newErrors.amount = `余额不足，可用余额: ${formatBalance(availableBalance.toString())}`;
        }
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  /**
   * 处理表单提交
   */
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      setShowConfirmation(true);
    }
  };

  /**
   * 确认提现
   */
  const handleConfirmWithdraw = async () => {
    try {
      const result = await withdraw({
        toAddress: formData.toAddress,
        amount: formData.amount,
        verificationCode: formData.verificationCode,
      });

      if (result.success) {
        setShowConfirmation(false);
        onSuccess?.();
      }
    } catch (error) {
      console.error('Withdraw failed:', error);
    }
  };

  /**
   * 设置最大金额
   */
  const setMaxAmount = () => {
    if (balance) {
      const maxBalance = formData.tokenType === 'HAOX' 
        ? balance.haoxBalance 
        : balance.bnbBalance;
      const fee = parseFloat(withdrawFee);
      const maxAmount = Math.max(0, parseFloat(maxBalance) - fee);
      const limit = parseFloat(withdrawLimits[formData.tokenType].max);
      const finalAmount = Math.min(maxAmount, limit);
      setFormData(prev => ({ ...prev, amount: finalAmount.toString() }));
    }
  };

  if (showConfirmation) {
    return (
      <Card className="p-6">
        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-system-orange/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <Icon icon={ActionIcons.alert} size="xl" color="warning" />
          </div>
          <h3 className="text-title-2 font-sf-pro font-bold text-label mb-2">
            确认提现
          </h3>
          <p className="text-body text-secondary-label">
            请仔细核对提现信息，提现后无法撤销
          </p>
        </div>

        <div className="space-y-4 mb-6">
          <div className="bg-system-gray-6 rounded-xl p-4">
            <div className="flex justify-between items-center mb-2">
              <span className="text-caption-1 text-secondary-label">提现地址</span>
            </div>
            <p className="text-body font-mono text-label break-all">
              {formData.toAddress}
            </p>
          </div>

          <div className="bg-system-gray-6 rounded-xl p-4">
            <div className="flex justify-between items-center mb-2">
              <span className="text-caption-1 text-secondary-label">提现金额</span>
            </div>
            <p className="text-title-3 font-sf-pro font-bold text-label">
              {formatBalance(formData.amount)} {formData.tokenType}
            </p>
          </div>

          <div className="bg-system-gray-6 rounded-xl p-4">
            <div className="flex justify-between items-center mb-2">
              <span className="text-caption-1 text-secondary-label">提现手续费</span>
            </div>
            <p className="text-body text-label">
              {formatBalance(withdrawFee, 6)} BNB
            </p>
          </div>

          <div className="bg-system-gray-6 rounded-xl p-4">
            <div className="flex justify-between items-center">
              <span className="text-caption-1 text-secondary-label">实际到账</span>
              <span className="text-title-3 font-sf-pro font-bold text-label">
                {formatBalance(formData.amount)} {formData.tokenType}
              </span>
            </div>
          </div>
        </div>

        <div className="flex space-x-3">
          <Button
            variant="secondary"
            size="md"
            onClick={() => setShowConfirmation(false)}
            className="flex-1"
          >
            取消
          </Button>
          <Button
            variant="primary"
            size="md"
            onClick={handleConfirmWithdraw}
            disabled={isWithdrawing}
            className="flex-1"
          >
            {isWithdrawing ? '提现中...' : '确认提现'}
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-title-2 font-sf-pro font-bold text-label">
          提现到外部钱包
        </h3>
        {onCancel && (
          <Button variant="ghost" size="sm" onClick={onCancel}>
            <Icon icon={ActionIcons.close} size="sm" />
          </Button>
        )}
      </div>

      {/* 安全提示 */}
      <div className="mb-6 p-4 bg-system-yellow/10 border border-system-yellow/20 rounded-xl">
        <div className="flex items-start space-x-2">
          <Icon icon={ActionIcons.alert} size="sm" color="warning" className="mt-0.5" />
          <div>
            <p className="text-caption-1 text-label font-medium mb-1">
              重要提示
            </p>
            <ul className="text-caption-1 text-secondary-label space-y-1">
              <li>• 请确保提现地址支持BSC网络</li>
              <li>• 提现需要人工审核，通常1-24小时到账</li>
              <li>• 提现手续费将从您的BNB余额中扣除</li>
            </ul>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* 代币类型选择 */}
        <div>
          <label className="block text-body font-sf-pro font-medium text-label mb-3">
            选择代币
          </label>
          <div className="grid grid-cols-2 gap-3">
            {['HAOX', 'BNB'].map((token) => (
              <button
                key={token}
                type="button"
                onClick={() => setFormData(prev => ({ ...prev, tokenType: token as 'HAOX' | 'BNB' }))}
                className={cn(
                  'p-4 rounded-xl border-2 transition-colors',
                  formData.tokenType === token
                    ? 'border-system-blue bg-system-blue/10'
                    : 'border-system-gray-4 hover:border-system-gray-3'
                )}
              >
                <div className="flex items-center space-x-3">
                  <Icon 
                    icon={token === 'HAOX' ? FinanceIcons.coins : FinanceIcons.barChart} 
                    size="lg" 
                    color={formData.tokenType === token ? 'primary' : 'muted'} 
                  />
                  <div className="text-left">
                    <p className="text-body font-sf-pro font-medium text-label">
                      {token}
                    </p>
                    <p className="text-caption-1 text-secondary-label">
                      余额: {balance ? formatBalance(
                        token === 'HAOX' ? balance.haoxBalance : balance.bnbBalance,
                        token === 'HAOX' ? 4 : 6
                      ) : '0'}
                    </p>
                    <p className="text-caption-1 text-secondary-label">
                      限额: {withdrawLimits[token as 'HAOX' | 'BNB'].min} - {withdrawLimits[token as 'HAOX' | 'BNB'].max}
                    </p>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* 提现地址 */}
        <div>
          <label className="block text-body font-sf-pro font-medium text-label mb-2">
            提现地址
          </label>
          <div className="relative">
            <input
              type="text"
              value={formData.toAddress}
              onChange={(e) => setFormData(prev => ({ ...prev, toAddress: e.target.value }))}
              placeholder="输入外部钱包地址 (0x...)"
              className={cn(
                'w-full px-4 py-3 rounded-xl border-2 bg-system-background font-mono text-body transition-colors',
                errors.toAddress
                  ? 'border-system-red focus:border-system-red'
                  : isValidAddress
                  ? 'border-system-green focus:border-system-green'
                  : 'border-system-gray-4 focus:border-system-blue'
              )}
            />
            {isValidAddress && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <Icon icon={FeatureIcons.checkCircle} size="sm" color="success" />
              </div>
            )}
          </div>
          {errors.toAddress && (
            <p className="text-caption-1 text-system-red mt-1">{errors.toAddress}</p>
          )}
        </div>

        {/* 提现金额 */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <label className="text-body font-sf-pro font-medium text-label">
              提现金额
            </label>
            <button
              type="button"
              onClick={setMaxAmount}
              className="text-caption-1 text-system-blue hover:text-system-blue/80 transition-colors"
            >
              最大金额
            </button>
          </div>
          <div className="relative">
            <input
              type="number"
              value={formData.amount}
              onChange={(e) => setFormData(prev => ({ ...prev, amount: e.target.value }))}
              placeholder="0.00"
              step="any"
              min="0"
              className={cn(
                'w-full px-4 py-3 rounded-xl border-2 bg-system-background text-body transition-colors',
                errors.amount
                  ? 'border-system-red focus:border-system-red'
                  : 'border-system-gray-4 focus:border-system-blue'
              )}
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <span className="text-body text-secondary-label">
                {formData.tokenType}
              </span>
            </div>
          </div>
          {errors.amount && (
            <p className="text-caption-1 text-system-red mt-1">{errors.amount}</p>
          )}
        </div>

        {/* 费用预估 */}
        {isValidAddress && formData.amount && (
          <div className="bg-system-gray-6 rounded-xl p-4">
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-body text-secondary-label">提现手续费</span>
                <span className="text-body text-label">
                  {formatBalance(withdrawFee, 6)} BNB
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-body text-secondary-label">实际到账</span>
                <span className="text-body font-sf-pro font-medium text-label">
                  {formatBalance(formData.amount)} {formData.tokenType}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* 错误信息 */}
        {withdrawError && (
          <div className="bg-system-red/10 border border-system-red/20 rounded-xl p-4">
            <div className="flex items-center space-x-2">
              <Icon icon={ActionIcons.alert} size="sm" color="error" />
              <span className="text-body text-system-red">{withdrawError}</span>
            </div>
          </div>
        )}

        {/* 提交按钮 */}
        <Button
          type="submit"
          variant="primary"
          size="lg"
          disabled={!isValidAddress || !formData.amount || isWithdrawing}
          className="w-full"
        >
          <Icon icon={ActionIcons.download} size="sm" />
          <span>提现 {formData.tokenType}</span>
        </Button>
      </form>
    </Card>
  );
};

export default WithdrawForm;
