'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, 
  QrCode, 
  Clock, 
  CheckCircle, 
  XCircle,
  RefreshCw,
  Copy,
  ExternalLink,
  Smartphone
} from 'lucide-react';
import { Mo<PERSON>, Button } from '@/components/ui';
import { usePayment } from '@/hooks/usePayment';
import { formatCurrency } from '@/lib/utils';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  amount: number;
  currency: 'CNY' | 'USD';
  method: 'alipay' | 'wechat' | 'crypto';
  description?: string;
  onSuccess?: (orderId: string) => void;
  onCancel?: () => void;
}

const PaymentModal: React.FC<PaymentModalProps> = ({
  isOpen,
  onClose,
  amount,
  currency,
  method,
  description,
  onSuccess,
  onCancel,
}) => {
  const {
    isCreatingOrder,
    isCheckingStatus,
    currentOrder,
    createOrder,
    checkPaymentStatus,
    cancelOrder,
    clearOrder,
    getPaymentMethodInfo,
    formatAmount,
    getOrderStatusInfo,
  } = usePayment();

  const [countdown, setCountdown] = useState(300); // 5 minutes
  const [isPolling, setIsPolling] = useState(false);

  const methodInfo = getPaymentMethodInfo(method);

  // Create order when modal opens
  useEffect(() => {
    if (isOpen && !currentOrder) {
      createOrder({
        amount,
        currency,
        method,
        description,
      });
    }
  }, [isOpen]);

  // Start countdown and polling when order is created
  useEffect(() => {
    if (currentOrder && currentOrder.status === 'pending') {
      setCountdown(300);
      setIsPolling(true);

      const countdownInterval = setInterval(() => {
        setCountdown(prev => {
          if (prev <= 1) {
            clearInterval(countdownInterval);
            handleTimeout();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);

      const pollingInterval = setInterval(() => {
        if (currentOrder.status === 'pending') {
          checkPaymentStatus(currentOrder.orderId);
        }
      }, 3000);

      return () => {
        clearInterval(countdownInterval);
        clearInterval(pollingInterval);
      };
    }
  }, [currentOrder]);

  // Handle payment success
  useEffect(() => {
    if (currentOrder && currentOrder.status === 'paid') {
      setIsPolling(false);
      if (onSuccess) {
        onSuccess(currentOrder.orderId);
      }
      setTimeout(() => {
        handleClose();
      }, 2000);
    }
  }, [currentOrder]);

  const handleTimeout = async () => {
    if (currentOrder) {
      await cancelOrder(currentOrder.orderId);
    }
    setIsPolling(false);
  };

  const handleClose = () => {
    setIsPolling(false);
    clearOrder();
    onClose();
  };

  const handleCancel = async () => {
    if (currentOrder && currentOrder.status === 'pending') {
      await cancelOrder(currentOrder.orderId);
    }
    if (onCancel) {
      onCancel();
    }
    handleClose();
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const statusInfo = currentOrder ? getOrderStatusInfo(currentOrder.status) : null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      size="md"
      closeOnOverlayClick={false}
      showCloseButton={false}
    >
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className={`w-10 h-10 rounded-xl flex items-center justify-center bg-system-gray-6`}>
              <span className="text-2xl">{methodInfo.icon}</span>
            </div>
            <div>
              <h3 className="text-title-3 font-sf-pro font-bold text-label">
                {methodInfo.name}支付
              </h3>
              <p className="text-body text-secondary-label">
                {formatAmount(amount, currency)}
              </p>
            </div>
          </div>
          
          <button
            onClick={handleClose}
            className="p-2 hover:bg-system-gray-6 rounded-xl transition-colors"
          >
            <X className="w-5 h-5 text-secondary-label" />
          </button>
        </div>

        {/* Loading State */}
        {isCreatingOrder && (
          <div className="text-center py-8">
            <div className="w-12 h-12 border-4 border-system-blue border-t-transparent rounded-full animate-spin mx-auto mb-4" />
            <p className="text-body text-secondary-label">正在创建订单...</p>
          </div>
        )}

        {/* Payment Content */}
        {currentOrder && !isCreatingOrder && (
          <div className="space-y-6">
            {/* Status Banner */}
            {statusInfo && (
              <div className={`p-4 rounded-xl border ${statusInfo.bgColor} ${statusInfo.borderColor}`}>
                <div className="flex items-center space-x-3">
                  {currentOrder.status === 'pending' && <Clock className={`w-5 h-5 ${statusInfo.color}`} />}
                  {currentOrder.status === 'paid' && <CheckCircle className={`w-5 h-5 ${statusInfo.color}`} />}
                  {currentOrder.status === 'failed' && <XCircle className={`w-5 h-5 ${statusInfo.color}`} />}
                  {currentOrder.status === 'cancelled' && <XCircle className={`w-5 h-5 ${statusInfo.color}`} />}
                  
                  <div>
                    <p className={`text-body font-sf-pro font-semibold ${statusInfo.color}`}>
                      {statusInfo.text}
                    </p>
                    {currentOrder.status === 'pending' && (
                      <p className="text-caption-1 text-secondary-label">
                        请在 {formatTime(countdown)} 内完成支付
                      </p>
                    )}
                    {currentOrder.status === 'paid' && (
                      <p className="text-caption-1 text-secondary-label">
                        支付成功，正在处理交易...
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* QR Code */}
            {currentOrder.status === 'pending' && currentOrder.qrCode && (
              <div className="text-center">
                <div className="inline-block p-4 bg-white rounded-2xl shadow-apple">
                  <img
                    src={currentOrder.qrCode}
                    alt="支付二维码"
                    className="w-48 h-48 mx-auto"
                  />
                </div>
                <p className="text-caption-1 text-secondary-label mt-3">
                  {methodInfo.description}
                </p>
              </div>
            )}

            {/* Payment Instructions */}
            {currentOrder.status === 'pending' && (
              <div className="bg-system-gray-6 rounded-xl p-4">
                <h4 className="text-body font-sf-pro font-semibold text-label mb-3 flex items-center space-x-2">
                  <Smartphone className="w-4 h-4" />
                  <span>支付步骤</span>
                </h4>
                <ol className="space-y-2">
                  {methodInfo.instructions.map((instruction, index) => (
                    <li key={index} className="flex items-start space-x-3">
                      <span className="flex-shrink-0 w-5 h-5 bg-system-blue text-white rounded-full flex items-center justify-center text-xs font-bold">
                        {index + 1}
                      </span>
                      <span className="text-body text-secondary-label">
                        {instruction}
                      </span>
                    </li>
                  ))}
                </ol>
              </div>
            )}

            {/* Order Info */}
            <div className="bg-system-gray-6 rounded-xl p-4">
              <h4 className="text-body font-sf-pro font-semibold text-label mb-3">
                订单信息
              </h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-body text-secondary-label">订单号</span>
                  <div className="flex items-center space-x-2">
                    <span className="text-body font-mono font-medium text-label">
                      {currentOrder.orderId}
                    </span>
                    <button
                      onClick={() => copyToClipboard(currentOrder.orderId)}
                      className="p-1 hover:bg-system-gray-5 rounded transition-colors"
                    >
                      <Copy className="w-3 h-3 text-secondary-label" />
                    </button>
                  </div>
                </div>
                <div className="flex justify-between">
                  <span className="text-body text-secondary-label">支付金额</span>
                  <span className="text-body font-sf-pro font-semibold text-label">
                    {formatAmount(currentOrder.amount, currentOrder.currency)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-body text-secondary-label">支付方式</span>
                  <span className="text-body font-sf-pro font-medium text-label">
                    {methodInfo.name}
                  </span>
                </div>
                {description && (
                  <div className="flex justify-between">
                    <span className="text-body text-secondary-label">商品描述</span>
                    <span className="text-body font-sf-pro font-medium text-label">
                      {description}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-3">
              {currentOrder.status === 'pending' && (
                <>
                  <Button
                    onClick={() => checkPaymentStatus(currentOrder.orderId)}
                    disabled={isCheckingStatus}
                    loading={isCheckingStatus}
                    variant="outline"
                    className="flex-1"
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    刷新状态
                  </Button>
                  
                  {currentOrder.paymentUrl && (
                    <Button
                      onClick={() => window.open(currentOrder.paymentUrl, '_blank')}
                      className="flex-1"
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      打开{methodInfo.name}
                    </Button>
                  )}
                </>
              )}
              
              {currentOrder.status === 'pending' && (
                <Button
                  onClick={handleCancel}
                  variant="outline"
                  className="flex-1 text-system-red border-system-red hover:bg-system-red hover:text-white"
                >
                  取消支付
                </Button>
              )}
              
              {(currentOrder.status === 'paid' || currentOrder.status === 'failed' || currentOrder.status === 'cancelled') && (
                <Button
                  onClick={handleClose}
                  className="flex-1"
                >
                  关闭
                </Button>
              )}
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default PaymentModal;
