'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Shield, 
  FileText, 
  User, 
  Phone, 
  Mail, 
  Building,
  AlertCircle,
  CheckCircle,
  Clock,
  XCircle
} from 'lucide-react';
import { Button, Card, Input, ConfirmModal } from '@/components/ui';
import { useMerchant } from '@/hooks/useMerchant';
import type { MerchantApplicationForm as FormData } from '@/types';

interface MerchantApplicationFormProps {
  userId?: string;
  className?: string;
}

const MerchantApplicationForm: React.FC<MerchantApplicationFormProps> = ({ 
  userId, 
  className 
}) => {
  const {
    application,
    isLoading,
    isSubmitting,
    submitApplication,
    updateApplication,
    cancelApplication,
    getStatusInfo,
    getFormData,
  } = useMerchant(userId);

  const [formData, setFormData] = useState<FormData>({
    businessName: '',
    businessType: '',
    businessLicense: '',
    contactPerson: '',
    contactPhone: '',
    contactEmail: '',
    description: '',
  });

  const [errors, setErrors] = useState<Partial<FormData>>({});
  const [cancelModal, setCancelModal] = useState(false);

  const statusInfo = getStatusInfo();

  // Load form data when application changes
  useEffect(() => {
    setFormData(getFormData());
  }, [application]);

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined,
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<FormData> = {};

    if (!formData.businessName.trim()) {
      newErrors.businessName = '请输入企业名称';
    }

    if (!formData.businessType.trim()) {
      newErrors.businessType = '请输入企业类型';
    }

    if (!formData.businessLicense.trim()) {
      newErrors.businessLicense = '请输入营业执照号';
    }

    if (!formData.contactPerson.trim()) {
      newErrors.contactPerson = '请输入联系人姓名';
    }

    if (!formData.contactPhone.trim()) {
      newErrors.contactPhone = '请输入联系电话';
    } else if (!/^1[3-9]\d{9}$/.test(formData.contactPhone)) {
      newErrors.contactPhone = '请输入有效的手机号码';
    }

    if (!formData.contactEmail.trim()) {
      newErrors.contactEmail = '请输入联系邮箱';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.contactEmail)) {
      newErrors.contactEmail = '请输入有效的邮箱地址';
    }

    if (!formData.description.trim()) {
      newErrors.description = '请输入企业描述';
    } else if (formData.description.length < 50) {
      newErrors.description = '企业描述至少需要50个字符';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    const success = application 
      ? await updateApplication(formData)
      : await submitApplication(formData);

    if (success) {
      // Form will be updated automatically through the hook
    }
  };

  const handleCancel = async () => {
    const success = await cancelApplication();
    if (success) {
      setFormData({
        businessName: '',
        businessType: '',
        businessLicense: '',
        contactPerson: '',
        contactPhone: '',
        contactEmail: '',
        description: '',
      });
    }
    setCancelModal(false);
  };

  const getStatusIcon = () => {
    switch (statusInfo.status) {
      case 'pending':
        return <Clock className="w-5 h-5 text-system-orange" />;
      case 'approved':
        return <CheckCircle className="w-5 h-5 text-system-green" />;
      case 'rejected':
        return <XCircle className="w-5 h-5 text-system-red" />;
      default:
        return <Shield className="w-5 h-5 text-system-blue" />;
    }
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <div className="animate-pulse space-y-4">
          {[1, 2, 3, 4, 5].map((i) => (
            <div key={i} className="h-16 bg-system-gray-5 rounded-xl" />
          ))}
        </div>
      </Card>
    );
  }

  return (
    <>
      <Card className={className}>
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-title-3 font-sf-pro font-bold text-label">
            商家认证申请
          </h3>
          <div className="flex items-center space-x-2">
            {getStatusIcon()}
            <span className={`text-body font-sf-pro font-medium ${statusInfo.statusColor}`}>
              {statusInfo.statusText}
            </span>
          </div>
        </div>

        {/* Status Message */}
        {application && (
          <div className={`p-4 rounded-xl mb-6 ${
            statusInfo.status === 'pending' ? 'bg-system-orange/5 border border-system-orange/20' :
            statusInfo.status === 'approved' ? 'bg-system-green/5 border border-system-green/20' :
            statusInfo.status === 'rejected' ? 'bg-system-red/5 border border-system-red/20' :
            'bg-system-gray-6'
          }`}>
            <div className="flex items-start space-x-3">
              {getStatusIcon()}
              <div>
                <h4 className="text-body font-sf-pro font-semibold text-label mb-1">
                  {statusInfo.status === 'pending' && '申请审核中'}
                  {statusInfo.status === 'approved' && '恭喜！申请已通过'}
                  {statusInfo.status === 'rejected' && '申请被拒绝'}
                </h4>
                <p className="text-caption-1 text-secondary-label">
                  {statusInfo.status === 'pending' && '我们正在审核您的申请，通常需要3-5个工作日。'}
                  {statusInfo.status === 'approved' && '您现在是认证商家，可以享受专属特权和更低的交易费率。'}
                  {statusInfo.status === 'rejected' && application?.rejection_reason && `拒绝原因：${application.rejection_reason}`}
                </p>
                {statusInfo.status === 'rejected' && (
                  <p className="text-caption-1 text-secondary-label mt-1">
                    您可以修改申请信息后重新提交。
                  </p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Application Form */}
        {statusInfo.canEdit && (
          <div className="space-y-6">
            {/* Basic Information */}
            <div>
              <h4 className="text-headline font-sf-pro font-semibold text-label mb-4 flex items-center space-x-2">
                <Building className="w-5 h-5 text-system-blue" />
                <span>企业信息</span>
              </h4>
              
              <div className="grid md:grid-cols-2 gap-4">
                <Input
                  label="企业名称"
                  value={formData.businessName}
                  onChange={(value) => handleInputChange('businessName', value)}
                  placeholder="请输入企业全称"
                  error={errors.businessName}
                  required
                />

                <Input
                  label="企业类型"
                  value={formData.businessType}
                  onChange={(value) => handleInputChange('businessType', value)}
                  placeholder="如：科技公司、投资机构等"
                  error={errors.businessType}
                  required
                />

                <Input
                  label="营业执照号"
                  value={formData.businessLicense}
                  onChange={(value) => handleInputChange('businessLicense', value)}
                  placeholder="请输入营业执照注册号"
                  error={errors.businessLicense}
                  required
                />
              </div>
            </div>

            {/* Contact Information */}
            <div>
              <h4 className="text-headline font-sf-pro font-semibold text-label mb-4 flex items-center space-x-2">
                <User className="w-5 h-5 text-system-blue" />
                <span>联系信息</span>
              </h4>
              
              <div className="grid md:grid-cols-2 gap-4">
                <Input
                  label="联系人姓名"
                  value={formData.contactPerson}
                  onChange={(value) => handleInputChange('contactPerson', value)}
                  placeholder="请输入负责人姓名"
                  error={errors.contactPerson}
                  required
                />

                <Input
                  label="联系电话"
                  type="tel"
                  value={formData.contactPhone}
                  onChange={(value) => handleInputChange('contactPhone', value)}
                  placeholder="请输入联系电话"
                  error={errors.contactPhone}
                  required
                />

                <Input
                  label="联系邮箱"
                  type="email"
                  value={formData.contactEmail}
                  onChange={(value) => handleInputChange('contactEmail', value)}
                  placeholder="请输入联系邮箱"
                  error={errors.contactEmail}
                  required
                />
              </div>
            </div>

            {/* Description */}
            <div>
              <h4 className="text-headline font-sf-pro font-semibold text-label mb-4 flex items-center space-x-2">
                <FileText className="w-5 h-5 text-system-blue" />
                <span>企业描述</span>
              </h4>
              
              <div>
                <label className="block text-sm font-medium font-sf-pro mb-2 text-label">
                  详细描述 <span className="text-system-red">*</span>
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="请详细描述您的企业背景、主营业务、团队规模等信息（至少50个字符）"
                  rows={4}
                  className={`w-full px-4 py-3 font-sf-pro text-body bg-system-background border-2 rounded-xl transition-all duration-200 focus:outline-none resize-none ${
                    errors.description 
                      ? 'border-system-red focus:border-system-red' 
                      : 'border-system-gray-4 focus:border-system-blue'
                  } placeholder:text-system-gray`}
                />
                {errors.description && (
                  <p className="mt-2 text-sm text-system-red font-sf-pro">
                    {errors.description}
                  </p>
                )}
                <p className="mt-2 text-caption-1 text-secondary-label">
                  {formData.description.length}/500 字符
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button
                onClick={handleSubmit}
                disabled={isSubmitting}
                loading={isSubmitting}
                className="flex-1"
              >
                {application ? '更新申请' : '提交申请'}
              </Button>
              
              {statusInfo.canCancel && (
                <Button
                  variant="outline"
                  onClick={() => setCancelModal(true)}
                  className="flex-1 text-system-red border-system-red hover:bg-system-red hover:text-white"
                >
                  取消申请
                </Button>
              )}
            </div>
          </div>
        )}

        {/* Read-only view for approved applications */}
        {statusInfo.status === 'approved' && !statusInfo.canEdit && (
          <div className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium font-sf-pro mb-1 text-secondary-label">
                  企业名称
                </label>
                <p className="text-body font-sf-pro text-label">{formData.businessName}</p>
              </div>
              <div>
                <label className="block text-sm font-medium font-sf-pro mb-1 text-secondary-label">
                  企业类型
                </label>
                <p className="text-body font-sf-pro text-label">{formData.businessType}</p>
              </div>
              <div>
                <label className="block text-sm font-medium font-sf-pro mb-1 text-secondary-label">
                  联系人
                </label>
                <p className="text-body font-sf-pro text-label">{formData.contactPerson}</p>
              </div>
              <div>
                <label className="block text-sm font-medium font-sf-pro mb-1 text-secondary-label">
                  联系电话
                </label>
                <p className="text-body font-sf-pro text-label">{formData.contactPhone}</p>
              </div>
            </div>
          </div>
        )}
      </Card>

      {/* Cancel Confirmation Modal */}
      <ConfirmModal
        isOpen={cancelModal}
        onClose={() => setCancelModal(false)}
        onConfirm={handleCancel}
        title="取消申请"
        message="确定要取消商家认证申请吗？取消后您需要重新填写申请信息。"
        confirmText="确认取消"
        cancelText="继续申请"
        type="warning"
      />
    </>
  );
};

export default MerchantApplicationForm;
