'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { TrendingUp, TrendingDown, RefreshCw } from 'lucide-react';
import { Card } from '@/components/ui';
import { formatCurrency, formatPercentage } from '@/lib/utils';

interface PriceData {
  price: number;
  change24h: number;
  volume24h: number;
  marketCap: number;
  high24h: number;
  low24h: number;
  lastUpdated: string;
}

interface HAOXPriceCardProps {
  className?: string;
}

const HAOXPriceCard: React.FC<HAOXPriceCardProps> = ({ className }) => {
  const [priceData, setPriceData] = useState<PriceData>({
    price: 0.25,
    change24h: 5.67,
    volume24h: 12500000,
    marketCap: 1250000000,
    high24h: 0.268,
    low24h: 0.235,
    lastUpdated: new Date().toISOString(),
  });
  
  const [isLoading, setIsLoading] = useState(false);

  // Mock price update function
  const updatePrice = async () => {
    setIsLoading(true);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Generate mock price data with small random changes
    const basePrice = 0.25;
    const priceVariation = (Math.random() - 0.5) * 0.02; // ±1% variation
    const newPrice = Math.max(0.01, basePrice + priceVariation);
    
    const change24h = (Math.random() - 0.5) * 20; // ±10% change
    
    setPriceData({
      price: newPrice,
      change24h,
      volume24h: 12500000 + (Math.random() - 0.5) * 2000000,
      marketCap: newPrice * 5000000000, // 50亿总供应量
      high24h: newPrice * (1 + Math.random() * 0.1),
      low24h: newPrice * (1 - Math.random() * 0.1),
      lastUpdated: new Date().toISOString(),
    });
    
    setIsLoading(false);
  };

  // Auto-update price every 30 seconds
  useEffect(() => {
    const interval = setInterval(updatePrice, 30000);
    return () => clearInterval(interval);
  }, []);

  const isPositive = priceData.change24h >= 0;

  return (
    <Card className={className}>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-gradient-to-br from-system-blue to-system-purple rounded-xl flex items-center justify-center">
            <span className="text-white font-sf-pro font-bold text-lg">H</span>
          </div>
          <div>
            <h2 className="text-title-3 font-sf-pro font-bold text-label">
              HAOX
            </h2>
            <p className="text-caption-1 text-secondary-label">
              HAOX Token
            </p>
          </div>
        </div>
        
        <button
          onClick={updatePrice}
          disabled={isLoading}
          className="p-2 rounded-xl hover:bg-system-gray-6 transition-colors disabled:opacity-50"
        >
          <RefreshCw className={`w-5 h-5 text-secondary-label ${isLoading ? 'animate-spin' : ''}`} />
        </button>
      </div>

      {/* Price Display */}
      <div className="mb-6">
        <div className="flex items-baseline space-x-3 mb-2">
          <motion.span
            key={priceData.price}
            initial={{ scale: 1.1, color: isPositive ? '#34C759' : '#FF3B30' }}
            animate={{ scale: 1, color: '#000000' }}
            transition={{ duration: 0.3 }}
            className="text-title-1 font-sf-pro font-bold text-label"
          >
            {formatCurrency(priceData.price, 'CNY')}
          </motion.span>
          
          <div className={`flex items-center space-x-1 ${
            isPositive ? 'text-system-green' : 'text-system-red'
          }`}>
            {isPositive ? (
              <TrendingUp className="w-5 h-5" />
            ) : (
              <TrendingDown className="w-5 h-5" />
            )}
            <span className="text-headline font-sf-pro font-semibold">
              {formatPercentage(priceData.change24h)}
            </span>
          </div>
        </div>
        
        <p className="text-caption-1 text-secondary-label">
          最后更新: {new Date(priceData.lastUpdated).toLocaleTimeString('zh-CN')}
        </p>
      </div>

      {/* Market Stats */}
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-4">
          <div>
            <p className="text-caption-1 text-secondary-label mb-1">24小时交易量</p>
            <p className="text-body font-sf-pro font-semibold text-label">
              {formatCurrency(priceData.volume24h)}
            </p>
          </div>
          
          <div>
            <p className="text-caption-1 text-secondary-label mb-1">24小时最高</p>
            <p className="text-body font-sf-pro font-semibold text-label">
              {formatCurrency(priceData.high24h, 'CNY')}
            </p>
          </div>
        </div>
        
        <div className="space-y-4">
          <div>
            <p className="text-caption-1 text-secondary-label mb-1">市值</p>
            <p className="text-body font-sf-pro font-semibold text-label">
              {formatCurrency(priceData.marketCap)}
            </p>
          </div>
          
          <div>
            <p className="text-caption-1 text-secondary-label mb-1">24小时最低</p>
            <p className="text-body font-sf-pro font-semibold text-label">
              {formatCurrency(priceData.low24h, 'CNY')}
            </p>
          </div>
        </div>
      </div>

      {/* Price Chart Placeholder */}
      <div className="mt-6 pt-6 border-t border-system-gray-4">
        <div className="h-32 bg-system-gray-6 rounded-xl flex items-center justify-center">
          <div className="text-center">
            <TrendingUp className="w-8 h-8 text-system-gray mx-auto mb-2" />
            <p className="text-caption-1 text-secondary-label">
              价格图表功能开发中
            </p>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default HAOXPriceCard;
