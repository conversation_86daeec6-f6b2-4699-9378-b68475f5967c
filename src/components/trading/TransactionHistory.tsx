'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  History, 
  TrendingUp, 
  TrendingDown, 
  Filter, 
  Search,
  ExternalLink,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import { Card, Input, Button } from '@/components/ui';
import { formatCurrency, formatRelativeTime, truncateAddress } from '@/lib/utils';

interface Transaction {
  id: string;
  type: 'buy' | 'sell';
  amount: string;
  price: number;
  total: number;
  fees: number;
  paymentMethod: 'alipay' | 'wechat' | 'crypto';
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  txHash?: string;
  createdAt: string;
  updatedAt: string;
}

interface TransactionHistoryProps {
  transactions?: Transaction[];
  className?: string;
}

const TransactionHistory: React.FC<TransactionHistoryProps> = ({ 
  transactions = [], 
  className 
}) => {
  const [filter, setFilter] = useState<'all' | 'buy' | 'sell'>('all');
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'completed' | 'failed'>('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Mock transaction data
  const mockTransactions: Transaction[] = [
    {
      id: '1',
      type: 'buy',
      amount: '1000',
      price: 0.25,
      total: 250,
      fees: 0.5,
      paymentMethod: 'alipay',
      status: 'completed',
      txHash: '******************************************',
      createdAt: '2024-01-20T10:30:00Z',
      updatedAt: '2024-01-20T10:35:00Z',
    },
    {
      id: '2',
      type: 'sell',
      amount: '500',
      price: 0.26,
      total: 130,
      fees: 0.26,
      paymentMethod: 'wechat',
      status: 'completed',
      txHash: '******************************************',
      createdAt: '2024-01-19T15:45:00Z',
      updatedAt: '2024-01-19T15:50:00Z',
    },
    {
      id: '3',
      type: 'buy',
      amount: '2000',
      price: 0.24,
      total: 480,
      fees: 0.96,
      paymentMethod: 'crypto',
      status: 'pending',
      createdAt: '2024-01-18T09:15:00Z',
      updatedAt: '2024-01-18T09:15:00Z',
    },
    {
      id: '4',
      type: 'sell',
      amount: '750',
      price: 0.23,
      total: 172.5,
      fees: 0.345,
      paymentMethod: 'alipay',
      status: 'failed',
      createdAt: '2024-01-17T14:20:00Z',
      updatedAt: '2024-01-17T14:25:00Z',
    },
  ];

  const allTransactions = transactions.length > 0 ? transactions : mockTransactions;

  // Filter transactions
  const filteredTransactions = allTransactions.filter(tx => {
    const matchesType = filter === 'all' || tx.type === filter;
    const matchesStatus = statusFilter === 'all' || tx.status === statusFilter;
    const matchesSearch = searchTerm === '' || 
      tx.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      tx.txHash?.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesType && matchesStatus && matchesSearch;
  });

  const getStatusIcon = (status: Transaction['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-system-green" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-system-orange" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-system-red" />;
      case 'cancelled':
        return <AlertCircle className="w-4 h-4 text-system-gray" />;
      default:
        return <Clock className="w-4 h-4 text-system-gray" />;
    }
  };

  const getStatusText = (status: Transaction['status']) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'pending':
        return '处理中';
      case 'failed':
        return '失败';
      case 'cancelled':
        return '已取消';
      default:
        return '未知';
    }
  };

  const getPaymentMethodText = (method: Transaction['paymentMethod']) => {
    switch (method) {
      case 'alipay':
        return '支付宝';
      case 'wechat':
        return '微信支付';
      case 'crypto':
        return '加密货币';
      default:
        return '未知';
    }
  };

  return (
    <Card title="交易历史" className={className}>
      {/* Filters */}
      <div className="space-y-4 mb-6">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-secondary-label" />
          <input
            type="text"
            placeholder="搜索交易ID或哈希..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-system-gray-6 border border-system-gray-4 rounded-xl text-body font-sf-pro focus:outline-none focus:border-system-blue transition-colors"
          />
        </div>

        {/* Filter Buttons */}
        <div className="flex flex-wrap gap-3">
          {/* Type Filter */}
          <div className="flex bg-system-gray-6 rounded-lg p-1">
            {[
              { key: 'all', label: '全部' },
              { key: 'buy', label: '买入' },
              { key: 'sell', label: '卖出' },
            ].map((option) => (
              <button
                key={option.key}
                onClick={() => setFilter(option.key as any)}
                className={`px-3 py-1 rounded-md text-sm font-sf-pro font-medium transition-colors ${
                  filter === option.key
                    ? 'bg-system-blue text-white'
                    : 'text-secondary-label hover:text-label'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>

          {/* Status Filter */}
          <div className="flex bg-system-gray-6 rounded-lg p-1">
            {[
              { key: 'all', label: '全部状态' },
              { key: 'completed', label: '已完成' },
              { key: 'pending', label: '处理中' },
              { key: 'failed', label: '失败' },
            ].map((option) => (
              <button
                key={option.key}
                onClick={() => setStatusFilter(option.key as any)}
                className={`px-3 py-1 rounded-md text-sm font-sf-pro font-medium transition-colors ${
                  statusFilter === option.key
                    ? 'bg-system-blue text-white'
                    : 'text-secondary-label hover:text-label'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Transaction List */}
      <div className="space-y-3">
        {filteredTransactions.length === 0 ? (
          <div className="text-center py-8">
            <History className="w-12 h-12 text-system-gray mx-auto mb-3" />
            <p className="text-body text-secondary-label">
              {searchTerm || filter !== 'all' || statusFilter !== 'all' 
                ? '没有找到匹配的交易记录' 
                : '暂无交易记录'
              }
            </p>
          </div>
        ) : (
          filteredTransactions.map((tx, index) => (
            <motion.div
              key={tx.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
              className="p-4 border border-system-gray-4 rounded-xl hover:border-system-blue/50 transition-colors"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {/* Type Icon */}
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                    tx.type === 'buy' ? 'bg-system-green/10' : 'bg-system-red/10'
                  }`}>
                    {tx.type === 'buy' ? (
                      <TrendingUp className="w-5 h-5 text-system-green" />
                    ) : (
                      <TrendingDown className="w-5 h-5 text-system-red" />
                    )}
                  </div>

                  {/* Transaction Info */}
                  <div>
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="text-body font-sf-pro font-semibold text-label">
                        {tx.type === 'buy' ? '买入' : '卖出'} {formatCurrency(parseFloat(tx.amount))} HAOX
                      </span>
                      {getStatusIcon(tx.status)}
                    </div>
                    <div className="flex items-center space-x-3 text-caption-1 text-secondary-label">
                      <span>单价: {formatCurrency(tx.price, 'CNY')}</span>
                      <span>•</span>
                      <span>{getPaymentMethodText(tx.paymentMethod)}</span>
                      <span>•</span>
                      <span>{formatRelativeTime(tx.createdAt)}</span>
                    </div>
                  </div>
                </div>

                {/* Amount and Status */}
                <div className="text-right">
                  <p className="text-body font-sf-pro font-semibold text-label mb-1">
                    {formatCurrency(tx.total)}
                  </p>
                  <div className="flex items-center space-x-2">
                    <span className={`text-caption-1 ${
                      tx.status === 'completed' ? 'text-system-green' :
                      tx.status === 'pending' ? 'text-system-orange' :
                      tx.status === 'failed' ? 'text-system-red' :
                      'text-system-gray'
                    }`}>
                      {getStatusText(tx.status)}
                    </span>
                    {tx.txHash && (
                      <button
                        onClick={() => window.open(`https://etherscan.io/tx/${tx.txHash}`, '_blank')}
                        className="p-1 hover:bg-system-gray-6 rounded transition-colors"
                        title="查看区块链交易"
                      >
                        <ExternalLink className="w-3 h-3 text-secondary-label" />
                      </button>
                    )}
                  </div>
                </div>
              </div>

              {/* Transaction Hash */}
              {tx.txHash && (
                <div className="mt-3 pt-3 border-t border-system-gray-4">
                  <div className="flex items-center justify-between">
                    <span className="text-caption-1 text-secondary-label">交易哈希:</span>
                    <span className="text-caption-1 font-mono text-secondary-label">
                      {truncateAddress(tx.txHash, 8, 8)}
                    </span>
                  </div>
                </div>
              )}
            </motion.div>
          ))
        )}
      </div>

      {/* Load More Button */}
      {filteredTransactions.length > 0 && (
        <div className="text-center mt-6">
          <Button variant="outline" size="sm">
            加载更多
          </Button>
        </div>
      )}
    </Card>
  );
};

export default TransactionHistory;
