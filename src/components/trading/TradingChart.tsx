'use client';

import React, { useEffect, useRef, useState } from 'react';
import { createChart, ColorType, IChartApi, ISeriesApi } from 'lightweight-charts';
import { Card, Button } from '@/components/ui';
import { TrendingUp, TrendingDown, BarChart3, Settings, Wifi, WifiOff } from 'lucide-react';
import { usePriceData } from '@/hooks/usePriceData';

interface TradingChartProps {
  symbol?: string;
  height?: number;
  enableRealTime?: boolean;
}

const TradingChart: React.FC<TradingChartProps> = ({
  symbol = 'HAOX',
  height = 400,
  enableRealTime = true
}) => {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<IChartApi | null>(null);
  const candlestickSeriesRef = useRef<ISeriesApi<'Candlestick'> | null>(null);
  const volumeSeriesRef = useRef<ISeriesApi<'Histogram'> | null>(null);
  const maSeriesRef = useRef<ISeriesApi<'Line'> | null>(null);

  const [timeframe, setTimeframe] = useState('1D');
  const [showMA, setShowMA] = useState(true);
  const [maPeriod, setMaPeriod] = useState(20);

  // 使用价格数据 Hook
  const {
    chartData,
    currentPrice,
    priceChange24h,
    volume24h,
    isLoading,
    error,
    isConnected,
    calculateMA,
    setPriceAlert
  } = usePriceData({
    symbol,
    timeframe,
    enableRealTime,
    refetchInterval: 30000
  });

  // 处理价格预警设置
  const handleSetPriceAlert = async (targetPrice: number, alertType: 'above' | 'below') => {
    try {
      await setPriceAlert(targetPrice, alertType);
      // 这里可以显示成功提示
      console.log('Price alert set successfully');
    } catch (error) {
      console.error('Failed to set price alert:', error);
      // 这里可以显示错误提示
    }
  };

  useEffect(() => {
    if (!chartContainerRef.current || !chartData) return;

    // 创建图表
    const chart = createChart(chartContainerRef.current, {
      layout: {
        background: { type: ColorType.Solid, color: 'transparent' },
        textColor: '#333',
      },
      width: chartContainerRef.current.clientWidth,
      height: height,
      grid: {
        vertLines: { color: '#f0f0f0' },
        horzLines: { color: '#f0f0f0' },
      },
      crosshair: {
        mode: 1,
      },
      rightPriceScale: {
        borderColor: '#e0e0e0',
      },
      timeScale: {
        borderColor: '#e0e0e0',
        timeVisible: true,
        secondsVisible: false,
      },
    });

    chartRef.current = chart;

    // 创建K线图系列
    const candlestickSeries = (chart as any).addCandlestickSeries({
      upColor: '#00C853',
      downColor: '#FF1744',
      borderDownColor: '#FF1744',
      borderUpColor: '#00C853',
      wickDownColor: '#FF1744',
      wickUpColor: '#00C853',
    });

    candlestickSeriesRef.current = candlestickSeries;

    // 创建成交量系列
    const volumeSeries = (chart as any).addHistogramSeries({
      color: '#26a69a',
      priceFormat: {
        type: 'volume',
      },
      priceScaleId: '',
      scaleMargins: {
        top: 0.8,
        bottom: 0,
      },
    });

    volumeSeriesRef.current = volumeSeries;

    // 创建移动平均线系列
    let maSeries: any = null;
    if (showMA) {
      maSeries = (chart as any).addLineSeries({
        color: '#2196F3',
        lineWidth: 2,
        title: `MA(${maPeriod})`,
      });
      maSeriesRef.current = maSeries;
    }

    // 设置数据
    candlestickSeries.setData(chartData.candlestick);
    volumeSeries.setData(chartData.volume);

    // 设置移动平均线数据
    if (showMA && maSeries) {
      const maData = calculateMA(maPeriod);
      maSeries.setData(maData);
    }

    // 响应式处理
    const handleResize = () => {
      if (chartContainerRef.current && chart) {
        chart.applyOptions({
          width: chartContainerRef.current.clientWidth
        });
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      chart.remove();
    };
  }, [chartData, height, showMA, maPeriod, calculateMA]);

  const timeframes = ['1H', '4H', '1D', '1W', '1M'];

  return (
    <Card className="p-6">
      {/* 图表头部 */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <h3 className="text-lg font-semibold">{symbol}/USDT</h3>
            {enableRealTime && (
              <div className={`flex items-center space-x-1 ${
                isConnected ? 'text-green-600' : 'text-red-600'
              }`}>
                {isConnected ? (
                  <Wifi className="w-3 h-3" />
                ) : (
                  <WifiOff className="w-3 h-3" />
                )}
                <span className="text-xs">
                  {isConnected ? '实时' : '离线'}
                </span>
              </div>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-2xl font-bold">
              ${currentPrice.toFixed(6)}
            </span>
            <div className={`flex items-center space-x-1 ${
              priceChange24h >= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              {priceChange24h >= 0 ? (
                <TrendingUp className="w-4 h-4" />
              ) : (
                <TrendingDown className="w-4 h-4" />
              )}
              <span className="text-sm font-medium">
                {priceChange24h >= 0 ? '+' : ''}{priceChange24h.toFixed(2)}%
              </span>
            </div>
          </div>
          <div className="text-sm text-gray-500">
            <div>24h 成交量: {(volume24h / 1000000).toFixed(2)}M</div>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant={showMA ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setShowMA(!showMA)}
          >
            MA({maPeriod})
          </Button>
          <Button variant="ghost" size="sm">
            <Settings className="w-4 h-4" />
          </Button>
          <Button variant="ghost" size="sm">
            <BarChart3 className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* 时间周期选择 */}
      <div className="flex space-x-1 mb-4">
        {timeframes.map((tf) => (
          <Button
            key={tf}
            variant={timeframe === tf ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setTimeframe(tf)}
            className="px-3 py-1 text-xs"
          >
            {tf}
          </Button>
        ))}
      </div>

      {/* 图表容器 */}
      <div className="relative">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white/80 z-10">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
              <span className="text-sm text-gray-600">加载图表数据...</span>
            </div>
          </div>
        )}

        {error && (
          <div className="absolute inset-0 flex items-center justify-center bg-white/80 z-10">
            <div className="text-center">
              <div className="text-red-500 mb-2">数据加载失败</div>
              <Button size="sm" onClick={() => window.location.reload()}>
                重试
              </Button>
            </div>
          </div>
        )}

        <div
          ref={chartContainerRef}
          className="w-full"
          style={{ height: `${height}px` }}
        />
      </div>

      {/* 图表说明 */}
      <div className="mt-4 text-xs text-gray-500 flex items-center justify-between">
        <span>
          数据更新时间：{new Date().toLocaleTimeString()}
          {enableRealTime && isConnected && ' (实时)'}
        </span>
        <span>由 Lightweight Charts 提供技术支持</span>
      </div>
    </Card>
  );
};

export default TradingChart;
