'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Calculator, AlertCircle, CheckCircle } from 'lucide-react';
import { Button, Card, Input, useToast } from '@/components/ui';
import { useWallet } from '@/hooks/useWallet';
// import { useHAOX } from '@/hooks/useHAOX'; // 暂时注释，等待重构
import { formatCurrency, formatNumber } from '@/lib/utils';
import { PAYMENT_METHODS } from '@/constants';

interface TradeFormProps {
  currentPrice: number;
  onTrade?: (tradeData: any) => void;
}

const TradeForm: React.FC<TradeFormProps> = ({ currentPrice, onTrade }) => {
  const { isConnected, address } = useWallet();
  // const { balanceFormatted, hasEnoughBalance, isValidAmount } = useHAOX(); // 暂时注释
  const balanceFormatted = '0.00'; // 临时模拟数据
  const hasEnoughBalance = true; // 临时模拟
  const isValidAmount = () => true; // 临时模拟函数
  const { addToast } = useToast();

  const [tradeType, setTradeType] = useState<'buy' | 'sell'>('buy');
  const [amount, setAmount] = useState('');
  const [paymentMethod, setPaymentMethod] = useState<'alipay' | 'wechat' | 'crypto'>('alipay');
  const [isProcessing, setIsProcessing] = useState(false);

  const calculateTotal = () => {
    const numAmount = parseFloat(amount) || 0;
    return numAmount * currentPrice;
  };

  const calculateFees = () => {
    const total = calculateTotal();
    const feeRate = 0.002; // 0.2% fee
    return total * feeRate;
  };

  const validateTrade = () => {
    if (!isConnected) {
      return { valid: false, message: '请先连接钱包' };
    }

    if (!amount || !isValidAmount(amount)) {
      return { valid: false, message: '请输入有效的交易数量' };
    }

    if (tradeType === 'sell' && !hasEnoughBalance(amount)) {
      return { valid: false, message: 'HAOX余额不足' };
    }

    const total = calculateTotal();
    if (total < 1) {
      return { valid: false, message: '最小交易金额为1元' };
    }

    return { valid: true, message: '' };
  };

  const handleTrade = async () => {
    const validation = validateTrade();
    if (!validation.valid) {
      addToast({
        type: 'error',
        title: '交易失败',
        message: validation.message,
      });
      return;
    }

    setIsProcessing(true);

    try {
      // Simulate trade processing
      await new Promise(resolve => setTimeout(resolve, 2000));

      const tradeData = {
        type: tradeType,
        amount: parseFloat(amount),
        price: currentPrice,
        total: calculateTotal(),
        fees: calculateFees(),
        paymentMethod,
        timestamp: new Date().toISOString(),
      };

      addToast({
        type: 'success',
        title: '交易成功',
        message: `${tradeType === 'buy' ? '买入' : '卖出'} ${amount} HAOX 成功`,
      });

      // Reset form
      setAmount('');
      
      // Callback to parent component
      if (onTrade) {
        onTrade(tradeData);
      }
    } catch (error) {
      addToast({
        type: 'error',
        title: '交易失败',
        message: '交易处理失败，请重试',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const validation = validateTrade();

  return (
    <Card title="交易">
      {/* Trade Type Toggle */}
      <div className="flex bg-system-gray-6 rounded-xl p-1 mb-6">
        <button
          onClick={() => setTradeType('buy')}
          className={`flex-1 py-3 px-4 rounded-lg text-sm font-sf-pro font-medium transition-all duration-200 ${
            tradeType === 'buy'
              ? 'bg-system-green text-white shadow-apple'
              : 'text-secondary-label hover:text-label'
          }`}
        >
          买入 HAOX
        </button>
        <button
          onClick={() => setTradeType('sell')}
          className={`flex-1 py-3 px-4 rounded-lg text-sm font-sf-pro font-medium transition-all duration-200 ${
            tradeType === 'sell'
              ? 'bg-system-red text-white shadow-apple'
              : 'text-secondary-label hover:text-label'
          }`}
        >
          卖出 HAOX
        </button>
      </div>

      {/* Current Balance */}
      {isConnected && (
        <div className="bg-system-gray-6 rounded-xl p-4 mb-6">
          <div className="flex justify-between items-center">
            <span className="text-body text-secondary-label">
              {tradeType === 'buy' ? '可用余额' : 'HAOX余额'}
            </span>
            <span className="text-body font-sf-pro font-semibold text-label">
              {tradeType === 'buy' ? '¥10,000.00' : `${balanceFormatted} HAOX`}
            </span>
          </div>
        </div>
      )}

      {/* Amount Input */}
      <div className="space-y-4">
        <Input
          label={`${tradeType === 'buy' ? '买入' : '卖出'}数量 (HAOX)`}
          type="number"
          value={amount}
          onChange={setAmount}
          placeholder="请输入交易数量"
          error={amount && !isValidAmount(amount) ? '请输入有效数量' : undefined}
        />

        {/* Quick Amount Buttons */}
        <div className="grid grid-cols-4 gap-2">
          {['100', '500', '1000', '5000'].map((quickAmount) => (
            <button
              key={quickAmount}
              onClick={() => setAmount(quickAmount)}
              className="py-2 px-3 text-sm font-sf-pro font-medium text-secondary-label border border-system-gray-4 rounded-lg hover:border-system-blue hover:text-system-blue transition-colors"
            >
              {formatNumber(parseInt(quickAmount))}
            </button>
          ))}
        </div>

        {/* Payment Method */}
        <div>
          <label className="block text-sm font-medium font-sf-pro mb-3 text-label">
            {tradeType === 'buy' ? '支付方式' : '收款方式'}
          </label>
          <div className="grid grid-cols-3 gap-3">
            {Object.entries(PAYMENT_METHODS).map(([key, method]) => (
              <button
                key={key}
                onClick={() => setPaymentMethod(key as any)}
                className={`p-4 rounded-xl border-2 transition-all duration-200 ${
                  paymentMethod === key
                    ? 'border-system-blue bg-system-blue/5 shadow-apple'
                    : 'border-system-gray-4 hover:border-system-gray-3'
                }`}
              >
                <div className="text-center">
                  <div className="text-2xl mb-2">{method.icon}</div>
                  <span className="text-sm font-sf-pro font-medium text-label">
                    {method.name}
                  </span>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Trade Summary */}
        {amount && isValidAmount(amount) && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-system-gray-6 rounded-xl p-4 space-y-3"
          >
            <div className="flex items-center space-x-2 mb-3">
              <Calculator className="w-5 h-5 text-system-blue" />
              <span className="text-body font-sf-pro font-semibold text-label">
                交易摘要
              </span>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-body text-secondary-label">数量</span>
                <span className="text-body font-sf-pro font-medium text-label">
                  {formatNumber(parseFloat(amount))} HAOX
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-body text-secondary-label">单价</span>
                <span className="text-body font-sf-pro font-medium text-label">
                  {formatCurrency(currentPrice, 'CNY')}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-body text-secondary-label">手续费 (0.2%)</span>
                <span className="text-body font-sf-pro font-medium text-label">
                  {formatCurrency(calculateFees())}
                </span>
              </div>
              
              <div className="border-t border-system-gray-4 pt-2">
                <div className="flex justify-between">
                  <span className="text-headline font-sf-pro font-semibold text-label">
                    {tradeType === 'buy' ? '总支付' : '总收入'}
                  </span>
                  <span className="text-headline font-sf-pro font-bold text-label">
                    {formatCurrency(calculateTotal() + (tradeType === 'buy' ? calculateFees() : -calculateFees()))}
                  </span>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Validation Message */}
        {amount && !validation.valid && (
          <div className="flex items-center space-x-2 p-3 bg-system-red/5 border border-system-red/20 rounded-xl">
            <AlertCircle className="w-5 h-5 text-system-red" />
            <span className="text-body text-system-red">
              {validation.message}
            </span>
          </div>
        )}

        {/* Trade Button */}
        <Button
          onClick={handleTrade}
          disabled={!validation.valid || isProcessing}
          loading={isProcessing}
          className={`w-full ${
            tradeType === 'buy' 
              ? 'bg-system-green hover:bg-green-600' 
              : 'bg-system-red hover:bg-red-600'
          }`}
          size="lg"
        >
          {isProcessing 
            ? '处理中...' 
            : `${tradeType === 'buy' ? '买入' : '卖出'} HAOX`
          }
        </Button>

        {/* Disclaimer */}
        <div className="text-center">
          <p className="text-caption-1 text-tertiary-label">
            交易有风险，投资需谨慎。请确保您了解相关风险。
          </p>
        </div>
      </div>
    </Card>
  );
};

export default TradeForm;
