/* 性能优化的 CSS 样式 */

/* 关键渲染路径优化 */
.critical-above-fold {
  /* 确保首屏内容优先渲染 */
  contain: layout style paint;
  will-change: auto;
}

/* 懒加载动画优化 */
.lazy-load-enter {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.3s ease-out, transform 0.3s ease-out;
}

.lazy-load-enter-active {
  opacity: 1;
  transform: translateY(0);
}

/* 骨架屏动画优化 */
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
}

/* 波浪动画 */
@keyframes wave {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-wave {
  position: relative;
  overflow: hidden;
}

.animate-wave::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: wave 1.6s linear infinite;
}

/* 性能优化的滚动 */
.optimized-scroll {
  /* 启用硬件加速 */
  transform: translateZ(0);
  /* 优化滚动性能 */
  -webkit-overflow-scrolling: touch;
  /* 减少重绘 */
  will-change: scroll-position;
}

/* GPU 加速的动画 */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 优化的过渡动画 */
.optimized-transition {
  transition-property: transform, opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, opacity;
}

/* 减少重排的布局 */
.layout-optimized {
  contain: layout;
}

/* 减少重绘的样式 */
.paint-optimized {
  contain: paint;
}

/* 复合层优化 */
.composite-layer {
  transform: translateZ(0);
  isolation: isolate;
}

/* 图片加载优化 */
.image-container {
  position: relative;
  overflow: hidden;
}

.image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
              linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
              linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
              linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
  animation: placeholder-shimmer 2s infinite linear;
}

@keyframes placeholder-shimmer {
  0% {
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
  }
  100% {
    background-position: 20px 20px, 20px 30px, 30px 10px, 10px 20px;
  }
}

/* 虚拟滚动优化 */
.virtual-scroll-container {
  height: 100%;
  overflow: auto;
  position: relative;
}

.virtual-scroll-item {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  will-change: transform;
}

/* 响应式图片优化 */
.responsive-image {
  width: 100%;
  height: auto;
  object-fit: cover;
  transition: opacity 0.3s ease;
}

.responsive-image[data-loaded="false"] {
  opacity: 0;
}

.responsive-image[data-loaded="true"] {
  opacity: 1;
}

/* 预加载指示器 */
.preload-indicator {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  z-index: 9999;
}

.preload-indicator.loading {
  animation: preload-progress 2s ease-in-out infinite;
}

@keyframes preload-progress {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 内容可见性优化 */
.content-visibility-auto {
  content-visibility: auto;
  contain-intrinsic-size: 0 500px;
}

/* 字体加载优化 */
.font-display-swap {
  font-display: swap;
}

/* 减少 CLS 的布局 */
.stable-layout {
  min-height: 200px; /* 为动态内容预留空间 */
}

/* 优化的表格 */
.optimized-table {
  table-layout: fixed;
  width: 100%;
}

.optimized-table th,
.optimized-table td {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 性能友好的阴影 */
.optimized-shadow {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  /* 避免使用复杂的阴影，使用简单的阴影减少渲染成本 */
}

/* 优化的渐变 */
.optimized-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  /* 使用简单的渐变，避免复杂的径向渐变 */
}

/* 移动端触摸优化 */
.touch-optimized {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

/* 减少重绘的按钮 */
.optimized-button {
  transform: translateZ(0);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.optimized-button:hover {
  transform: translateY(-1px) translateZ(0);
}

.optimized-button:active {
  transform: translateY(0) translateZ(0);
}

/* 性能监控样式 */
.performance-monitor {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px;
  border-radius: 8px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 12px;
  z-index: 10000;
  max-width: 300px;
  backdrop-filter: blur(10px);
}

.performance-metric {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.performance-score {
  font-weight: bold;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .mobile-optimized {
    /* 移动端特定优化 */
    transform: translateZ(0);
  }
  
  .performance-monitor {
    bottom: 10px;
    right: 10px;
    font-size: 10px;
    padding: 8px;
    max-width: 250px;
  }
}

/* 打印样式优化 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-optimized {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
}

/* 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
