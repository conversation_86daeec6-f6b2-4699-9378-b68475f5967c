/**
 * 奖励管理Hook
 * 处理奖励的领取和管理
 */

import { useState, useEffect, useCallback } from 'react';
import { useTelegramAuth } from './useTelegramAuth';
import { useToast } from './useToast';

// 模拟奖励类型
interface UserReward {
  id: string;
  type: string;
  amount: string;
  tokenType: string;
  status: 'pending' | 'claimed' | 'expired';
  description: string;
  createdAt: Date;
  expiresAt?: Date;
}

interface RewardClaim {
  id: string;
  rewardId: string;
  amount: string;
  tokenType: string;
  claimedAt: Date;
  txHash?: string;
}

interface UseRewardsReturn {
  // 奖励列表
  rewards: UserReward[];
  isLoadingRewards: boolean;
  refreshRewards: () => Promise<void>;

  // 待领取奖励
  pendingRewards: UserReward[];
  totalPendingAmount: string;

  // 领取奖励
  claimReward: (rewardId: string) => Promise<{ success: boolean; error?: string }>;
  claimAllRewards: () => Promise<{ success: boolean; claimed: number; error?: string }>;
  isClaiming: boolean;

  // 奖励历史
  claimHistory: RewardClaim[];
  isLoadingHistory: boolean;
  refreshHistory: () => Promise<void>;

  // 每日签到
  processDailySignIn: () => Promise<void>;

  // 错误状态
  error: string | null;
  clearError: () => void;
}

export const useRewards = (): UseRewardsReturn => {
  const { user, isAuthenticated } = useTelegramAuth();
  const { addToast } = useToast();

  // 状态管理
  const [rewards, setRewards] = useState<UserReward[]>([]);
  const [isLoadingRewards, setIsLoadingRewards] = useState(false);
  const [claimHistory, setClaimHistory] = useState<RewardClaim[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [isClaiming, setIsClaiming] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * 刷新奖励列表
   */
  const refreshRewards = useCallback(async () => {
    if (!user?.id || !isAuthenticated) return;

    try {
      setIsLoadingRewards(true);
      setError(null);

      // 模拟获取奖励数据
      const mockRewards: UserReward[] = [
        {
          id: 'reward_1',
          type: 'daily_signin',
          amount: '10',
          tokenType: 'HAOX',
          status: 'pending',
          description: '每日签到奖励',
          createdAt: new Date(),
        },
        {
          id: 'reward_2',
          type: 'social_task',
          amount: '25',
          tokenType: 'HAOX',
          status: 'pending',
          description: '社交任务奖励',
          createdAt: new Date(),
        },
      ];

      setRewards(mockRewards);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取奖励列表失败';
      setError(errorMessage);
      console.error('Failed to refresh rewards:', err);
    } finally {
      setIsLoadingRewards(false);
    }
  }, [user?.id, isAuthenticated]);

  /**
   * 刷新领取历史
   */
  const refreshHistory = useCallback(async () => {
    if (!user?.id || !isAuthenticated) return;

    try {
      setIsLoadingHistory(true);
      setError(null);

      // 模拟获取历史数据
      const mockHistory: RewardClaim[] = [
        {
          id: 'claim_1',
          rewardId: 'reward_old_1',
          amount: '15',
          tokenType: 'HAOX',
          claimedAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
          txHash: '0x123...',
        },
      ];

      setClaimHistory(mockHistory);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取领取历史失败';
      setError(errorMessage);
      console.error('Failed to refresh claim history:', err);
    } finally {
      setIsLoadingHistory(false);
    }
  }, [user?.id, isAuthenticated]);

  /**
   * 领取单个奖励
   */
  const claimReward = useCallback(async (rewardId: string) => {
    if (!user?.id || !isAuthenticated) {
      return { success: false, error: '用户未登录' };
    }

    try {
      setIsClaiming(true);
      setError(null);

      // 模拟领取奖励
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const reward = rewards.find(r => r.id === rewardId);
      if (reward) {
        // 更新奖励状态
        setRewards(prev => prev.map(r => 
          r.id === rewardId ? { ...r, status: 'claimed' as const } : r
        ));

        addToast({
          type: 'success',
          title: '奖励领取成功',
          message: `已领取 ${reward.amount} ${reward.tokenType} 奖励`,
        });

        // 刷新历史
        await refreshHistory();
      }

      return { success: true };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '领取奖励失败';
      setError(errorMessage);
      
      addToast({
        type: 'error',
        title: '奖励领取失败',
        message: errorMessage,
      });

      return { success: false, error: errorMessage };
    } finally {
      setIsClaiming(false);
    }
  }, [user?.id, isAuthenticated, rewards, addToast, refreshHistory]);

  /**
   * 领取所有待领取奖励
   */
  const claimAllRewards = useCallback(async () => {
    if (!user?.id || !isAuthenticated) {
      return { success: false, claimed: 0, error: '用户未登录' };
    }

    const pendingRewards = rewards.filter(r => r.status === 'pending');
    if (pendingRewards.length === 0) {
      return { success: false, claimed: 0, error: '没有待领取的奖励' };
    }

    try {
      setIsClaiming(true);
      setError(null);

      let claimedCount = 0;
      let totalAmount = 0;

      for (const reward of pendingRewards) {
        const result = await claimReward(reward.id);
        if (result.success) {
          claimedCount++;
          totalAmount += parseFloat(reward.amount);
        }
      }

      if (claimedCount > 0) {
        addToast({
          type: 'success',
          title: '批量领取成功',
          message: `已领取 ${claimedCount} 个奖励，共计 ${totalAmount.toFixed(2)} HAOX`,
        });
      }

      return { 
        success: claimedCount > 0, 
        claimed: claimedCount,
        error: claimedCount === 0 ? '没有成功领取任何奖励' : undefined
      };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '批量领取失败';
      setError(errorMessage);
      
      addToast({
        type: 'error',
        title: '批量领取失败',
        message: errorMessage,
      });

      return { success: false, claimed: 0, error: errorMessage };
    } finally {
      setIsClaiming(false);
    }
  }, [user?.id, isAuthenticated, rewards, addToast, claimReward]);

  /**
   * 每日签到
   */
  const processDailySignIn = useCallback(async () => {
    if (!user?.id || !isAuthenticated) return;

    try {
      setError(null);

      // 模拟每日签到
      await new Promise(resolve => setTimeout(resolve, 500));

      // 添加签到奖励
      const signInReward: UserReward = {
        id: `signin_${Date.now()}`,
        type: 'daily_signin',
        amount: '5',
        tokenType: 'HAOX',
        status: 'pending',
        description: '每日签到奖励',
        createdAt: new Date(),
      };

      setRewards(prev => [signInReward, ...prev]);

      addToast({
        type: 'success',
        title: '签到成功',
        message: '获得 5 HAOX 签到奖励',
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '签到失败';
      setError(errorMessage);
      
      addToast({
        type: 'error',
        title: '签到失败',
        message: errorMessage,
      });
    }
  }, [user?.id, isAuthenticated, addToast]);

  /**
   * 清除错误
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // 计算待领取奖励
  const pendingRewards = rewards.filter(r => r.status === 'pending');
  const totalPendingAmount = pendingRewards
    .reduce((sum, reward) => sum + parseFloat(reward.amount), 0)
    .toFixed(2);

  // 初始化数据
  useEffect(() => {
    if (user?.id && isAuthenticated) {
      refreshRewards();
      refreshHistory();
    } else {
      // 清除状态
      setRewards([]);
      setClaimHistory([]);
      setError(null);
    }
  }, [user?.id, isAuthenticated, refreshRewards, refreshHistory]);

  return {
    // 奖励列表
    rewards,
    isLoadingRewards,
    refreshRewards,

    // 待领取奖励
    pendingRewards,
    totalPendingAmount,

    // 领取奖励
    claimReward,
    claimAllRewards,
    isClaiming,

    // 奖励历史
    claimHistory,
    isLoadingHistory,
    refreshHistory,

    // 每日签到
    processDailySignIn,

    // 错误状态
    error,
    clearError,
  };
};
