/**
 * 钱包管理Hook
 * 统一管理钱包相关操作和状态
 */

import { useState, useEffect, useCallback } from 'react';
import { useTelegramAuth } from './useTelegramAuth';
import { useToast } from './useToast';
import { 
  walletService, 
  custodyWalletService,
  type WalletBalance,
  type TransactionHistory,
  type TransferRequest,
  type WithdrawalRequest
} from '@/services/wallet';

interface UseWalletReturn {
  // 余额相关
  balance: WalletBalance | null;
  isLoadingBalance: boolean;
  refreshBalance: () => Promise<void>;

  // 交易相关
  transactionHistory: TransactionHistory[];
  isLoadingHistory: boolean;
  refreshHistory: () => Promise<void>;

  // 转账相关
  transfer: (request: TransferRequest) => Promise<{ success: boolean; txHash?: string; error?: string }>;
  isTransferring: boolean;

  // 提现相关
  withdraw: (request: WithdrawalRequest) => Promise<{ success: boolean; txHash?: string; error?: string }>;
  isWithdrawing: boolean;

  // 地址相关
  walletAddress: string | null;
  generateReceiveAddress: () => Promise<string | null>;

  // 错误状态
  error: string | null;
  clearError: () => void;
}

export const useWallet = (): UseWalletReturn => {
  const { user, isAuthenticated } = useTelegramAuth();
  const { addToast } = useToast();

  // 状态管理
  const [balance, setBalance] = useState<WalletBalance | null>(null);
  const [isLoadingBalance, setIsLoadingBalance] = useState(false);
  const [transactionHistory, setTransactionHistory] = useState<TransactionHistory[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [isTransferring, setIsTransferring] = useState(false);
  const [isWithdrawing, setIsWithdrawing] = useState(false);
  const [walletAddress, setWalletAddress] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  /**
   * 刷新余额
   */
  const refreshBalance = useCallback(async () => {
    if (!user?.id || !isAuthenticated) return;

    try {
      setIsLoadingBalance(true);
      setError(null);

      const balance = await custodyWalletService.getBalance(user.id);
      setBalance(balance);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取余额失败';
      setError(errorMessage);
      console.error('Failed to refresh balance:', err);
    } finally {
      setIsLoadingBalance(false);
    }
  }, [user?.id, isAuthenticated]);

  /**
   * 刷新交易历史
   */
  const refreshHistory = useCallback(async () => {
    if (!user?.id || !isAuthenticated) return;

    try {
      setIsLoadingHistory(true);
      setError(null);

      const transactions = await custodyWalletService.getTransactionHistory(user.id);
      setTransactionHistory(transactions);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取交易历史失败';
      setError(errorMessage);
      console.error('Failed to refresh history:', err);
    } finally {
      setIsLoadingHistory(false);
    }
  }, [user?.id, isAuthenticated]);

  /**
   * 转账
   */
  const transfer = useCallback(async (request: TransferRequest) => {
    if (!user?.id || !isAuthenticated) {
      return { success: false, error: '用户未登录' };
    }

    try {
      setIsTransferring(true);
      setError(null);

      const result = await custodyWalletService.transfer(user.id, request);
      
      if (result.success) {
        addToast({
          type: 'success',
          title: '转账成功',
          message: `已成功转账 ${request.amount} ${request.tokenType}`,
        });

        // 刷新余额和交易历史
        await Promise.all([refreshBalance(), refreshHistory()]);
      } else {
        addToast({
          type: 'error',
          title: '转账失败',
          message: result.error || '转账失败，请重试',
        });
      }

      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '转账失败';
      setError(errorMessage);
      
      addToast({
        type: 'error',
        title: '转账失败',
        message: errorMessage,
      });

      return { success: false, error: errorMessage };
    } finally {
      setIsTransferring(false);
    }
  }, [user?.id, isAuthenticated, addToast, refreshBalance, refreshHistory]);

  /**
   * 提现
   */
  const withdraw = useCallback(async (request: WithdrawalRequest) => {
    if (!user?.id || !isAuthenticated) {
      return { success: false, error: '用户未登录' };
    }

    try {
      setIsWithdrawing(true);
      setError(null);

      const result = await custodyWalletService.withdraw(user.id, request);
      
      if (result.success) {
        addToast({
          type: 'success',
          title: '提现申请已提交',
          message: `提现 ${request.amount} ${request.tokenType} 正在处理中`,
        });

        // 刷新余额和交易历史
        await Promise.all([refreshBalance(), refreshHistory()]);
      } else {
        addToast({
          type: 'error',
          title: '提现失败',
          message: result.error || '提现失败，请重试',
        });
      }

      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '提现失败';
      setError(errorMessage);
      
      addToast({
        type: 'error',
        title: '提现失败',
        message: errorMessage,
      });

      return { success: false, error: errorMessage };
    } finally {
      setIsWithdrawing(false);
    }
  }, [user?.id, isAuthenticated, addToast, refreshBalance, refreshHistory]);

  /**
   * 生成收款地址
   */
  const generateReceiveAddress = useCallback(async (): Promise<string | null> => {
    if (!user?.id || !isAuthenticated) return null;

    try {
      setError(null);

      const address = await custodyWalletService.generateWalletForUser(user.id);
      setWalletAddress(address);
      return address;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '生成地址失败';
      setError(errorMessage);
      console.error('Failed to generate receive address:', err);
      return null;
    }
  }, [user?.id, isAuthenticated]);

  /**
   * 清除错误
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // 初始化数据
  useEffect(() => {
    if (user?.id && isAuthenticated) {
      refreshBalance();
      refreshHistory();
      generateReceiveAddress();
    } else {
      // 清除状态
      setBalance(null);
      setTransactionHistory([]);
      setWalletAddress(null);
      setError(null);
    }
  }, [user?.id, isAuthenticated, refreshBalance, refreshHistory, generateReceiveAddress]);

  return {
    // 余额相关
    balance,
    isLoadingBalance,
    refreshBalance,

    // 交易相关
    transactionHistory,
    isLoadingHistory,
    refreshHistory,

    // 转账相关
    transfer,
    isTransferring,

    // 提现相关
    withdraw,
    isWithdrawing,

    // 地址相关
    walletAddress,
    generateReceiveAddress,

    // 错误状态
    error,
    clearError,
  };
};
