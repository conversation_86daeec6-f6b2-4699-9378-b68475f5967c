import { useState, useEffect, useCallback } from 'react';
import { useWalletAuth } from './useWalletAuth';

interface MerchantApplication {
  id: string;
  business_name: string;
  business_type: string;
  application_status: string;
  created_at: string;
  review_notes?: string;
}

interface MerchantInfo {
  id: string;
  business_name: string;
  merchant_level: string;
  rating: number;
  total_orders: number;
  completion_rate: number;
  available_amount: number;
  frozen_amount: number;
  status: string;
  level_config?: {
    min_deposit: number;
    max_daily_volume: number;
    commission_rate: number;
    features: any;
  };
}

interface FrozenFund {
  id: string;
  freeze_type: string;
  amount: number;
  reason: string;
  frozen_at: string;
  expires_at?: string;
}

interface MerchantData {
  is_merchant: boolean;
  merchant_info: MerchantInfo | null;
  latest_application: MerchantApplication | null;
  can_apply: boolean;
  frozen_funds: FrozenFund[];
  funds_summary: {
    total_balance: number;
    available_amount: number;
    frozen_amount: number;
    required_deposit: number;
  };
}

export function useMerchantSystem() {
  const { user, isAuthenticated } = useWalletAuth();
  const [merchantData, setMerchantData] = useState<MerchantData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取商家状态
  const fetchMerchantStatus = useCallback(async () => {
    if (!user?.id) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // 获取申请状态
      const applicationResponse = await fetch(`/api/merchant/apply?userId=${user.id}`);
      const applicationData = await applicationResponse.json();

      if (!applicationResponse.ok) {
        throw new Error(applicationData.error || 'Failed to fetch application status');
      }

      let fundsData = null;
      if (applicationData.data.is_merchant) {
        // 获取资金状态
        const fundsResponse = await fetch(`/api/merchant/funds?userId=${user.id}`);
        const fundsResult = await fundsResponse.json();

        if (fundsResponse.ok) {
          fundsData = fundsResult.data;
        }
      }

      setMerchantData({
        is_merchant: applicationData.data.is_merchant,
        merchant_info: fundsData?.merchant || applicationData.data.merchant_info,
        latest_application: applicationData.data.latest_application,
        can_apply: applicationData.data.can_apply,
        frozen_funds: fundsData?.frozen_funds || [],
        funds_summary: fundsData?.funds_summary || {
          total_balance: 0,
          available_amount: 0,
          frozen_amount: 0,
          required_deposit: 5000
        }
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [user?.id]);

  // 申请成为商家
  const applyForMerchant = useCallback(async (applicationData: {
    businessName: string;
    businessType: string;
    businessDescription?: string;
    contactEmail: string;
    contactPhone?: string;
    tradingExperience?: string;
    expectedVolume?: number;
  }) => {
    if (!user?.id) {
      setError('User not authenticated');
      return false;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/merchant/apply', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.id,
          ...applicationData,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to submit application');
      }

      // 刷新商家状态
      await fetchMerchantStatus();
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, fetchMerchantStatus]);

  // 冻结资金
  const freezeFunds = useCallback(async (
    merchantId: string,
    freezeType: string,
    amount: number,
    reason: string,
    expiresAt?: string
  ) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/merchant/funds', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          merchantId,
          freezeType,
          amount,
          reason,
          expiresAt,
          frozenBy: user?.id,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to freeze funds');
      }

      // 刷新商家状态
      await fetchMerchantStatus();
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, fetchMerchantStatus]);

  // 检查是否满足商家要求
  const checkMerchantRequirements = useCallback(() => {
    if (!merchantData?.funds_summary) {
      return {
        canBecomeMerchant: false,
        missingDeposit: 5000,
        hasEnoughFunds: false
      };
    }

    const requiredDeposit = merchantData.funds_summary.required_deposit;
    const totalBalance = merchantData.funds_summary.total_balance;
    const hasEnoughFunds = totalBalance >= requiredDeposit;

    return {
      canBecomeMerchant: hasEnoughFunds && merchantData.can_apply,
      missingDeposit: Math.max(0, requiredDeposit - totalBalance),
      hasEnoughFunds,
      requiredDeposit
    };
  }, [merchantData]);

  // 初始化时获取数据
  useEffect(() => {
    if (isAuthenticated && user?.id) {
      fetchMerchantStatus();
    }
  }, [isAuthenticated, user?.id, fetchMerchantStatus]);

  return {
    merchantData,
    isLoading,
    error,
    fetchMerchantStatus,
    applyForMerchant,
    freezeFunds,
    checkMerchantRequirements,
    // 便捷访问器
    isMerchant: merchantData?.is_merchant || false,
    merchantInfo: merchantData?.merchant_info,
    applicationStatus: merchantData?.latest_application?.application_status,
    canApply: merchantData?.can_apply || false,
    frozenFunds: merchantData?.frozen_funds || [],
    fundsSummary: merchantData?.funds_summary,
  };
}
