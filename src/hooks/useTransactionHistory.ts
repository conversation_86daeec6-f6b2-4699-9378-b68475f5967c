/**
 * 交易历史Hook
 * 管理用户交易历史记录的查询和筛选
 */

import { useState, useCallback, useEffect } from 'react';
import { useTelegramAuth } from './useTelegramAuth';
import { WalletService } from '@/services/wallet/WalletService';
import { WalletTransaction } from '@/services/wallet/types';

interface UseTransactionHistoryReturn {
  transactions: WalletTransaction[];
  isLoading: boolean;
  error: string | null;
  loadTransactions: () => Promise<void>;
  loadMore: () => Promise<void>;
  hasMore: boolean;
  filter: 'all' | 'transfer' | 'withdrawal' | 'deposit' | 'reward_claim';
  setFilter: (filter: 'all' | 'transfer' | 'withdrawal' | 'deposit' | 'reward_claim') => void;
  refreshTransactions: () => Promise<void>;
}

export const useTransactionHistory = (): UseTransactionHistoryReturn => {
  const { user, isAuthenticated } = useTelegramAuth();
  const [transactions, setTransactions] = useState<WalletTransaction[]>([]);
  const [allTransactions, setAllTransactions] = useState<WalletTransaction[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [filter, setFilter] = useState<'all' | 'transfer' | 'withdrawal' | 'deposit' | 'reward_claim'>('all');

  const pageSize = 20;

  /**
   * 初始化钱包服务
   */
  const initializeWalletService = useCallback(async () => {
    const walletService = new WalletService();
    await walletService.initialize();
    return walletService;
  }, []);

  /**
   * 加载交易记录
   */
  const loadTransactions = useCallback(async () => {
    if (!isAuthenticated || !user) {
      setTransactions([]);
      setAllTransactions([]);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const walletService = await initializeWalletService();
      const newTransactions = await walletService.getUserTransactionHistory(user.id, 1, pageSize);

      // 添加一些模拟数据用于演示
      const mockTransactions: WalletTransaction[] = [
        {
          id: 'mock_1',
          fromAddress: '******************************************',
          toAddress: '******************************************',
          amount: '100.50',
          tokenType: 'HAOX',
          txHash: '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890',
          status: 'confirmed',
          type: 'reward_claim',
          createdAt: new Date(Date.now() - 3600000),
          confirmedAt: new Date(Date.now() - 3500000),
          gasFee: '0.002',
          metadata: {
            rewardSource: 'daily_signin',
            description: '每日签到奖励',
          },
        },
        {
          id: 'mock_2',
          fromAddress: '******************************************',
          toAddress: '******************************************',
          amount: '50.00',
          tokenType: 'HAOX',
          txHash: '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
          status: 'confirmed',
          type: 'transfer',
          createdAt: new Date(Date.now() - 7200000),
          confirmedAt: new Date(Date.now() - 7100000),
          gasFee: '0.001',
          metadata: {
            telegramUserId: user.id,
          },
        },
        {
          id: 'mock_3',
          fromAddress: '******************************************',
          toAddress: '0x5555555555555555555555555555555555555555',
          amount: '200.00',
          tokenType: 'HAOX',
          status: 'pending',
          type: 'withdrawal',
          createdAt: new Date(Date.now() - 1800000),
          gasFee: '0.005',
          metadata: {
            telegramUserId: user.id,
            withdrawType: 'external',
          },
        },
        {
          id: 'mock_4',
          fromAddress: '0x0000000000000000000000000000000000000000',
          toAddress: '******************************************',
          amount: '25.00',
          tokenType: 'HAOX',
          txHash: '0xfedcba0987654321fedcba0987654321fedcba0987654321fedcba0987654321',
          status: 'confirmed',
          type: 'reward_claim',
          createdAt: new Date(Date.now() - 86400000),
          confirmedAt: new Date(Date.now() - 86300000),
          metadata: {
            rewardSource: 'referral',
            description: '邀请好友奖励',
          },
        },
        {
          id: 'mock_5',
          fromAddress: '0x1111111111111111111111111111111111111111',
          toAddress: '******************************************',
          amount: '0.05',
          tokenType: 'BNB',
          txHash: '0x9999999999999999999999999999999999999999999999999999999999999999',
          status: 'confirmed',
          type: 'deposit',
          createdAt: new Date(Date.now() - 172800000),
          confirmedAt: new Date(Date.now() - 172700000),
          gasFee: '0.0001',
          metadata: {
            depositType: 'external',
          },
        },
      ];

      const combinedTransactions = [...mockTransactions, ...newTransactions];
      
      // 按时间排序
      combinedTransactions.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

      setAllTransactions(combinedTransactions);
      setCurrentPage(1);
      setHasMore(combinedTransactions.length >= pageSize);
      
      // 应用筛选
      applyFilter(combinedTransactions, filter);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '加载交易记录失败';
      setError(errorMessage);
      console.error('Failed to load transactions:', err);
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user, initializeWalletService, filter]);

  /**
   * 加载更多交易记录
   */
  const loadMore = useCallback(async () => {
    if (!isAuthenticated || !user || isLoading || !hasMore) {
      return;
    }

    try {
      setIsLoading(true);

      const walletService = await initializeWalletService();
      const newTransactions = await walletService.getUserTransactionHistory(
        user.id, 
        currentPage + 1, 
        pageSize
      );

      if (newTransactions.length > 0) {
        const updatedTransactions = [...allTransactions, ...newTransactions];
        setAllTransactions(updatedTransactions);
        setCurrentPage(prev => prev + 1);
        setHasMore(newTransactions.length >= pageSize);
        
        // 重新应用筛选
        applyFilter(updatedTransactions, filter);
      } else {
        setHasMore(false);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '加载更多交易记录失败';
      setError(errorMessage);
      console.error('Failed to load more transactions:', err);
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user, isLoading, hasMore, currentPage, allTransactions, filter, initializeWalletService]);

  /**
   * 应用筛选
   */
  const applyFilter = useCallback((transactionList: Transaction[], filterType: typeof filter) => {
    if (filterType === 'all') {
      setTransactions(transactionList);
    } else {
      const filtered = transactionList.filter(tx => tx.type === filterType);
      setTransactions(filtered);
    }
  }, []);

  /**
   * 设置筛选器
   */
  const handleSetFilter = useCallback((newFilter: typeof filter) => {
    setFilter(newFilter);
    applyFilter(allTransactions, newFilter);
  }, [allTransactions, applyFilter]);

  /**
   * 刷新交易记录
   */
  const refreshTransactions = useCallback(async () => {
    setCurrentPage(1);
    setHasMore(true);
    await loadTransactions();
  }, [loadTransactions]);

  /**
   * 当用户认证状态变化时重新加载
   */
  useEffect(() => {
    if (isAuthenticated && user) {
      loadTransactions();
    } else {
      setTransactions([]);
      setAllTransactions([]);
    }
  }, [isAuthenticated, user]);

  return {
    transactions,
    isLoading,
    error,
    loadTransactions,
    loadMore,
    hasMore,
    filter,
    setFilter: handleSetFilter,
    refreshTransactions,
  };
};

/**
 * 交易统计Hook
 */
export const useTransactionStats = () => {
  const { user, isAuthenticated } = useTelegramAuth();
  const [stats, setStats] = useState<{
    totalTransactions: number;
    totalSent: string;
    totalReceived: string;
    totalRewards: string;
    thisMonth: {
      transactions: number;
      sent: string;
      received: string;
    };
  } | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const loadStats = useCallback(async () => {
    if (!isAuthenticated || !user) {
      setStats(null);
      return;
    }

    try {
      setIsLoading(true);

      // 这里应该调用API获取统计数据
      // 暂时使用模拟数据
      const mockStats = {
        totalTransactions: 25,
        totalSent: '1,250.50',
        totalReceived: '2,100.75',
        totalRewards: '850.25',
        thisMonth: {
          transactions: 8,
          sent: '350.00',
          received: '425.50',
        },
      };

      setStats(mockStats);
    } catch (error) {
      console.error('Failed to load transaction stats:', error);
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user]);

  useEffect(() => {
    loadStats();
  }, [loadStats]);

  return {
    stats,
    isLoading,
    loadStats,
  };
};

/**
 * 交易搜索Hook
 */
export const useTransactionSearch = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Transaction[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  const searchTransactions = useCallback(async (query: string, transactions: Transaction[]) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      setIsSearching(true);

      // 简单的本地搜索实现
      const results = transactions.filter(tx => 
        tx.txHash?.toLowerCase().includes(query.toLowerCase()) ||
        tx.toAddress.toLowerCase().includes(query.toLowerCase()) ||
        tx.fromAddress.toLowerCase().includes(query.toLowerCase()) ||
        tx.amount.includes(query) ||
        tx.type.toLowerCase().includes(query.toLowerCase())
      );

      setSearchResults(results);
    } catch (error) {
      console.error('Failed to search transactions:', error);
    } finally {
      setIsSearching(false);
    }
  }, []);

  return {
    searchQuery,
    setSearchQuery,
    searchResults,
    isSearching,
    searchTransactions,
  };
};
