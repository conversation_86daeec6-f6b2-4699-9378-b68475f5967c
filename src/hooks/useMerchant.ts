'use client';

import { useState, useEffect } from 'react';
import { useToast } from '@/components/ui';
import { supabase } from '@/lib/supabase';
import type { MerchantApplication, MerchantApplicationForm } from '@/types';

export const useMerchant = (userId?: string) => {
  const [application, setApplication] = useState<MerchantApplication | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { addToast } = useToast();

  // Fetch merchant application
  const fetchApplication = async () => {
    if (!userId) return;

    // Check if supabase client is available
    if (!supabase) {
      console.warn('Supabase client not available');
      return;
    }

    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('merchant_applications')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      setApplication(data || null);
    } catch (error) {
      console.error('Failed to fetch merchant application:', error);
      addToast({
        type: 'error',
        title: '获取申请信息失败',
        message: '无法加载商家认证申请信息',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Submit merchant application
  const submitApplication = async (formData: MerchantApplicationForm) => {
    if (!userId) {
      addToast({
        type: 'error',
        title: '提交失败',
        message: '请先登录您的账户',
      });
      return false;
    }

    // Check if application already exists
    if (application) {
      addToast({
        type: 'warning',
        title: '申请已存在',
        message: '您已经提交过商家认证申请',
      });
      return false;
    }

    // Check if supabase client is available
    if (!supabase) {
      addToast({
        type: 'error',
        title: '提交失败',
        message: '数据库连接不可用',
      });
      return false;
    }

    setIsSubmitting(true);
    try {
      const { error } = await supabase
        .from('merchant_applications')
        .insert({
          user_id: userId,
          business_name: formData.businessName,
          business_type: formData.businessType,
          business_license: formData.businessLicense,
          contact_person: formData.contactPerson,
          contact_phone: formData.contactPhone,
          contact_email: formData.contactEmail,
          description: formData.description,
          status: 'pending',
        });

      if (error) throw error;

      addToast({
        type: 'success',
        title: '申请提交成功',
        message: '您的商家认证申请已提交，我们将在3-5个工作日内完成审核',
      });

      // Refresh application data
      await fetchApplication();
      return true;
    } catch (error) {
      console.error('Failed to submit merchant application:', error);
      addToast({
        type: 'error',
        title: '提交申请失败',
        message: '提交商家认证申请时出现错误，请重试',
      });
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  // Update application
  const updateApplication = async (updates: Partial<MerchantApplicationForm>) => {
    if (!userId || !application) {
      addToast({
        type: 'error',
        title: '更新失败',
        message: '无法更新申请信息',
      });
      return false;
    }

    // Only allow updates if status is pending or rejected
    if (application.status === 'approved') {
      addToast({
        type: 'warning',
        title: '无法更新',
        message: '已通过审核的申请无法修改',
      });
      return false;
    }

    setIsSubmitting(true);
    try {
      const { error } = await supabase
        .from('merchant_applications')
        .update({
          business_name: updates.businessName,
          business_type: updates.businessType,
          business_license: updates.businessLicense,
          contact_person: updates.contactPerson,
          contact_phone: updates.contactPhone,
          contact_email: updates.contactEmail,
          description: updates.description,
          status: 'pending', // Reset to pending when updated
        })
        .eq('id', application.id);

      if (error) throw error;

      addToast({
        type: 'success',
        title: '申请更新成功',
        message: '您的商家认证申请已更新',
      });

      // Refresh application data
      await fetchApplication();
      return true;
    } catch (error) {
      console.error('Failed to update merchant application:', error);
      addToast({
        type: 'error',
        title: '更新申请失败',
        message: '更新商家认证申请时出现错误，请重试',
      });
      return false;
    } finally {
      setIsSubmitting(false);
    }
  };

  // Cancel application
  const cancelApplication = async () => {
    if (!userId || !application) {
      addToast({
        type: 'error',
        title: '取消失败',
        message: '无法取消申请',
      });
      return false;
    }

    // Only allow cancellation if status is pending
    if (application.status !== 'pending') {
      addToast({
        type: 'warning',
        title: '无法取消',
        message: '只能取消待审核的申请',
      });
      return false;
    }

    try {
      const { error } = await supabase
        .from('merchant_applications')
        .delete()
        .eq('id', application.id);

      if (error) throw error;

      addToast({
        type: 'info',
        title: '申请已取消',
        message: '您的商家认证申请已取消',
      });

      setApplication(null);
      return true;
    } catch (error) {
      console.error('Failed to cancel merchant application:', error);
      addToast({
        type: 'error',
        title: '取消申请失败',
        message: '取消商家认证申请时出现错误，请重试',
      });
      return false;
    }
  };

  // Get application status info
  const getStatusInfo = () => {
    if (!application) {
      return {
        status: 'none',
        statusText: '未申请',
        statusColor: 'text-secondary-label',
        canEdit: true,
        canCancel: false,
      };
    }

    switch (application.status) {
      case 'pending':
        return {
          status: 'pending',
          statusText: '审核中',
          statusColor: 'text-system-orange',
          canEdit: true,
          canCancel: true,
        };
      case 'approved':
        return {
          status: 'approved',
          statusText: '已通过',
          statusColor: 'text-system-green',
          canEdit: false,
          canCancel: false,
        };
      case 'rejected':
        return {
          status: 'rejected',
          statusText: '已拒绝',
          statusColor: 'text-system-red',
          canEdit: true,
          canCancel: true,
        };
      default:
        return {
          status: 'unknown',
          statusText: '未知状态',
          statusColor: 'text-secondary-label',
          canEdit: false,
          canCancel: false,
        };
    }
  };

  // Check if user is merchant
  const isMerchant = () => {
    return application?.status === 'approved';
  };

  // Get application form data
  const getFormData = (): MerchantApplicationForm => {
    if (!application) {
      return {
        businessName: '',
        businessType: '',
        businessLicense: '',
        contactPerson: '',
        contactPhone: '',
        contactEmail: '',
        description: '',
      };
    }

    return {
      businessName: application.business_name,
      businessType: application.business_type,
      businessLicense: application.business_license,
      contactPerson: application.contact_person,
      contactPhone: application.contact_phone,
      contactEmail: application.contact_email,
      description: application.description,
    };
  };

  // Load application on mount
  useEffect(() => {
    if (userId) {
      fetchApplication();
    }
  }, [userId]);

  return {
    application,
    isLoading,
    isSubmitting,
    
    // Actions
    submitApplication,
    updateApplication,
    cancelApplication,
    
    // Utilities
    getStatusInfo,
    isMerchant,
    getFormData,
    refetch: fetchApplication,
  };
};
