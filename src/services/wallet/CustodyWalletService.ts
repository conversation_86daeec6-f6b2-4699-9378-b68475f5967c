/**
 * 托管钱包服务
 * 为Telegram用户自动生成和管理BSC钱包地址
 */

import { ethers } from 'ethers';
import { TelegramUser, WalletBalance, TransactionHistory, TransferRequest, WithdrawalRequest } from './types';

export interface CustodyWalletConfig {
  rpcUrl: string;
  chainId: number;
  masterSeed?: string;
}

export class CustodyWalletService {
  private provider: ethers.JsonRpcProvider;
  private config: CustodyWalletConfig;
  private walletCache: Map<number, ethers.Wallet> = new Map();

  constructor(config: CustodyWalletConfig) {
    this.config = config;
    this.provider = new ethers.JsonRpcProvider(config.rpcUrl);
  }

  /**
   * 为Telegram用户生成钱包地址
   */
  async generateWalletForUser(telegramUserId: number): Promise<string> {
    try {
      // 检查缓存
      if (this.walletCache.has(telegramUserId)) {
        return this.walletCache.get(telegramUserId)!.address;
      }

      // 基于用户ID生成确定性钱包
      const seed = this.generateSeedForUser(telegramUserId);
      const wallet = ethers.Wallet.fromPhrase(seed);
      
      // 连接到提供者
      const connectedWallet = wallet.connect(this.provider);
      
      // 缓存钱包
      this.walletCache.set(telegramUserId, connectedWallet);

      console.log(`Generated wallet for user ${telegramUserId}: ${wallet.address}`);
      
      return wallet.address;
    } catch (error) {
      console.error('Failed to generate wallet:', error);
      throw new Error('钱包生成失败');
    }
  }

  /**
   * 获取用户钱包余额
   */
  async getBalance(telegramUserId: number): Promise<WalletBalance> {
    try {
      const walletAddress = await this.generateWalletForUser(telegramUserId);
      
      // 获取BNB余额
      const bnbBalance = await this.provider.getBalance(walletAddress);
      
      // 模拟HAOX余额（在真实环境中需要调用代币合约）
      const haoxBalance = ethers.parseEther('1000'); // 模拟1000 HAOX

      return {
        bnb: bnbBalance.toString(),
        haox: haoxBalance.toString(),
        usd: '0', // 需要从价格API获取
        lastUpdated: new Date(),
      };
    } catch (error) {
      console.error('Failed to get balance:', error);
      throw new Error('获取余额失败');
    }
  }

  /**
   * 获取交易历史
   */
  async getTransactionHistory(telegramUserId: number): Promise<TransactionHistory[]> {
    try {
      const walletAddress = await this.generateWalletForUser(telegramUserId);
      
      // 模拟交易历史（在真实环境中需要从区块链获取）
      const mockHistory: TransactionHistory[] = [
        {
          id: '1',
          type: 'receive',
          tokenType: 'HAOX',
          amount: '500',
          from: '******************************************',
          to: walletAddress,
          txHash: '0x1234567890abcdef',
          timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1天前
          status: 'completed',
          fee: '0.001',
        },
        {
          id: '2',
          type: 'send',
          tokenType: 'BNB',
          amount: '0.1',
          from: walletAddress,
          to: '0x9876543210fedcba',
          txHash: '0xfedcba0987654321',
          timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2天前
          status: 'completed',
          fee: '0.0005',
        },
      ];

      return mockHistory;
    } catch (error) {
      console.error('Failed to get transaction history:', error);
      throw new Error('获取交易历史失败');
    }
  }

  /**
   * 转账
   */
  async transfer(
    telegramUserId: number, 
    request: TransferRequest
  ): Promise<{ success: boolean; txHash?: string; error?: string }> {
    try {
      const wallet = this.walletCache.get(telegramUserId);
      if (!wallet) {
        throw new Error('钱包未找到');
      }

      // 验证地址格式
      if (!ethers.isAddress(request.toAddress)) {
        throw new Error('无效的接收地址');
      }

      // 模拟转账（在真实环境中需要实际发送交易）
      console.log('Simulating transfer:', {
        from: wallet.address,
        to: request.toAddress,
        amount: request.amount,
        tokenType: request.tokenType,
      });

      // 模拟交易哈希
      const mockTxHash = '0x' + Math.random().toString(16).substr(2, 64);

      return {
        success: true,
        txHash: mockTxHash,
      };
    } catch (error) {
      console.error('Transfer failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '转账失败',
      };
    }
  }

  /**
   * 提现到外部地址
   */
  async withdraw(
    telegramUserId: number,
    request: WithdrawalRequest
  ): Promise<{ success: boolean; txHash?: string; error?: string }> {
    try {
      const wallet = this.walletCache.get(telegramUserId);
      if (!wallet) {
        throw new Error('钱包未找到');
      }

      // 验证地址格式
      if (!ethers.isAddress(request.toAddress)) {
        throw new Error('无效的提现地址');
      }

      // 模拟提现（在真实环境中需要实际发送交易）
      console.log('Simulating withdrawal:', {
        from: wallet.address,
        to: request.toAddress,
        amount: request.amount,
        tokenType: request.tokenType,
      });

      // 模拟交易哈希
      const mockTxHash = '0x' + Math.random().toString(16).substr(2, 64);

      return {
        success: true,
        txHash: mockTxHash,
      };
    } catch (error) {
      console.error('Withdrawal failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '提现失败',
      };
    }
  }

  /**
   * 为用户生成确定性种子
   */
  private generateSeedForUser(telegramUserId: number): string {
    // 在真实环境中，这应该使用更安全的方法
    // 这里只是为了演示目的
    const baseSeed = this.config.masterSeed || 'sociomint-demo-seed';
    const userSeed = `${baseSeed}-user-${telegramUserId}`;
    
    // 生成助记词
    const entropy = ethers.keccak256(ethers.toUtf8Bytes(userSeed));
    const mnemonic = ethers.Mnemonic.fromEntropy(entropy.slice(0, 32));
    
    return mnemonic.phrase;
  }

  /**
   * 获取钱包私钥（仅用于内部操作）
   */
  private async getWalletPrivateKey(telegramUserId: number): Promise<string> {
    const wallet = this.walletCache.get(telegramUserId);
    if (!wallet) {
      throw new Error('钱包未找到');
    }
    return wallet.privateKey;
  }

  /**
   * 验证用户是否有足够余额
   */
  async hasEnoughBalance(
    telegramUserId: number,
    amount: string,
    tokenType: 'BNB' | 'HAOX'
  ): Promise<boolean> {
    try {
      const balance = await this.getBalance(telegramUserId);
      const requiredAmount = ethers.parseEther(amount);
      
      if (tokenType === 'BNB') {
        return BigInt(balance.bnb) >= requiredAmount;
      } else {
        return BigInt(balance.haox) >= requiredAmount;
      }
    } catch (error) {
      console.error('Failed to check balance:', error);
      return false;
    }
  }
}

// 创建默认实例
export const custodyWalletService = new CustodyWalletService({
  rpcUrl: process.env.NEXT_PUBLIC_BSC_RPC_URL || 'https://bsc-dataseed1.binance.org/',
  chainId: process.env.NEXT_PUBLIC_NETWORK === 'mainnet' ? 56 : 97,
  masterSeed: process.env.WALLET_MASTER_SEED,
});
