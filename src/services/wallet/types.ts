/**
 * 托管钱包系统类型定义
 */

export interface TelegramUser {
  id: number;
  username?: string;
  firstName: string;
  lastName?: string;
  photoUrl?: string;
  authDate?: number;
  hash?: string;
}

export interface WalletAddress {
  id: string;
  telegramUserId: number;
  address: string;
  derivationPath: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface WalletBalance {
  address: string;
  haoxBalance: string;
  bnbBalance: string;
  lastUpdated: Date;
}

export interface PendingReward {
  id: string;
  telegramUserId: number;
  amount: string;
  source: 'social_task' | 'referral' | 'trading' | 'staking';
  description: string;
  createdAt: Date;
  claimedAt?: Date;
}

export interface WalletTransaction {
  id: string;
  fromAddress: string;
  toAddress: string;
  amount: string;
  tokenType: 'HAOX' | 'BNB';
  txHash?: string;
  status: 'pending' | 'confirmed' | 'failed' | 'cancelled';
  type: 'transfer' | 'withdrawal' | 'deposit' | 'reward_claim';
  gasUsed?: string;
  gasFee?: string;
  createdAt: Date;
  confirmedAt?: Date;
  metadata?: Record<string, any>;
}

export interface MultiSigTransaction {
  id: string;
  transactionId: string;
  requiredSignatures: number;
  signatures: MultiSigSignature[];
  status: 'pending' | 'approved' | 'rejected' | 'executed';
  createdAt: Date;
  executedAt?: Date;
}

export interface MultiSigSignature {
  id: string;
  signerId: string;
  signerType: 'platform_key_1' | 'platform_key_2' | 'user_verification';
  signature: string;
  signedAt: Date;
}

export interface WalletSecurity {
  telegramUserId: number;
  dailyWithdrawalLimit: string;
  monthlyWithdrawalLimit: string;
  requiresKYC: boolean;
  kycStatus: 'none' | 'pending' | 'approved' | 'rejected';
  twoFactorEnabled: boolean;
  lastSecurityCheck: Date;
}

export interface GasFeeEstimate {
  slow: {
    gasPrice: string;
    estimatedTime: number; // minutes
    cost: string; // in BNB
  };
  standard: {
    gasPrice: string;
    estimatedTime: number;
    cost: string;
  };
  fast: {
    gasPrice: string;
    estimatedTime: number;
    cost: string;
  };
}

export interface WalletConfig {
  network: 'mainnet' | 'testnet';
  rpcUrl: string;
  chainId: number;
  haoxContractAddress: string;
  multiSigContractAddress: string;
  hotWalletThreshold: string; // Maximum amount in hot wallet
  coldWalletAddress: string;
  gasLimitMultiplier: number;
  maxGasPrice: string;
}

export interface HDWalletConfig {
  masterSeedPath: string;
  derivationBasePath: string; // m/44'/60'/0'/0
  encryptionKey: string;
  backupLocations: string[];
}

export interface SecurityAlert {
  id: string;
  type: 'suspicious_transaction' | 'unusual_login' | 'high_value_transfer' | 'multiple_failures';
  severity: 'low' | 'medium' | 'high' | 'critical';
  telegramUserId?: number;
  description: string;
  metadata: Record<string, any>;
  resolved: boolean;
  createdAt: Date;
  resolvedAt?: Date;
}

export interface AuditLog {
  id: string;
  action: string;
  actor: string;
  actorType: 'user' | 'system' | 'admin';
  resource: string;
  resourceId: string;
  details: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
}

// API Request/Response Types
export interface CreateWalletRequest {
  telegramUser: TelegramUser;
}

export interface CreateWalletResponse {
  success: boolean;
  walletAddress: string;
  message?: string;
}

export interface GetBalanceRequest {
  telegramUserId: number;
}

export interface GetBalanceResponse {
  success: boolean;
  balance: WalletBalance;
  pendingRewards: PendingReward[];
}

export interface TransferRequest {
  fromTelegramUserId: number;
  toAddress: string;
  amount: string;
  tokenType: 'HAOX' | 'BNB';
  verificationCode?: string;
}

export interface TransferResponse {
  success: boolean;
  transactionId: string;
  estimatedConfirmationTime: number;
  message?: string;
}

export interface WithdrawRequest {
  telegramUserId: number;
  toAddress: string;
  amount: string;
  verificationCode: string;
  emailConfirmationCode?: string;
}

export interface WithdrawResponse {
  success: boolean;
  transactionId: string;
  requiresAdditionalVerification: boolean;
  message?: string;
}

export interface ClaimRewardRequest {
  telegramUserId: number;
  rewardIds: string[];
}

export interface ClaimRewardResponse {
  success: boolean;
  transactionId: string;
  totalAmount: string;
  message?: string;
}

export interface GetTransactionHistoryRequest {
  telegramUserId: number;
  page?: number;
  limit?: number;
  type?: Transaction['type'];
  status?: Transaction['status'];
}

export interface GetTransactionHistoryResponse {
  success: boolean;
  transactions: Transaction[];
  totalCount: number;
  hasMore: boolean;
}

export interface EstimateGasFeeRequest {
  fromAddress: string;
  toAddress: string;
  amount: string;
  tokenType: 'HAOX' | 'BNB';
}

export interface EstimateGasFeeResponse {
  success: boolean;
  estimates: GasFeeEstimate;
}

// Error Types
export class WalletError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 400
  ) {
    super(message);
    this.name = 'WalletError';
  }
}

export class InsufficientFundsError extends WalletError {
  constructor(available: string, required: string) {
    super(
      `Insufficient funds. Available: ${available}, Required: ${required}`,
      'INSUFFICIENT_FUNDS',
      400
    );
  }
}

export class InvalidAddressError extends WalletError {
  constructor(address: string) {
    super(`Invalid wallet address: ${address}`, 'INVALID_ADDRESS', 400);
  }
}

export class SecurityViolationError extends WalletError {
  constructor(reason: string) {
    super(`Security violation: ${reason}`, 'SECURITY_VIOLATION', 403);
  }
}

export class TransactionFailedError extends WalletError {
  constructor(reason: string, txHash?: string) {
    super(
      `Transaction failed: ${reason}${txHash ? ` (${txHash})` : ''}`,
      'TRANSACTION_FAILED',
      500
    );
  }
}

// Utility Types
export type WalletEventType = 
  | 'wallet_created'
  | 'balance_updated'
  | 'transaction_created'
  | 'transaction_confirmed'
  | 'transaction_failed'
  | 'reward_claimed'
  | 'security_alert'
  | 'withdrawal_requested'
  | 'withdrawal_approved'
  | 'withdrawal_rejected';

export interface WalletEvent {
  type: WalletEventType;
  telegramUserId: number;
  data: Record<string, any>;
  timestamp: Date;
}
