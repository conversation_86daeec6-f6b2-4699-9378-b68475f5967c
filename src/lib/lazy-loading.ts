/**
 * 代码分割和懒加载工具
 * 实现路由级别和组件级别的代码分割
 */

import { lazy, ComponentType, LazyExoticComponent } from 'react';
import { log } from './logger';

/**
 * 懒加载组件的配置选项
 */
export interface LazyLoadOptions {
  fallback?: ComponentType;
  retryCount?: number;
  retryDelay?: number;
  onError?: (error: Error) => void;
  onLoad?: () => void;
}

/**
 * 创建懒加载组件
 */
export function createLazyComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  options: LazyLoadOptions = {}
): LazyExoticComponent<T> {
  const {
    retryCount = 3,
    retryDelay = 1000,
    onError,
    onLoad,
  } = options;

  let retries = 0;

  const retryImport = async (): Promise<{ default: T }> => {
    try {
      const componentModule = await importFn();
      onLoad?.();
      log.debug('Lazy component loaded successfully');
      return componentModule;
    } catch (error) {
      retries++;
      log.warn(`Lazy component load failed (attempt ${retries}/${retryCount})`, { error });

      if (retries < retryCount) {
        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, retryDelay * retries));
        return retryImport();
      } else {
        // 达到最大重试次数
        const finalError = new Error(`Failed to load component after ${retryCount} attempts`);
        onError?.(finalError);
        log.error('Lazy component load failed permanently', finalError);
        throw finalError;
      }
    }
  };

  return lazy(retryImport);
}

/**
 * 预加载组件
 */
export function preloadComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>
): Promise<{ default: T }> {
  return importFn().catch(error => {
    log.warn('Component preload failed', { error });
    throw error;
  });
}

/**
 * 路由级别的懒加载组件
 */
export const LazyPages = {
  // 主要页面
  Dashboard: createLazyComponent(
    () => import('@/app/dashboard/page'),
    {
      onLoad: () => log.info('Dashboard page loaded'),
      onError: (error) => log.error('Dashboard page load failed', error),
    }
  ),

  TaskMarketplace: createLazyComponent(
    () => import('@/app/task-marketplace/page'),
    {
      onLoad: () => log.info('Task marketplace page loaded'),
      onError: (error) => log.error('Task marketplace page load failed', error),
    }
  ),

  Presale: createLazyComponent(
    () => import('@/app/presale/page'),
    {
      onLoad: () => log.info('Presale page loaded'),
      onError: (error) => log.error('Presale page load failed', error),
    }
  ),

  Merchants: createLazyComponent(
    () => import('@/app/merchants/page'),
    {
      onLoad: () => log.info('Merchants page loaded'),
      onError: (error) => log.error('Merchants page load failed', error),
    }
  ),

  // 认证页面
  Login: createLazyComponent(
    () => import('@/app/login/page'),
    {
      onLoad: () => log.info('Login page loaded'),
      onError: (error) => log.error('Login page load failed', error),
    }
  ),

  // 测试页面
  TestTelegram: createLazyComponent(
    () => import('@/app/test-telegram/page'),
    {
      onLoad: () => log.info('Test telegram page loaded'),
      onError: (error) => log.error('Test telegram page load failed', error),
    }
  ),
};

/**
 * 组件级别的懒加载
 */
export const LazyComponents = {
  // 钱包相关组件
  WalletModal: createLazyComponent(
    () => import('@/components/wallet/WalletModal'),
    {
      onLoad: () => log.debug('WalletModal component loaded'),
    }
  ),

  TransferForm: createLazyComponent(
    () => import('@/components/wallet/TransferForm'),
    {
      onLoad: () => log.debug('TransferForm component loaded'),
    }
  ),

  WithdrawForm: createLazyComponent(
    () => import('@/components/wallet/WithdrawForm'),
    {
      onLoad: () => log.debug('WithdrawForm component loaded'),
    }
  ),

  TransactionHistory: createLazyComponent(
    () => import('@/components/wallet/TransactionHistory'),
    {
      onLoad: () => log.debug('TransactionHistory component loaded'),
    }
  ),

  // 交易相关组件
  TradingChart: createLazyComponent(
    () => import('@/components/trading/TradingChart'),
    {
      onLoad: () => log.debug('TradingChart component loaded'),
    }
  ),

  HAOXPriceCard: createLazyComponent(
    () => import('@/components/trading/HAOXPriceCard'),
    {
      onLoad: () => log.debug('HAOXPriceCard component loaded'),
    }
  ),

  // 社交相关组件
  SocialBinding: createLazyComponent(
    () => import('@/components/social/SocialBinding'),
    {
      onLoad: () => log.debug('SocialBinding component loaded'),
    }
  ),

  TelegramChannelJoin: createLazyComponent(
    () => import('@/components/telegram/TelegramChannelJoin'),
    {
      onLoad: () => log.debug('TelegramChannelJoin component loaded'),
    }
  ),

  // 支付相关组件
  PaymentModal: createLazyComponent(
    () => import('@/components/payment/PaymentModal'),
    {
      onLoad: () => log.debug('PaymentModal component loaded'),
    }
  ),

  // 预售相关组件
  PresaleSection: createLazyComponent(
    () => import('@/components/presale/PresaleSection'),
    {
      onLoad: () => log.debug('PresaleSection component loaded'),
    }
  ),

  // 安全相关组件
  MultiFactorAuthModal: createLazyComponent(
    () => import('@/components/security/MultiFactorAuthModal'),
    {
      onLoad: () => log.debug('MultiFactorAuthModal component loaded'),
    }
  ),

  // 性能监控组件
  PerformanceMonitor: createLazyComponent(
    () => import('@/components/performance/PerformanceMonitor'),
    {
      onLoad: () => log.debug('PerformanceMonitor component loaded'),
    }
  ),
};

/**
 * 预加载关键组件
 */
export function preloadCriticalComponents(): void {
  // 在应用启动时预加载关键组件
  const criticalComponents = [
    () => import('@/components/wallet/WalletModal'),
    () => import('@/components/auth/LoginModal'),
    () => import('@/components/ui/Toast'),
  ];

  criticalComponents.forEach(importFn => {
    preloadComponent(importFn).catch(() => {
      // 预加载失败不影响应用运行
    });
  });
}

/**
 * 基于路由预加载组件
 */
export function preloadRouteComponents(pathname: string): void {
  const routePreloadMap: Record<string, Array<() => Promise<any>>> = {
    '/dashboard': [
      () => import('@/components/wallet/TransactionHistory'),
      () => import('@/components/trading/HAOXPriceCard'),
    ],
    '/task-marketplace': [
      () => import('@/components/social/SocialBinding'),
      () => import('@/components/telegram/TelegramChannelJoin'),
    ],
    '/presale': [
      () => import('@/components/presale/PresaleSection'),
      () => import('@/components/payment/PaymentModal'),
    ],
    '/merchants': [
      () => import('@/components/payment/PaymentModal'),
      () => import('@/components/security/MultiFactorAuthModal'),
    ],
  };

  const componentsToPreload = routePreloadMap[pathname];
  if (componentsToPreload) {
    componentsToPreload.forEach(importFn => {
      preloadComponent(importFn).catch(() => {
        // 预加载失败不影响应用运行
      });
    });
  }
}

/**
 * 智能预加载
 * 基于用户行为预测需要加载的组件
 */
export class SmartPreloader {
  private static instance: SmartPreloader;
  private preloadQueue: Set<() => Promise<any>> = new Set();
  private isPreloading = false;

  static getInstance(): SmartPreloader {
    if (!SmartPreloader.instance) {
      SmartPreloader.instance = new SmartPreloader();
    }
    return SmartPreloader.instance;
  }

  /**
   * 添加到预加载队列
   */
  addToQueue(importFn: () => Promise<any>): void {
    this.preloadQueue.add(importFn);
    this.processQueue();
  }

  /**
   * 处理预加载队列
   */
  private async processQueue(): Promise<void> {
    if (this.isPreloading || this.preloadQueue.size === 0) {
      return;
    }

    this.isPreloading = true;

    // 在空闲时间预加载
    if ('requestIdleCallback' in window) {
      window.requestIdleCallback(() => {
        this.executePreload();
      });
    } else {
      // 降级到 setTimeout
      setTimeout(() => {
        this.executePreload();
      }, 100);
    }
  }

  /**
   * 执行预加载
   */
  private async executePreload(): Promise<void> {
    const importFn = this.preloadQueue.values().next().value;
    if (importFn) {
      this.preloadQueue.delete(importFn);
      
      try {
        await importFn();
        log.debug('Component preloaded successfully');
      } catch (error) {
        log.warn('Component preload failed', { error });
      }
    }

    this.isPreloading = false;

    // 继续处理队列
    if (this.preloadQueue.size > 0) {
      this.processQueue();
    }
  }

  /**
   * 基于鼠标悬停预加载
   */
  onHover(importFn: () => Promise<any>): void {
    this.addToQueue(importFn);
  }

  /**
   * 基于用户交互预加载
   */
  onInteraction(importFn: () => Promise<any>): void {
    // 立即预加载
    preloadComponent(importFn).catch(() => {
      // 忽略错误
    });
  }
}

/**
 * 导出智能预加载器实例
 */
export const smartPreloader = SmartPreloader.getInstance();
