{"id": "be63f286e1e74a57f4e232dcbaa0aa90", "_format": "hh-sol-build-info-1", "solcVersion": "0.8.20", "solcLongVersion": "0.8.20+commit.a1b79de6", "input": {"language": "Solidity", "sources": {"@openzeppelin/contracts/access/Ownable.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.0.0) (access/Ownable.sol)\n\npragma solidity ^0.8.20;\n\nimport {Context} from \"../utils/Context.sol\";\n\n/**\n * @dev Contract module which provides a basic access control mechanism, where\n * there is an account (an owner) that can be granted exclusive access to\n * specific functions.\n *\n * The initial owner is set to the address provided by the deployer. This can\n * later be changed with {transferOwnership}.\n *\n * This module is used through inheritance. It will make available the modifier\n * `onlyOwner`, which can be applied to your functions to restrict their use to\n * the owner.\n */\nabstract contract Ownable is Context {\n    address private _owner;\n\n    /**\n     * @dev The caller account is not authorized to perform an operation.\n     */\n    error OwnableUnauthorizedAccount(address account);\n\n    /**\n     * @dev The owner is not a valid owner account. (eg. `address(0)`)\n     */\n    error OwnableInvalidOwner(address owner);\n\n    event OwnershipTransferred(address indexed previousOwner, address indexed newOwner);\n\n    /**\n     * @dev Initializes the contract setting the address provided by the deployer as the initial owner.\n     */\n    constructor(address initialOwner) {\n        if (initialOwner == address(0)) {\n            revert OwnableInvalidOwner(address(0));\n        }\n        _transferOwnership(initialOwner);\n    }\n\n    /**\n     * @dev Throws if called by any account other than the owner.\n     */\n    modifier onlyOwner() {\n        _checkOwner();\n        _;\n    }\n\n    /**\n     * @dev Returns the address of the current owner.\n     */\n    function owner() public view virtual returns (address) {\n        return _owner;\n    }\n\n    /**\n     * @dev Throws if the sender is not the owner.\n     */\n    function _checkOwner() internal view virtual {\n        if (owner() != _msgSender()) {\n            revert OwnableUnauthorizedAccount(_msgSender());\n        }\n    }\n\n    /**\n     * @dev Leaves the contract without owner. It will not be possible to call\n     * `onlyOwner` functions. Can only be called by the current owner.\n     *\n     * NOTE: Renouncing ownership will leave the contract without an owner,\n     * thereby disabling any functionality that is only available to the owner.\n     */\n    function renounceOwnership() public virtual onlyOwner {\n        _transferOwnership(address(0));\n    }\n\n    /**\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\n     * Can only be called by the current owner.\n     */\n    function transferOwnership(address newOwner) public virtual onlyOwner {\n        if (newOwner == address(0)) {\n            revert OwnableInvalidOwner(address(0));\n        }\n        _transferOwnership(newOwner);\n    }\n\n    /**\n     * @dev Transfers ownership of the contract to a new account (`newOwner`).\n     * Internal function without access restriction.\n     */\n    function _transferOwnership(address newOwner) internal virtual {\n        address oldOwner = _owner;\n        _owner = newOwner;\n        emit OwnershipTransferred(oldOwner, newOwner);\n    }\n}\n"}, "@openzeppelin/contracts/token/ERC20/IERC20.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.4.0) (token/ERC20/IERC20.sol)\n\npragma solidity >=0.4.16;\n\n/**\n * @dev Interface of the ERC-20 standard as defined in the ERC.\n */\ninterface IERC20 {\n    /**\n     * @dev Emitted when `value` tokens are moved from one account (`from`) to\n     * another (`to`).\n     *\n     * Note that `value` may be zero.\n     */\n    event Transfer(address indexed from, address indexed to, uint256 value);\n\n    /**\n     * @dev Emitted when the allowance of a `spender` for an `owner` is set by\n     * a call to {approve}. `value` is the new allowance.\n     */\n    event Approval(address indexed owner, address indexed spender, uint256 value);\n\n    /**\n     * @dev Returns the value of tokens in existence.\n     */\n    function totalSupply() external view returns (uint256);\n\n    /**\n     * @dev Returns the value of tokens owned by `account`.\n     */\n    function balanceOf(address account) external view returns (uint256);\n\n    /**\n     * @dev Moves a `value` amount of tokens from the caller's account to `to`.\n     *\n     * Returns a boolean value indicating whether the operation succeeded.\n     *\n     * Emits a {Transfer} event.\n     */\n    function transfer(address to, uint256 value) external returns (bool);\n\n    /**\n     * @dev Returns the remaining number of tokens that `spender` will be\n     * allowed to spend on behalf of `owner` through {transferFrom}. This is\n     * zero by default.\n     *\n     * This value changes when {approve} or {transferFrom} are called.\n     */\n    function allowance(address owner, address spender) external view returns (uint256);\n\n    /**\n     * @dev Sets a `value` amount of tokens as the allowance of `spender` over the\n     * caller's tokens.\n     *\n     * Returns a boolean value indicating whether the operation succeeded.\n     *\n     * IMPORTANT: Beware that changing an allowance with this method brings the risk\n     * that someone may use both the old and the new allowance by unfortunate\n     * transaction ordering. One possible solution to mitigate this race\n     * condition is to first reduce the spender's allowance to 0 and set the\n     * desired value afterwards:\n     * https://github.com/ethereum/EIPs/issues/20#issuecomment-*********\n     *\n     * Emits an {Approval} event.\n     */\n    function approve(address spender, uint256 value) external returns (bool);\n\n    /**\n     * @dev Moves a `value` amount of tokens from `from` to `to` using the\n     * allowance mechanism. `value` is then deducted from the caller's\n     * allowance.\n     *\n     * Returns a boolean value indicating whether the operation succeeded.\n     *\n     * Emits a {Transfer} event.\n     */\n    function transferFrom(address from, address to, uint256 value) external returns (bool);\n}\n"}, "@openzeppelin/contracts/utils/Context.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.0.1) (utils/Context.sol)\n\npragma solidity ^0.8.20;\n\n/**\n * @dev Provides information about the current execution context, including the\n * sender of the transaction and its data. While these are generally available\n * via msg.sender and msg.data, they should not be accessed in such a direct\n * manner, since when dealing with meta-transactions the account sending and\n * paying for execution may not be the actual sender (as far as an application\n * is concerned).\n *\n * This contract is only required for intermediate, library-like contracts.\n */\nabstract contract Context {\n    function _msgSender() internal view virtual returns (address) {\n        return msg.sender;\n    }\n\n    function _msgData() internal view virtual returns (bytes calldata) {\n        return msg.data;\n    }\n\n    function _contextSuffixLength() internal view virtual returns (uint256) {\n        return 0;\n    }\n}\n"}, "@openzeppelin/contracts/utils/Pausable.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.3.0) (utils/Pausable.sol)\n\npragma solidity ^0.8.20;\n\nimport {Context} from \"../utils/Context.sol\";\n\n/**\n * @dev Contract module which allows children to implement an emergency stop\n * mechanism that can be triggered by an authorized account.\n *\n * This module is used through inheritance. It will make available the\n * modifiers `whenNotPaused` and `whenPaused`, which can be applied to\n * the functions of your contract. Note that they will not be pausable by\n * simply including this module, only once the modifiers are put in place.\n */\nabstract contract Pausable is Context {\n    bool private _paused;\n\n    /**\n     * @dev Emitted when the pause is triggered by `account`.\n     */\n    event Paused(address account);\n\n    /**\n     * @dev Emitted when the pause is lifted by `account`.\n     */\n    event Unpaused(address account);\n\n    /**\n     * @dev The operation failed because the contract is paused.\n     */\n    error EnforcedPause();\n\n    /**\n     * @dev The operation failed because the contract is not paused.\n     */\n    error ExpectedPause();\n\n    /**\n     * @dev Modifier to make a function callable only when the contract is not paused.\n     *\n     * Requirements:\n     *\n     * - The contract must not be paused.\n     */\n    modifier whenNotPaused() {\n        _requireNotPaused();\n        _;\n    }\n\n    /**\n     * @dev Modifier to make a function callable only when the contract is paused.\n     *\n     * Requirements:\n     *\n     * - The contract must be paused.\n     */\n    modifier whenPaused() {\n        _requirePaused();\n        _;\n    }\n\n    /**\n     * @dev Returns true if the contract is paused, and false otherwise.\n     */\n    function paused() public view virtual returns (bool) {\n        return _paused;\n    }\n\n    /**\n     * @dev Throws if the contract is paused.\n     */\n    function _requireNotPaused() internal view virtual {\n        if (paused()) {\n            revert EnforcedPause();\n        }\n    }\n\n    /**\n     * @dev Throws if the contract is not paused.\n     */\n    function _requirePaused() internal view virtual {\n        if (!paused()) {\n            revert ExpectedPause();\n        }\n    }\n\n    /**\n     * @dev Triggers stopped state.\n     *\n     * Requirements:\n     *\n     * - The contract must not be paused.\n     */\n    function _pause() internal virtual whenNotPaused {\n        _paused = true;\n        emit Paused(_msgSender());\n    }\n\n    /**\n     * @dev Returns to normal state.\n     *\n     * Requirements:\n     *\n     * - The contract must be paused.\n     */\n    function _unpause() internal virtual whenPaused {\n        _paused = false;\n        emit Unpaused(_msgSender());\n    }\n}\n"}, "@openzeppelin/contracts/utils/ReentrancyGuard.sol": {"content": "// SPDX-License-Identifier: MIT\n// OpenZeppelin Contracts (last updated v5.1.0) (utils/ReentrancyGuard.sol)\n\npragma solidity ^0.8.20;\n\n/**\n * @dev Contract module that helps prevent reentrant calls to a function.\n *\n * Inheriting from `ReentrancyGuard` will make the {nonReentrant} modifier\n * available, which can be applied to functions to make sure there are no nested\n * (reentrant) calls to them.\n *\n * Note that because there is a single `nonReentrant` guard, functions marked as\n * `nonReentrant` may not call one another. This can be worked around by making\n * those functions `private`, and then adding `external` `nonReentrant` entry\n * points to them.\n *\n * TIP: If EIP-1153 (transient storage) is available on the chain you're deploying at,\n * consider using {ReentrancyGuardTransient} instead.\n *\n * TIP: If you would like to learn more about reentrancy and alternative ways\n * to protect against it, check out our blog post\n * https://blog.openzeppelin.com/reentrancy-after-istanbul/[Reentrancy After Istanbul].\n */\nabstract contract ReentrancyGuard {\n    // Booleans are more expensive than uint256 or any type that takes up a full\n    // word because each write operation emits an extra SLOAD to first read the\n    // slot's contents, replace the bits taken up by the boolean, and then write\n    // back. This is the compiler's defense against contract upgrades and\n    // pointer aliasing, and it cannot be disabled.\n\n    // The values being non-zero value makes deployment a bit more expensive,\n    // but in exchange the refund on every call to nonReentrant will be lower in\n    // amount. Since refunds are capped to a percentage of the total\n    // transaction's gas, it is best to keep them low in cases like this one, to\n    // increase the likelihood of the full refund coming into effect.\n    uint256 private constant NOT_ENTERED = 1;\n    uint256 private constant ENTERED = 2;\n\n    uint256 private _status;\n\n    /**\n     * @dev Unauthorized reentrant call.\n     */\n    error ReentrancyGuardReentrantCall();\n\n    constructor() {\n        _status = NOT_ENTERED;\n    }\n\n    /**\n     * @dev Prevents a contract from calling itself, directly or indirectly.\n     * Calling a `nonReentrant` function from another `nonReentrant`\n     * function is not supported. It is possible to prevent this from happening\n     * by making the `nonReentrant` function external, and making it call a\n     * `private` function that does the actual work.\n     */\n    modifier nonReentrant() {\n        _nonReentrantBefore();\n        _;\n        _nonReentrantAfter();\n    }\n\n    function _nonReentrantBefore() private {\n        // On the first call to nonReentrant, _status will be NOT_ENTERED\n        if (_status == ENTERED) {\n            revert ReentrancyGuardReentrantCall();\n        }\n\n        // Any calls to nonReentrant after this point will fail\n        _status = ENTERED;\n    }\n\n    function _nonReentrantAfter() private {\n        // By storing the original value once again, a refund is triggered (see\n        // https://eips.ethereum.org/EIPS/eip-2200)\n        _status = NOT_ENTERED;\n    }\n\n    /**\n     * @dev Returns true if the reentrancy guard is currently set to \"entered\", which indicates there is a\n     * `nonReentrant` function in the call stack.\n     */\n    function _reentrancyGuardEntered() internal view returns (bool) {\n        return _status == ENTERED;\n    }\n}\n"}, "contracts/HAOXVestingV2Ultra.sol": {"content": "// SPDX-License-Identifier: MIT\npragma solidity ^0.8.20;\n\nimport \"@openzeppelin/contracts/token/ERC20/IERC20.sol\";\nimport \"@openzeppelin/contracts/access/Ownable.sol\";\nimport \"@openzeppelin/contracts/utils/ReentrancyGuard.sol\";\nimport \"@openzeppelin/contracts/utils/Pausable.sol\";\n\n/**\n * @title HAOXVestingV2Ultra\n * @dev 超精简版HAOX代币解锁合约 - 极致成本优化版本\n * 仅保留最核心的安全功能，最大化降低部署成本\n */\ncontract HAOXVestingV2Ultra is Ownable, ReentrancyGuard, Pausable {\n    \n    // 基础常量\n    uint256 public constant TOTAL_ROUNDS = 31;\n    uint256 public constant PRICE_MAINTAIN_DURATION = 7 days;\n    uint256 public constant EMERGENCY_DELAY = 7 days;\n    uint256 public constant MAX_EMERGENCY_AMOUNT = 1000000 * 10**18;\n    \n    // 核心状态变量\n    IERC20 public immutable haoxToken;\n    address public immutable priceOracle;\n    address public immutable projectWallet;\n    address public immutable communityWallet;\n    \n    uint256 public currentRound = 1;\n    \n    // 轮次信息结构（超紧凑存储）\n    struct Round {\n        uint128 triggerPrice;      // 触发价格 (8位小数)\n        uint64 priceReachedTime;   // 价格达到时间\n        bool priceConditionMet;    // 价格条件是否满足\n        bool unlocked;             // 是否已解锁\n    }\n    \n    // 紧急提取请求结构（超精简版）\n    struct EmergencyRequest {\n        uint128 amount;\n        uint64 requestTime;\n        bool executed;\n    }\n    \n    // 状态映射（移除历史记录）\n    mapping(uint256 => Round) public rounds;\n    mapping(bytes32 => EmergencyRequest) public emergencyRequests;\n    mapping(address => bool) public emergencySigners;\n    \n    uint256 public requiredSignatures = 1;\n    \n    // 事件定义（超精简版）\n    event PriceConditionMet(uint256 indexed roundNumber, uint256 price);\n    event RoundUnlocked(uint256 indexed roundNumber, uint256 timestamp);\n    event EmergencyWithdrawRequested(bytes32 indexed requestId, uint256 amount);\n    event EmergencyWithdrawExecuted(bytes32 indexed requestId, uint256 amount);\n    \n    constructor(\n        address _haoxToken,\n        address _priceOracle,\n        address _projectWallet,\n        address _communityWallet\n    ) Ownable(msg.sender) {\n        require(_haoxToken != address(0), \"Invalid token address\");\n        require(_priceOracle != address(0), \"Invalid oracle address\");\n        require(_projectWallet != address(0), \"Invalid project wallet\");\n        require(_communityWallet != address(0), \"Invalid community wallet\");\n        \n        haoxToken = IERC20(_haoxToken);\n        priceOracle = _priceOracle;\n        projectWallet = _projectWallet;\n        communityWallet = _communityWallet;\n        \n        emergencySigners[msg.sender] = true;\n        \n        // 初始化31轮价格阶梯\n        _initializeRounds();\n    }\n    \n    /**\n     * @dev 初始化31轮价格阶梯\n     */\n    function _initializeRounds() internal {\n        uint256[31] memory prices = [\n            uint256(0.01e8), 0.02e8, 0.03e8, 0.04e8, 0.05e8, 0.06e8, 0.07e8, 0.08e8, 0.09e8, 0.10e8,\n            0.11e8, 0.12e8, 0.13e8, 0.14e8, 0.15e8, 0.16e8, 0.17e8, 0.18e8, 0.19e8, 0.20e8,\n            0.25e8, 0.30e8, 0.35e8, 0.40e8, 0.45e8, 0.50e8, 0.60e8, 0.70e8, 0.80e8, 0.90e8, 1.00e8\n        ];\n        \n        for (uint256 i = 0; i < TOTAL_ROUNDS; i++) {\n            rounds[i + 1] = Round({\n                triggerPrice: uint128(prices[i]),\n                priceReachedTime: 0,\n                priceConditionMet: false,\n                unlocked: false\n            });\n        }\n    }\n    \n    /**\n     * @dev 检查价格条件（超精简版）\n     */\n    function checkPriceCondition() external whenNotPaused {\n        uint256 roundNumber = currentRound;\n        if (roundNumber > TOTAL_ROUNDS) return;\n        \n        Round storage round = rounds[roundNumber];\n        if (round.unlocked) return;\n        \n        uint256 currentPrice = _getCurrentPrice();\n        bool conditionMet = currentPrice >= round.triggerPrice;\n        \n        if (conditionMet && !round.priceConditionMet) {\n            round.priceConditionMet = true;\n            round.priceReachedTime = uint64(block.timestamp);\n            emit PriceConditionMet(roundNumber, currentPrice);\n        } else if (!conditionMet && round.priceConditionMet) {\n            round.priceConditionMet = false;\n            round.priceReachedTime = 0;\n        }\n        \n        // 检查是否可以解锁\n        if (round.priceConditionMet && \n            block.timestamp >= round.priceReachedTime + PRICE_MAINTAIN_DURATION) {\n            _unlockRound(roundNumber);\n        }\n    }\n    \n    /**\n     * @dev 解锁轮次\n     */\n    function _unlockRound(uint256 roundNumber) internal {\n        Round storage round = rounds[roundNumber];\n        round.unlocked = true;\n        \n        emit RoundUnlocked(roundNumber, block.timestamp);\n        \n        if (roundNumber < TOTAL_ROUNDS) {\n            currentRound = roundNumber + 1;\n        }\n    }\n    \n    /**\n     * @dev 获取当前价格\n     */\n    function _getCurrentPrice() internal view returns (uint256) {\n        (bool success, bytes memory data) = priceOracle.staticcall(\n            abi.encodeWithSignature(\"getLatestPrice()\")\n        );\n        require(success && data.length >= 32, \"Price oracle call failed\");\n        return abi.decode(data, (uint256));\n    }\n    \n    /**\n     * @dev 紧急提取请求\n     */\n    function requestEmergencyWithdraw(\n        address token,\n        uint256 amount\n    ) external onlyOwner returns (bytes32) {\n        require(amount <= MAX_EMERGENCY_AMOUNT, \"Amount exceeds maximum\");\n        \n        bytes32 requestId = keccak256(abi.encodePacked(\n            token, amount, block.timestamp, msg.sender\n        ));\n        \n        emergencyRequests[requestId] = EmergencyRequest({\n            amount: uint128(amount),\n            requestTime: uint64(block.timestamp),\n            executed: false\n        });\n        \n        emit EmergencyWithdrawRequested(requestId, amount);\n        return requestId;\n    }\n    \n    /**\n     * @dev 执行紧急提取\n     */\n    function executeEmergencyWithdraw(\n        bytes32 requestId,\n        address token\n    ) external nonReentrant {\n        require(emergencySigners[msg.sender], \"Not authorized signer\");\n        \n        EmergencyRequest storage request = emergencyRequests[requestId];\n        require(!request.executed, \"Already executed\");\n        require(\n            block.timestamp >= request.requestTime + EMERGENCY_DELAY,\n            \"Time lock not expired\"\n        );\n        \n        request.executed = true;\n        \n        if (token == address(haoxToken)) {\n            haoxToken.transfer(projectWallet, request.amount);\n        } else {\n            IERC20(token).transfer(projectWallet, request.amount);\n        }\n        \n        emit EmergencyWithdrawExecuted(requestId, request.amount);\n    }\n    \n    /**\n     * @dev 管理紧急签名者\n     */\n    function setEmergencySigner(address signer, bool status) external onlyOwner {\n        emergencySigners[signer] = status;\n    }\n    \n    /**\n     * @dev 设置所需签名数量\n     */\n    function setRequiredSignatures(uint256 _required) external onlyOwner {\n        require(_required > 0, \"Invalid signature count\");\n        requiredSignatures = _required;\n    }\n    \n    /**\n     * @dev 暂停/恢复合约\n     */\n    function pause() external onlyOwner {\n        _pause();\n    }\n    \n    function unpause() external onlyOwner {\n        _unpause();\n    }\n    \n    /**\n     * @dev 获取轮次信息\n     */\n    function getRoundInfo(uint256 roundNumber) external view returns (\n        uint256 triggerPrice,\n        bool priceConditionMet,\n        bool unlocked,\n        uint256 priceReachedTime\n    ) {\n        Round memory round = rounds[roundNumber];\n        return (\n            round.triggerPrice,\n            round.priceConditionMet,\n            round.unlocked,\n            round.priceReachedTime\n        );\n    }\n    \n    /**\n     * @dev 获取解锁进度\n     */\n    function getUnlockProgress() external view returns (\n        uint256 totalRounds,\n        uint256 currentRoundNumber,\n        uint256 unlockedRounds\n    ) {\n        uint256 unlocked = 0;\n        for (uint256 i = 1; i <= TOTAL_ROUNDS; i++) {\n            if (rounds[i].unlocked) unlocked++;\n        }\n        \n        return (TOTAL_ROUNDS, currentRound, unlocked);\n    }\n    \n    /**\n     * @dev 获取当前价格（外部调用）\n     */\n    function getCurrentPrice() external view returns (uint256) {\n        return _getCurrentPrice();\n    }\n}\n"}}, "settings": {"optimizer": {"enabled": true, "runs": 200}, "viaIR": true, "evmVersion": "paris", "outputSelection": {"*": {"*": ["abi", "evm.bytecode", "evm.deployedBytecode", "evm.methodIdentifiers", "metadata"], "": ["ast"]}}}}, "output": {"sources": {"@openzeppelin/contracts/access/Ownable.sol": {"ast": {"absolutePath": "@openzeppelin/contracts/access/Ownable.sol", "exportedSymbols": {"Context": [255], "Ownable": [147]}, "id": 148, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 1, "literals": ["solidity", "^", "0.8", ".20"], "nodeType": "PragmaDirective", "src": "102:24:0"}, {"absolutePath": "@openzeppelin/contracts/utils/Context.sol", "file": "../utils/Context.sol", "id": 3, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 148, "sourceUnit": 256, "src": "128:45:0", "symbolAliases": [{"foreign": {"id": 2, "name": "Context", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 255, "src": "136:7:0", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"abstract": true, "baseContracts": [{"baseName": {"id": 5, "name": "Context", "nameLocations": ["692:7:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 255, "src": "692:7:0"}, "id": 6, "nodeType": "InheritanceSpecifier", "src": "692:7:0"}], "canonicalName": "Ownable", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 4, "nodeType": "StructuredDocumentation", "src": "175:487:0", "text": " @dev Contract module which provides a basic access control mechanism, where\n there is an account (an owner) that can be granted exclusive access to\n specific functions.\n The initial owner is set to the address provided by the deployer. This can\n later be changed with {transferOwnership}.\n This module is used through inheritance. It will make available the modifier\n `onlyOwner`, which can be applied to your functions to restrict their use to\n the owner."}, "fullyImplemented": true, "id": 147, "linearizedBaseContracts": [147, 255], "name": "Ownable", "nameLocation": "681:7:0", "nodeType": "ContractDefinition", "nodes": [{"constant": false, "id": 8, "mutability": "mutable", "name": "_owner", "nameLocation": "722:6:0", "nodeType": "VariableDeclaration", "scope": 147, "src": "706:22:0", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 7, "name": "address", "nodeType": "ElementaryTypeName", "src": "706:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "private"}, {"documentation": {"id": 9, "nodeType": "StructuredDocumentation", "src": "735:85:0", "text": " @dev The caller account is not authorized to perform an operation."}, "errorSelector": "118cdaa7", "id": 13, "name": "OwnableUnauthorizedAccount", "nameLocation": "831:26:0", "nodeType": "ErrorDefinition", "parameters": {"id": 12, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 11, "mutability": "mutable", "name": "account", "nameLocation": "866:7:0", "nodeType": "VariableDeclaration", "scope": 13, "src": "858:15:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 10, "name": "address", "nodeType": "ElementaryTypeName", "src": "858:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "857:17:0"}, "src": "825:50:0"}, {"documentation": {"id": 14, "nodeType": "StructuredDocumentation", "src": "881:82:0", "text": " @dev The owner is not a valid owner account. (eg. `address(0)`)"}, "errorSelector": "1e4fbdf7", "id": 18, "name": "OwnableInvalidOwner", "nameLocation": "974:19:0", "nodeType": "ErrorDefinition", "parameters": {"id": 17, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 16, "mutability": "mutable", "name": "owner", "nameLocation": "1002:5:0", "nodeType": "VariableDeclaration", "scope": 18, "src": "994:13:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 15, "name": "address", "nodeType": "ElementaryTypeName", "src": "994:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "993:15:0"}, "src": "968:41:0"}, {"anonymous": false, "eventSelector": "8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0", "id": 24, "name": "OwnershipTransferred", "nameLocation": "1021:20:0", "nodeType": "EventDefinition", "parameters": {"id": 23, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 20, "indexed": true, "mutability": "mutable", "name": "previousOwner", "nameLocation": "1058:13:0", "nodeType": "VariableDeclaration", "scope": 24, "src": "1042:29:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 19, "name": "address", "nodeType": "ElementaryTypeName", "src": "1042:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 22, "indexed": true, "mutability": "mutable", "name": "new<PERSON>wner", "nameLocation": "1089:8:0", "nodeType": "VariableDeclaration", "scope": 24, "src": "1073:24:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 21, "name": "address", "nodeType": "ElementaryTypeName", "src": "1073:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1041:57:0"}, "src": "1015:84:0"}, {"body": {"id": 49, "nodeType": "Block", "src": "1259:153:0", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 35, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 30, "name": "initialOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 27, "src": "1273:12:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"arguments": [{"hexValue": "30", "id": 33, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1297:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 32, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "1289:7:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 31, "name": "address", "nodeType": "ElementaryTypeName", "src": "1289:7:0", "typeDescriptions": {}}}, "id": 34, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1289:10:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "1273:26:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 44, "nodeType": "IfStatement", "src": "1269:95:0", "trueBody": {"id": 43, "nodeType": "Block", "src": "1301:63:0", "statements": [{"errorCall": {"arguments": [{"arguments": [{"hexValue": "30", "id": 39, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1350:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 38, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "1342:7:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 37, "name": "address", "nodeType": "ElementaryTypeName", "src": "1342:7:0", "typeDescriptions": {}}}, "id": 40, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1342:10:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 36, "name": "OwnableInvalidOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 18, "src": "1322:19:0", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$returns$__$", "typeString": "function (address) pure"}}, "id": 41, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1322:31:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 42, "nodeType": "RevertStatement", "src": "1315:38:0"}]}}, {"expression": {"arguments": [{"id": 46, "name": "initialOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 27, "src": "1392:12:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 45, "name": "_transferOwnership", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 146, "src": "1373:18:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$returns$__$", "typeString": "function (address)"}}, "id": 47, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1373:32:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 48, "nodeType": "ExpressionStatement", "src": "1373:32:0"}]}, "documentation": {"id": 25, "nodeType": "StructuredDocumentation", "src": "1105:115:0", "text": " @dev Initializes the contract setting the address provided by the deployer as the initial owner."}, "id": 50, "implemented": true, "kind": "constructor", "modifiers": [], "name": "", "nameLocation": "-1:-1:-1", "nodeType": "FunctionDefinition", "parameters": {"id": 28, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 27, "mutability": "mutable", "name": "initialOwner", "nameLocation": "1245:12:0", "nodeType": "VariableDeclaration", "scope": 50, "src": "1237:20:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 26, "name": "address", "nodeType": "ElementaryTypeName", "src": "1237:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1236:22:0"}, "returnParameters": {"id": 29, "nodeType": "ParameterList", "parameters": [], "src": "1259:0:0"}, "scope": 147, "src": "1225:187:0", "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"body": {"id": 57, "nodeType": "Block", "src": "1521:41:0", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 53, "name": "_checkOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 84, "src": "1531:11:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$__$", "typeString": "function () view"}}, "id": 54, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1531:13:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 55, "nodeType": "ExpressionStatement", "src": "1531:13:0"}, {"id": 56, "nodeType": "PlaceholderStatement", "src": "1554:1:0"}]}, "documentation": {"id": 51, "nodeType": "StructuredDocumentation", "src": "1418:77:0", "text": " @dev Throws if called by any account other than the owner."}, "id": 58, "name": "only<PERSON><PERSON>er", "nameLocation": "1509:9:0", "nodeType": "ModifierDefinition", "parameters": {"id": 52, "nodeType": "ParameterList", "parameters": [], "src": "1518:2:0"}, "src": "1500:62:0", "virtual": false, "visibility": "internal"}, {"body": {"id": 66, "nodeType": "Block", "src": "1693:30:0", "statements": [{"expression": {"id": 64, "name": "_owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8, "src": "1710:6:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 63, "id": 65, "nodeType": "Return", "src": "1703:13:0"}]}, "documentation": {"id": 59, "nodeType": "StructuredDocumentation", "src": "1568:65:0", "text": " @dev Returns the address of the current owner."}, "functionSelector": "8da5cb5b", "id": 67, "implemented": true, "kind": "function", "modifiers": [], "name": "owner", "nameLocation": "1647:5:0", "nodeType": "FunctionDefinition", "parameters": {"id": 60, "nodeType": "ParameterList", "parameters": [], "src": "1652:2:0"}, "returnParameters": {"id": 63, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 62, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 67, "src": "1684:7:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 61, "name": "address", "nodeType": "ElementaryTypeName", "src": "1684:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1683:9:0"}, "scope": 147, "src": "1638:85:0", "stateMutability": "view", "virtual": true, "visibility": "public"}, {"body": {"id": 83, "nodeType": "Block", "src": "1841:117:0", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 75, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"arguments": [], "expression": {"argumentTypes": [], "id": 71, "name": "owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 67, "src": "1855:5:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 72, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1855:7:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"arguments": [], "expression": {"argumentTypes": [], "id": 73, "name": "_msgSender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 237, "src": "1866:10:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 74, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1866:12:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "1855:23:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 82, "nodeType": "IfStatement", "src": "1851:101:0", "trueBody": {"id": 81, "nodeType": "Block", "src": "1880:72:0", "statements": [{"errorCall": {"arguments": [{"arguments": [], "expression": {"argumentTypes": [], "id": 77, "name": "_msgSender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 237, "src": "1928:10:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 78, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1928:12:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 76, "name": "OwnableUnauthorizedAccount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 13, "src": "1901:26:0", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$returns$__$", "typeString": "function (address) pure"}}, "id": 79, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1901:40:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 80, "nodeType": "RevertStatement", "src": "1894:47:0"}]}}]}, "documentation": {"id": 68, "nodeType": "StructuredDocumentation", "src": "1729:62:0", "text": " @dev Throws if the sender is not the owner."}, "id": 84, "implemented": true, "kind": "function", "modifiers": [], "name": "_checkOwner", "nameLocation": "1805:11:0", "nodeType": "FunctionDefinition", "parameters": {"id": 69, "nodeType": "ParameterList", "parameters": [], "src": "1816:2:0"}, "returnParameters": {"id": 70, "nodeType": "ParameterList", "parameters": [], "src": "1841:0:0"}, "scope": 147, "src": "1796:162:0", "stateMutability": "view", "virtual": true, "visibility": "internal"}, {"body": {"id": 97, "nodeType": "Block", "src": "2347:47:0", "statements": [{"expression": {"arguments": [{"arguments": [{"hexValue": "30", "id": 93, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2384:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 92, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2376:7:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 91, "name": "address", "nodeType": "ElementaryTypeName", "src": "2376:7:0", "typeDescriptions": {}}}, "id": 94, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2376:10:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 90, "name": "_transferOwnership", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 146, "src": "2357:18:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$returns$__$", "typeString": "function (address)"}}, "id": 95, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2357:30:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 96, "nodeType": "ExpressionStatement", "src": "2357:30:0"}]}, "documentation": {"id": 85, "nodeType": "StructuredDocumentation", "src": "1964:324:0", "text": " @dev Leaves the contract without owner. It will not be possible to call\n `onlyOwner` functions. Can only be called by the current owner.\n NOTE: Renouncing ownership will leave the contract without an owner,\n thereby disabling any functionality that is only available to the owner."}, "functionSelector": "715018a6", "id": 98, "implemented": true, "kind": "function", "modifiers": [{"id": 88, "kind": "modifierInvocation", "modifierName": {"id": 87, "name": "only<PERSON><PERSON>er", "nameLocations": ["2337:9:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 58, "src": "2337:9:0"}, "nodeType": "ModifierInvocation", "src": "2337:9:0"}], "name": "renounceOwnership", "nameLocation": "2302:17:0", "nodeType": "FunctionDefinition", "parameters": {"id": 86, "nodeType": "ParameterList", "parameters": [], "src": "2319:2:0"}, "returnParameters": {"id": 89, "nodeType": "ParameterList", "parameters": [], "src": "2347:0:0"}, "scope": 147, "src": "2293:101:0", "stateMutability": "nonpayable", "virtual": true, "visibility": "public"}, {"body": {"id": 125, "nodeType": "Block", "src": "2613:145:0", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 111, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 106, "name": "new<PERSON>wner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 101, "src": "2627:8:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"arguments": [{"hexValue": "30", "id": 109, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2647:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 108, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2639:7:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 107, "name": "address", "nodeType": "ElementaryTypeName", "src": "2639:7:0", "typeDescriptions": {}}}, "id": 110, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2639:10:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "2627:22:0", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 120, "nodeType": "IfStatement", "src": "2623:91:0", "trueBody": {"id": 119, "nodeType": "Block", "src": "2651:63:0", "statements": [{"errorCall": {"arguments": [{"arguments": [{"hexValue": "30", "id": 115, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2700:1:0", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 114, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2692:7:0", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 113, "name": "address", "nodeType": "ElementaryTypeName", "src": "2692:7:0", "typeDescriptions": {}}}, "id": 116, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2692:10:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 112, "name": "OwnableInvalidOwner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 18, "src": "2672:19:0", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$_t_address_$returns$__$", "typeString": "function (address) pure"}}, "id": 117, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2672:31:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 118, "nodeType": "RevertStatement", "src": "2665:38:0"}]}}, {"expression": {"arguments": [{"id": 122, "name": "new<PERSON>wner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 101, "src": "2742:8:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 121, "name": "_transferOwnership", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 146, "src": "2723:18:0", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_address_$returns$__$", "typeString": "function (address)"}}, "id": 123, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2723:28:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 124, "nodeType": "ExpressionStatement", "src": "2723:28:0"}]}, "documentation": {"id": 99, "nodeType": "StructuredDocumentation", "src": "2400:138:0", "text": " @dev Transfers ownership of the contract to a new account (`newOwner`).\n Can only be called by the current owner."}, "functionSelector": "f2fde38b", "id": 126, "implemented": true, "kind": "function", "modifiers": [{"id": 104, "kind": "modifierInvocation", "modifierName": {"id": 103, "name": "only<PERSON><PERSON>er", "nameLocations": ["2603:9:0"], "nodeType": "IdentifierPath", "referencedDeclaration": 58, "src": "2603:9:0"}, "nodeType": "ModifierInvocation", "src": "2603:9:0"}], "name": "transferOwnership", "nameLocation": "2552:17:0", "nodeType": "FunctionDefinition", "parameters": {"id": 102, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 101, "mutability": "mutable", "name": "new<PERSON>wner", "nameLocation": "2578:8:0", "nodeType": "VariableDeclaration", "scope": 126, "src": "2570:16:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 100, "name": "address", "nodeType": "ElementaryTypeName", "src": "2570:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2569:18:0"}, "returnParameters": {"id": 105, "nodeType": "ParameterList", "parameters": [], "src": "2613:0:0"}, "scope": 147, "src": "2543:215:0", "stateMutability": "nonpayable", "virtual": true, "visibility": "public"}, {"body": {"id": 145, "nodeType": "Block", "src": "2975:124:0", "statements": [{"assignments": [133], "declarations": [{"constant": false, "id": 133, "mutability": "mutable", "name": "old<PERSON>wner", "nameLocation": "2993:8:0", "nodeType": "VariableDeclaration", "scope": 145, "src": "2985:16:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 132, "name": "address", "nodeType": "ElementaryTypeName", "src": "2985:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "id": 135, "initialValue": {"id": 134, "name": "_owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8, "src": "3004:6:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "VariableDeclarationStatement", "src": "2985:25:0"}, {"expression": {"id": 138, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 136, "name": "_owner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 8, "src": "3020:6:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 137, "name": "new<PERSON>wner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 129, "src": "3029:8:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "3020:17:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 139, "nodeType": "ExpressionStatement", "src": "3020:17:0"}, {"eventCall": {"arguments": [{"id": 141, "name": "old<PERSON>wner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 133, "src": "3073:8:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 142, "name": "new<PERSON>wner", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 129, "src": "3083:8:0", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_address", "typeString": "address"}], "id": 140, "name": "OwnershipTransferred", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 24, "src": "3052:20:0", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$_t_address_$returns$__$", "typeString": "function (address,address)"}}, "id": 143, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3052:40:0", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 144, "nodeType": "EmitStatement", "src": "3047:45:0"}]}, "documentation": {"id": 127, "nodeType": "StructuredDocumentation", "src": "2764:143:0", "text": " @dev Transfers ownership of the contract to a new account (`newOwner`).\n Internal function without access restriction."}, "id": 146, "implemented": true, "kind": "function", "modifiers": [], "name": "_transferOwnership", "nameLocation": "2921:18:0", "nodeType": "FunctionDefinition", "parameters": {"id": 130, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 129, "mutability": "mutable", "name": "new<PERSON>wner", "nameLocation": "2948:8:0", "nodeType": "VariableDeclaration", "scope": 146, "src": "2940:16:0", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 128, "name": "address", "nodeType": "ElementaryTypeName", "src": "2940:7:0", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2939:18:0"}, "returnParameters": {"id": 131, "nodeType": "ParameterList", "parameters": [], "src": "2975:0:0"}, "scope": 147, "src": "2912:187:0", "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}], "scope": 148, "src": "663:2438:0", "usedErrors": [13, 18], "usedEvents": [24]}], "src": "102:3000:0"}, "id": 0}, "@openzeppelin/contracts/token/ERC20/IERC20.sol": {"ast": {"absolutePath": "@openzeppelin/contracts/token/ERC20/IERC20.sol", "exportedSymbols": {"IERC20": [225]}, "id": 226, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 149, "literals": ["solidity", ">=", "0.4", ".16"], "nodeType": "PragmaDirective", "src": "106:25:1"}, {"abstract": false, "baseContracts": [], "canonicalName": "IERC20", "contractDependencies": [], "contractKind": "interface", "documentation": {"id": 150, "nodeType": "StructuredDocumentation", "src": "133:71:1", "text": " @dev Interface of the ERC-20 standard as defined in the ERC."}, "fullyImplemented": false, "id": 225, "linearizedBaseContracts": [225], "name": "IERC20", "nameLocation": "215:6:1", "nodeType": "ContractDefinition", "nodes": [{"anonymous": false, "documentation": {"id": 151, "nodeType": "StructuredDocumentation", "src": "228:158:1", "text": " @dev Emitted when `value` tokens are moved from one account (`from`) to\n another (`to`).\n Note that `value` may be zero."}, "eventSelector": "ddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef", "id": 159, "name": "Transfer", "nameLocation": "397:8:1", "nodeType": "EventDefinition", "parameters": {"id": 158, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 153, "indexed": true, "mutability": "mutable", "name": "from", "nameLocation": "422:4:1", "nodeType": "VariableDeclaration", "scope": 159, "src": "406:20:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 152, "name": "address", "nodeType": "ElementaryTypeName", "src": "406:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 155, "indexed": true, "mutability": "mutable", "name": "to", "nameLocation": "444:2:1", "nodeType": "VariableDeclaration", "scope": 159, "src": "428:18:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 154, "name": "address", "nodeType": "ElementaryTypeName", "src": "428:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 157, "indexed": false, "mutability": "mutable", "name": "value", "nameLocation": "456:5:1", "nodeType": "VariableDeclaration", "scope": 159, "src": "448:13:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 156, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "448:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "405:57:1"}, "src": "391:72:1"}, {"anonymous": false, "documentation": {"id": 160, "nodeType": "StructuredDocumentation", "src": "469:148:1", "text": " @dev Emitted when the allowance of a `spender` for an `owner` is set by\n a call to {approve}. `value` is the new allowance."}, "eventSelector": "8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925", "id": 168, "name": "Approval", "nameLocation": "628:8:1", "nodeType": "EventDefinition", "parameters": {"id": 167, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 162, "indexed": true, "mutability": "mutable", "name": "owner", "nameLocation": "653:5:1", "nodeType": "VariableDeclaration", "scope": 168, "src": "637:21:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 161, "name": "address", "nodeType": "ElementaryTypeName", "src": "637:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 164, "indexed": true, "mutability": "mutable", "name": "spender", "nameLocation": "676:7:1", "nodeType": "VariableDeclaration", "scope": 168, "src": "660:23:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 163, "name": "address", "nodeType": "ElementaryTypeName", "src": "660:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 166, "indexed": false, "mutability": "mutable", "name": "value", "nameLocation": "693:5:1", "nodeType": "VariableDeclaration", "scope": 168, "src": "685:13:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 165, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "685:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "636:63:1"}, "src": "622:78:1"}, {"documentation": {"id": 169, "nodeType": "StructuredDocumentation", "src": "706:65:1", "text": " @dev Returns the value of tokens in existence."}, "functionSelector": "18160ddd", "id": 174, "implemented": false, "kind": "function", "modifiers": [], "name": "totalSupply", "nameLocation": "785:11:1", "nodeType": "FunctionDefinition", "parameters": {"id": 170, "nodeType": "ParameterList", "parameters": [], "src": "796:2:1"}, "returnParameters": {"id": 173, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 172, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 174, "src": "822:7:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 171, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "822:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "821:9:1"}, "scope": 225, "src": "776:55:1", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"documentation": {"id": 175, "nodeType": "StructuredDocumentation", "src": "837:71:1", "text": " @dev Returns the value of tokens owned by `account`."}, "functionSelector": "70a08231", "id": 182, "implemented": false, "kind": "function", "modifiers": [], "name": "balanceOf", "nameLocation": "922:9:1", "nodeType": "FunctionDefinition", "parameters": {"id": 178, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 177, "mutability": "mutable", "name": "account", "nameLocation": "940:7:1", "nodeType": "VariableDeclaration", "scope": 182, "src": "932:15:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 176, "name": "address", "nodeType": "ElementaryTypeName", "src": "932:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "931:17:1"}, "returnParameters": {"id": 181, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 180, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 182, "src": "972:7:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 179, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "972:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "971:9:1"}, "scope": 225, "src": "913:68:1", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"documentation": {"id": 183, "nodeType": "StructuredDocumentation", "src": "987:213:1", "text": " @dev Moves a `value` amount of tokens from the caller's account to `to`.\n Returns a boolean value indicating whether the operation succeeded.\n Emits a {Transfer} event."}, "functionSelector": "a9059cbb", "id": 192, "implemented": false, "kind": "function", "modifiers": [], "name": "transfer", "nameLocation": "1214:8:1", "nodeType": "FunctionDefinition", "parameters": {"id": 188, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 185, "mutability": "mutable", "name": "to", "nameLocation": "1231:2:1", "nodeType": "VariableDeclaration", "scope": 192, "src": "1223:10:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 184, "name": "address", "nodeType": "ElementaryTypeName", "src": "1223:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 187, "mutability": "mutable", "name": "value", "nameLocation": "1243:5:1", "nodeType": "VariableDeclaration", "scope": 192, "src": "1235:13:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 186, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1235:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1222:27:1"}, "returnParameters": {"id": 191, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 190, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 192, "src": "1268:4:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 189, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1268:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "1267:6:1"}, "scope": 225, "src": "1205:69:1", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"documentation": {"id": 193, "nodeType": "StructuredDocumentation", "src": "1280:264:1", "text": " @dev Returns the remaining number of tokens that `spender` will be\n allowed to spend on behalf of `owner` through {transferFrom}. This is\n zero by default.\n This value changes when {approve} or {transferFrom} are called."}, "functionSelector": "dd62ed3e", "id": 202, "implemented": false, "kind": "function", "modifiers": [], "name": "allowance", "nameLocation": "1558:9:1", "nodeType": "FunctionDefinition", "parameters": {"id": 198, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 195, "mutability": "mutable", "name": "owner", "nameLocation": "1576:5:1", "nodeType": "VariableDeclaration", "scope": 202, "src": "1568:13:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 194, "name": "address", "nodeType": "ElementaryTypeName", "src": "1568:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 197, "mutability": "mutable", "name": "spender", "nameLocation": "1591:7:1", "nodeType": "VariableDeclaration", "scope": 202, "src": "1583:15:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 196, "name": "address", "nodeType": "ElementaryTypeName", "src": "1583:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "1567:32:1"}, "returnParameters": {"id": 201, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 200, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 202, "src": "1623:7:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 199, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1623:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1622:9:1"}, "scope": 225, "src": "1549:83:1", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"documentation": {"id": 203, "nodeType": "StructuredDocumentation", "src": "1638:667:1", "text": " @dev Sets a `value` amount of tokens as the allowance of `spender` over the\n caller's tokens.\n Returns a boolean value indicating whether the operation succeeded.\n IMPORTANT: Beware that changing an allowance with this method brings the risk\n that someone may use both the old and the new allowance by unfortunate\n transaction ordering. One possible solution to mitigate this race\n condition is to first reduce the spender's allowance to 0 and set the\n desired value afterwards:\n https://github.com/ethereum/EIPs/issues/20#issuecomment-*********\n Emits an {Approval} event."}, "functionSelector": "095ea7b3", "id": 212, "implemented": false, "kind": "function", "modifiers": [], "name": "approve", "nameLocation": "2319:7:1", "nodeType": "FunctionDefinition", "parameters": {"id": 208, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 205, "mutability": "mutable", "name": "spender", "nameLocation": "2335:7:1", "nodeType": "VariableDeclaration", "scope": 212, "src": "2327:15:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 204, "name": "address", "nodeType": "ElementaryTypeName", "src": "2327:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 207, "mutability": "mutable", "name": "value", "nameLocation": "2352:5:1", "nodeType": "VariableDeclaration", "scope": 212, "src": "2344:13:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 206, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2344:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2326:32:1"}, "returnParameters": {"id": 211, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 210, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 212, "src": "2377:4:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 209, "name": "bool", "nodeType": "ElementaryTypeName", "src": "2377:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "2376:6:1"}, "scope": 225, "src": "2310:73:1", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"documentation": {"id": 213, "nodeType": "StructuredDocumentation", "src": "2389:297:1", "text": " @dev Moves a `value` amount of tokens from `from` to `to` using the\n allowance mechanism. `value` is then deducted from the caller's\n allowance.\n Returns a boolean value indicating whether the operation succeeded.\n Emits a {Transfer} event."}, "functionSelector": "23b872dd", "id": 224, "implemented": false, "kind": "function", "modifiers": [], "name": "transferFrom", "nameLocation": "2700:12:1", "nodeType": "FunctionDefinition", "parameters": {"id": 220, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 215, "mutability": "mutable", "name": "from", "nameLocation": "2721:4:1", "nodeType": "VariableDeclaration", "scope": 224, "src": "2713:12:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 214, "name": "address", "nodeType": "ElementaryTypeName", "src": "2713:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 217, "mutability": "mutable", "name": "to", "nameLocation": "2735:2:1", "nodeType": "VariableDeclaration", "scope": 224, "src": "2727:10:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 216, "name": "address", "nodeType": "ElementaryTypeName", "src": "2727:7:1", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 219, "mutability": "mutable", "name": "value", "nameLocation": "2747:5:1", "nodeType": "VariableDeclaration", "scope": 224, "src": "2739:13:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 218, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2739:7:1", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2712:41:1"}, "returnParameters": {"id": 223, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 222, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 224, "src": "2772:4:1", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 221, "name": "bool", "nodeType": "ElementaryTypeName", "src": "2772:4:1", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "2771:6:1"}, "scope": 225, "src": "2691:87:1", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}], "scope": 226, "src": "205:2575:1", "usedErrors": [], "usedEvents": [159, 168]}], "src": "106:2675:1"}, "id": 1}, "@openzeppelin/contracts/utils/Context.sol": {"ast": {"absolutePath": "@openzeppelin/contracts/utils/Context.sol", "exportedSymbols": {"Context": [255]}, "id": 256, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 227, "literals": ["solidity", "^", "0.8", ".20"], "nodeType": "PragmaDirective", "src": "101:24:2"}, {"abstract": true, "baseContracts": [], "canonicalName": "Context", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 228, "nodeType": "StructuredDocumentation", "src": "127:496:2", "text": " @dev Provides information about the current execution context, including the\n sender of the transaction and its data. While these are generally available\n via msg.sender and msg.data, they should not be accessed in such a direct\n manner, since when dealing with meta-transactions the account sending and\n paying for execution may not be the actual sender (as far as an application\n is concerned).\n This contract is only required for intermediate, library-like contracts."}, "fullyImplemented": true, "id": 255, "linearizedBaseContracts": [255], "name": "Context", "nameLocation": "642:7:2", "nodeType": "ContractDefinition", "nodes": [{"body": {"id": 236, "nodeType": "Block", "src": "718:34:2", "statements": [{"expression": {"expression": {"id": 233, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "735:3:2", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 234, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "739:6:2", "memberName": "sender", "nodeType": "MemberAccess", "src": "735:10:2", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "functionReturnParameters": 232, "id": 235, "nodeType": "Return", "src": "728:17:2"}]}, "id": 237, "implemented": true, "kind": "function", "modifiers": [], "name": "_msgSender", "nameLocation": "665:10:2", "nodeType": "FunctionDefinition", "parameters": {"id": 229, "nodeType": "ParameterList", "parameters": [], "src": "675:2:2"}, "returnParameters": {"id": 232, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 231, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 237, "src": "709:7:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 230, "name": "address", "nodeType": "ElementaryTypeName", "src": "709:7:2", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "708:9:2"}, "scope": 255, "src": "656:96:2", "stateMutability": "view", "virtual": true, "visibility": "internal"}, {"body": {"id": 245, "nodeType": "Block", "src": "825:32:2", "statements": [{"expression": {"expression": {"id": 242, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "842:3:2", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 243, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "846:4:2", "memberName": "data", "nodeType": "MemberAccess", "src": "842:8:2", "typeDescriptions": {"typeIdentifier": "t_bytes_calldata_ptr", "typeString": "bytes calldata"}}, "functionReturnParameters": 241, "id": 244, "nodeType": "Return", "src": "835:15:2"}]}, "id": 246, "implemented": true, "kind": "function", "modifiers": [], "name": "_msgData", "nameLocation": "767:8:2", "nodeType": "FunctionDefinition", "parameters": {"id": 238, "nodeType": "ParameterList", "parameters": [], "src": "775:2:2"}, "returnParameters": {"id": 241, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 240, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 246, "src": "809:14:2", "stateVariable": false, "storageLocation": "calldata", "typeDescriptions": {"typeIdentifier": "t_bytes_calldata_ptr", "typeString": "bytes"}, "typeName": {"id": 239, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "809:5:2", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "src": "808:16:2"}, "scope": 255, "src": "758:99:2", "stateMutability": "view", "virtual": true, "visibility": "internal"}, {"body": {"id": 253, "nodeType": "Block", "src": "935:25:2", "statements": [{"expression": {"hexValue": "30", "id": 251, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "952:1:2", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "functionReturnParameters": 250, "id": 252, "nodeType": "Return", "src": "945:8:2"}]}, "id": 254, "implemented": true, "kind": "function", "modifiers": [], "name": "_contextSuffixLength", "nameLocation": "872:20:2", "nodeType": "FunctionDefinition", "parameters": {"id": 247, "nodeType": "ParameterList", "parameters": [], "src": "892:2:2"}, "returnParameters": {"id": 250, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 249, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 254, "src": "926:7:2", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 248, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "926:7:2", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "925:9:2"}, "scope": 255, "src": "863:97:2", "stateMutability": "view", "virtual": true, "visibility": "internal"}], "scope": 256, "src": "624:338:2", "usedErrors": [], "usedEvents": []}], "src": "101:862:2"}, "id": 2}, "@openzeppelin/contracts/utils/Pausable.sol": {"ast": {"absolutePath": "@openzeppelin/contracts/utils/Pausable.sol", "exportedSymbols": {"Context": [255], "Pausable": [363]}, "id": 364, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 257, "literals": ["solidity", "^", "0.8", ".20"], "nodeType": "PragmaDirective", "src": "102:24:3"}, {"absolutePath": "@openzeppelin/contracts/utils/Context.sol", "file": "../utils/Context.sol", "id": 259, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 364, "sourceUnit": 256, "src": "128:45:3", "symbolAliases": [{"foreign": {"id": 258, "name": "Context", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 255, "src": "136:7:3", "typeDescriptions": {}}, "nameLocation": "-1:-1:-1"}], "unitAlias": ""}, {"abstract": true, "baseContracts": [{"baseName": {"id": 261, "name": "Context", "nameLocations": ["645:7:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 255, "src": "645:7:3"}, "id": 262, "nodeType": "InheritanceSpecifier", "src": "645:7:3"}], "canonicalName": "Pausable", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 260, "nodeType": "StructuredDocumentation", "src": "175:439:3", "text": " @dev Contract module which allows children to implement an emergency stop\n mechanism that can be triggered by an authorized account.\n This module is used through inheritance. It will make available the\n modifiers `whenNotPaused` and `whenPaused`, which can be applied to\n the functions of your contract. Note that they will not be pausable by\n simply including this module, only once the modifiers are put in place."}, "fullyImplemented": true, "id": 363, "linearizedBaseContracts": [363, 255], "name": "Pausable", "nameLocation": "633:8:3", "nodeType": "ContractDefinition", "nodes": [{"constant": false, "id": 264, "mutability": "mutable", "name": "_paused", "nameLocation": "672:7:3", "nodeType": "VariableDeclaration", "scope": 363, "src": "659:20:3", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 263, "name": "bool", "nodeType": "ElementaryTypeName", "src": "659:4:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "private"}, {"anonymous": false, "documentation": {"id": 265, "nodeType": "StructuredDocumentation", "src": "686:73:3", "text": " @dev Emitted when the pause is triggered by `account`."}, "eventSelector": "62e78cea01bee320cd4e420270b5ea74000d11b0c9f74754ebdbfc544b05a258", "id": 269, "name": "Paused", "nameLocation": "770:6:3", "nodeType": "EventDefinition", "parameters": {"id": 268, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 267, "indexed": false, "mutability": "mutable", "name": "account", "nameLocation": "785:7:3", "nodeType": "VariableDeclaration", "scope": 269, "src": "777:15:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 266, "name": "address", "nodeType": "ElementaryTypeName", "src": "777:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "776:17:3"}, "src": "764:30:3"}, {"anonymous": false, "documentation": {"id": 270, "nodeType": "StructuredDocumentation", "src": "800:70:3", "text": " @dev Emitted when the pause is lifted by `account`."}, "eventSelector": "5db9ee0a495bf2e6ff9c91a7834c1ba4fdd244a5e8aa4e537bd38aeae4b073aa", "id": 274, "name": "Unpaused", "nameLocation": "881:8:3", "nodeType": "EventDefinition", "parameters": {"id": 273, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 272, "indexed": false, "mutability": "mutable", "name": "account", "nameLocation": "898:7:3", "nodeType": "VariableDeclaration", "scope": 274, "src": "890:15:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 271, "name": "address", "nodeType": "ElementaryTypeName", "src": "890:7:3", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "889:17:3"}, "src": "875:32:3"}, {"documentation": {"id": 275, "nodeType": "StructuredDocumentation", "src": "913:76:3", "text": " @dev The operation failed because the contract is paused."}, "errorSelector": "d93c0665", "id": 277, "name": "EnforcedPause", "nameLocation": "1000:13:3", "nodeType": "ErrorDefinition", "parameters": {"id": 276, "nodeType": "ParameterList", "parameters": [], "src": "1013:2:3"}, "src": "994:22:3"}, {"documentation": {"id": 278, "nodeType": "StructuredDocumentation", "src": "1022:80:3", "text": " @dev The operation failed because the contract is not paused."}, "errorSelector": "8dfc202b", "id": 280, "name": "ExpectedPause", "nameLocation": "1113:13:3", "nodeType": "ErrorDefinition", "parameters": {"id": 279, "nodeType": "ParameterList", "parameters": [], "src": "1126:2:3"}, "src": "1107:22:3"}, {"body": {"id": 287, "nodeType": "Block", "src": "1340:47:3", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 283, "name": "_requireNotPaused", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 317, "src": "1350:17:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$__$", "typeString": "function () view"}}, "id": 284, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1350:19:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 285, "nodeType": "ExpressionStatement", "src": "1350:19:3"}, {"id": 286, "nodeType": "PlaceholderStatement", "src": "1379:1:3"}]}, "documentation": {"id": 281, "nodeType": "StructuredDocumentation", "src": "1135:175:3", "text": " @dev Modifier to make a function callable only when the contract is not paused.\n Requirements:\n - The contract must not be paused."}, "id": 288, "name": "whenNotPaused", "nameLocation": "1324:13:3", "nodeType": "ModifierDefinition", "parameters": {"id": 282, "nodeType": "ParameterList", "parameters": [], "src": "1337:2:3"}, "src": "1315:72:3", "virtual": false, "visibility": "internal"}, {"body": {"id": 295, "nodeType": "Block", "src": "1587:44:3", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 291, "name": "_requirePaused", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 330, "src": "1597:14:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$__$", "typeString": "function () view"}}, "id": 292, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1597:16:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 293, "nodeType": "ExpressionStatement", "src": "1597:16:3"}, {"id": 294, "nodeType": "PlaceholderStatement", "src": "1623:1:3"}]}, "documentation": {"id": 289, "nodeType": "StructuredDocumentation", "src": "1393:167:3", "text": " @dev Modifier to make a function callable only when the contract is paused.\n Requirements:\n - The contract must be paused."}, "id": 296, "name": "whenPaused", "nameLocation": "1574:10:3", "nodeType": "ModifierDefinition", "parameters": {"id": 290, "nodeType": "ParameterList", "parameters": [], "src": "1584:2:3"}, "src": "1565:66:3", "virtual": false, "visibility": "internal"}, {"body": {"id": 304, "nodeType": "Block", "src": "1779:31:3", "statements": [{"expression": {"id": 302, "name": "_paused", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 264, "src": "1796:7:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 301, "id": 303, "nodeType": "Return", "src": "1789:14:3"}]}, "documentation": {"id": 297, "nodeType": "StructuredDocumentation", "src": "1637:84:3", "text": " @dev Returns true if the contract is paused, and false otherwise."}, "functionSelector": "5c975abb", "id": 305, "implemented": true, "kind": "function", "modifiers": [], "name": "paused", "nameLocation": "1735:6:3", "nodeType": "FunctionDefinition", "parameters": {"id": 298, "nodeType": "ParameterList", "parameters": [], "src": "1741:2:3"}, "returnParameters": {"id": 301, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 300, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 305, "src": "1773:4:3", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 299, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1773:4:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "1772:6:3"}, "scope": 363, "src": "1726:84:3", "stateMutability": "view", "virtual": true, "visibility": "public"}, {"body": {"id": 316, "nodeType": "Block", "src": "1929:77:3", "statements": [{"condition": {"arguments": [], "expression": {"argumentTypes": [], "id": 309, "name": "paused", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 305, "src": "1943:6:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_bool_$", "typeString": "function () view returns (bool)"}}, "id": 310, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1943:8:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 315, "nodeType": "IfStatement", "src": "1939:61:3", "trueBody": {"id": 314, "nodeType": "Block", "src": "1953:47:3", "statements": [{"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 311, "name": "EnforcedPause", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 277, "src": "1974:13:3", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 312, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "1974:15:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 313, "nodeType": "RevertStatement", "src": "1967:22:3"}]}}]}, "documentation": {"id": 306, "nodeType": "StructuredDocumentation", "src": "1816:57:3", "text": " @dev Throws if the contract is paused."}, "id": 317, "implemented": true, "kind": "function", "modifiers": [], "name": "_requireNotPaused", "nameLocation": "1887:17:3", "nodeType": "FunctionDefinition", "parameters": {"id": 307, "nodeType": "ParameterList", "parameters": [], "src": "1904:2:3"}, "returnParameters": {"id": 308, "nodeType": "ParameterList", "parameters": [], "src": "1929:0:3"}, "scope": 363, "src": "1878:128:3", "stateMutability": "view", "virtual": true, "visibility": "internal"}, {"body": {"id": 329, "nodeType": "Block", "src": "2126:78:3", "statements": [{"condition": {"id": 323, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "2140:9:3", "subExpression": {"arguments": [], "expression": {"argumentTypes": [], "id": 321, "name": "paused", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 305, "src": "2141:6:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_bool_$", "typeString": "function () view returns (bool)"}}, "id": 322, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2141:8:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 328, "nodeType": "IfStatement", "src": "2136:62:3", "trueBody": {"id": 327, "nodeType": "Block", "src": "2151:47:3", "statements": [{"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 324, "name": "ExpectedPause", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 280, "src": "2172:13:3", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 325, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2172:15:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 326, "nodeType": "RevertStatement", "src": "2165:22:3"}]}}]}, "documentation": {"id": 318, "nodeType": "StructuredDocumentation", "src": "2012:61:3", "text": " @dev Throws if the contract is not paused."}, "id": 330, "implemented": true, "kind": "function", "modifiers": [], "name": "_requirePaused", "nameLocation": "2087:14:3", "nodeType": "FunctionDefinition", "parameters": {"id": 319, "nodeType": "ParameterList", "parameters": [], "src": "2101:2:3"}, "returnParameters": {"id": 320, "nodeType": "ParameterList", "parameters": [], "src": "2126:0:3"}, "scope": 363, "src": "2078:126:3", "stateMutability": "view", "virtual": true, "visibility": "internal"}, {"body": {"id": 345, "nodeType": "Block", "src": "2388:66:3", "statements": [{"expression": {"id": 338, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 336, "name": "_paused", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 264, "src": "2398:7:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "74727565", "id": 337, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "2408:4:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "src": "2398:14:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 339, "nodeType": "ExpressionStatement", "src": "2398:14:3"}, {"eventCall": {"arguments": [{"arguments": [], "expression": {"argumentTypes": [], "id": 341, "name": "_msgSender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 237, "src": "2434:10:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 342, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2434:12:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 340, "name": "Paused", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 269, "src": "2427:6:3", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$returns$__$", "typeString": "function (address)"}}, "id": 343, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2427:20:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 344, "nodeType": "EmitStatement", "src": "2422:25:3"}]}, "documentation": {"id": 331, "nodeType": "StructuredDocumentation", "src": "2210:124:3", "text": " @dev Triggers stopped state.\n Requirements:\n - The contract must not be paused."}, "id": 346, "implemented": true, "kind": "function", "modifiers": [{"id": 334, "kind": "modifierInvocation", "modifierName": {"id": 333, "name": "whenNotPaused", "nameLocations": ["2374:13:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 288, "src": "2374:13:3"}, "nodeType": "ModifierInvocation", "src": "2374:13:3"}], "name": "_pause", "nameLocation": "2348:6:3", "nodeType": "FunctionDefinition", "parameters": {"id": 332, "nodeType": "ParameterList", "parameters": [], "src": "2354:2:3"}, "returnParameters": {"id": 335, "nodeType": "ParameterList", "parameters": [], "src": "2388:0:3"}, "scope": 363, "src": "2339:115:3", "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}, {"body": {"id": 361, "nodeType": "Block", "src": "2634:69:3", "statements": [{"expression": {"id": 354, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 352, "name": "_paused", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 264, "src": "2644:7:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "66616c7365", "id": 353, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "2654:5:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}, "src": "2644:15:3", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 355, "nodeType": "ExpressionStatement", "src": "2644:15:3"}, {"eventCall": {"arguments": [{"arguments": [], "expression": {"argumentTypes": [], "id": 357, "name": "_msgSender", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 237, "src": "2683:10:3", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_address_$", "typeString": "function () view returns (address)"}}, "id": 358, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2683:12:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 356, "name": "Unpaused", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 274, "src": "2674:8:3", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_address_$returns$__$", "typeString": "function (address)"}}, "id": 359, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2674:22:3", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 360, "nodeType": "EmitStatement", "src": "2669:27:3"}]}, "documentation": {"id": 347, "nodeType": "StructuredDocumentation", "src": "2460:121:3", "text": " @dev Returns to normal state.\n Requirements:\n - The contract must be paused."}, "id": 362, "implemented": true, "kind": "function", "modifiers": [{"id": 350, "kind": "modifierInvocation", "modifierName": {"id": 349, "name": "whenPaused", "nameLocations": ["2623:10:3"], "nodeType": "IdentifierPath", "referencedDeclaration": 296, "src": "2623:10:3"}, "nodeType": "ModifierInvocation", "src": "2623:10:3"}], "name": "_unpause", "nameLocation": "2595:8:3", "nodeType": "FunctionDefinition", "parameters": {"id": 348, "nodeType": "ParameterList", "parameters": [], "src": "2603:2:3"}, "returnParameters": {"id": 351, "nodeType": "ParameterList", "parameters": [], "src": "2634:0:3"}, "scope": 363, "src": "2586:117:3", "stateMutability": "nonpayable", "virtual": true, "visibility": "internal"}], "scope": 364, "src": "615:2090:3", "usedErrors": [277, 280], "usedEvents": [269, 274]}], "src": "102:2604:3"}, "id": 3}, "@openzeppelin/contracts/utils/ReentrancyGuard.sol": {"ast": {"absolutePath": "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "exportedSymbols": {"ReentrancyGuard": [432]}, "id": 433, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 365, "literals": ["solidity", "^", "0.8", ".20"], "nodeType": "PragmaDirective", "src": "109:24:4"}, {"abstract": true, "baseContracts": [], "canonicalName": "Reentrancy<PERSON><PERSON>", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 366, "nodeType": "StructuredDocumentation", "src": "135:894:4", "text": " @dev Contract module that helps prevent reentrant calls to a function.\n Inheriting from `ReentrancyGuard` will make the {nonReentrant} modifier\n available, which can be applied to functions to make sure there are no nested\n (reentrant) calls to them.\n Note that because there is a single `nonReentrant` guard, functions marked as\n `nonReentrant` may not call one another. This can be worked around by making\n those functions `private`, and then adding `external` `nonReentrant` entry\n points to them.\n TIP: If EIP-1153 (transient storage) is available on the chain you're deploying at,\n consider using {ReentrancyGuardTransient} instead.\n TIP: If you would like to learn more about reentrancy and alternative ways\n to protect against it, check out our blog post\n https://blog.openzeppelin.com/reentrancy-after-istanbul/[Reentrancy After Istanbul]."}, "fullyImplemented": true, "id": 432, "linearizedBaseContracts": [432], "name": "Reentrancy<PERSON><PERSON>", "nameLocation": "1048:15:4", "nodeType": "ContractDefinition", "nodes": [{"constant": true, "id": 369, "mutability": "constant", "name": "NOT_ENTERED", "nameLocation": "1843:11:4", "nodeType": "VariableDeclaration", "scope": 432, "src": "1818:40:4", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 367, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1818:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": {"hexValue": "31", "id": 368, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1857:1:4", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "visibility": "private"}, {"constant": true, "id": 372, "mutability": "constant", "name": "ENTERED", "nameLocation": "1889:7:4", "nodeType": "VariableDeclaration", "scope": 432, "src": "1864:36:4", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 370, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1864:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": {"hexValue": "32", "id": 371, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1899:1:4", "typeDescriptions": {"typeIdentifier": "t_rational_2_by_1", "typeString": "int_const 2"}, "value": "2"}, "visibility": "private"}, {"constant": false, "id": 374, "mutability": "mutable", "name": "_status", "nameLocation": "1923:7:4", "nodeType": "VariableDeclaration", "scope": 432, "src": "1907:23:4", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 373, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1907:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "private"}, {"documentation": {"id": 375, "nodeType": "StructuredDocumentation", "src": "1937:52:4", "text": " @dev Unauthorized reentrant call."}, "errorSelector": "3ee5aeb5", "id": 377, "name": "ReentrancyGuardReentrantCall", "nameLocation": "2000:28:4", "nodeType": "ErrorDefinition", "parameters": {"id": 376, "nodeType": "ParameterList", "parameters": [], "src": "2028:2:4"}, "src": "1994:37:4"}, {"body": {"id": 384, "nodeType": "Block", "src": "2051:38:4", "statements": [{"expression": {"id": 382, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 380, "name": "_status", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 374, "src": "2061:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 381, "name": "NOT_ENTERED", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 369, "src": "2071:11:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2061:21:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 383, "nodeType": "ExpressionStatement", "src": "2061:21:4"}]}, "id": 385, "implemented": true, "kind": "constructor", "modifiers": [], "name": "", "nameLocation": "-1:-1:-1", "nodeType": "FunctionDefinition", "parameters": {"id": 378, "nodeType": "ParameterList", "parameters": [], "src": "2048:2:4"}, "returnParameters": {"id": 379, "nodeType": "ParameterList", "parameters": [], "src": "2051:0:4"}, "scope": 432, "src": "2037:52:4", "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"body": {"id": 395, "nodeType": "Block", "src": "2490:79:4", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 388, "name": "_nonReentrantBefore", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 412, "src": "2500:19:4", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$__$returns$__$", "typeString": "function ()"}}, "id": 389, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2500:21:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 390, "nodeType": "ExpressionStatement", "src": "2500:21:4"}, {"id": 391, "nodeType": "PlaceholderStatement", "src": "2531:1:4"}, {"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 392, "name": "_nonReentrantAfter", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 420, "src": "2542:18:4", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$__$returns$__$", "typeString": "function ()"}}, "id": 393, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2542:20:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 394, "nodeType": "ExpressionStatement", "src": "2542:20:4"}]}, "documentation": {"id": 386, "nodeType": "StructuredDocumentation", "src": "2095:366:4", "text": " @dev Prevents a contract from calling itself, directly or indirectly.\n Calling a `nonReentrant` function from another `nonReentrant`\n function is not supported. It is possible to prevent this from happening\n by making the `nonReentrant` function external, and making it call a\n `private` function that does the actual work."}, "id": 396, "name": "nonReentrant", "nameLocation": "2475:12:4", "nodeType": "ModifierDefinition", "parameters": {"id": 387, "nodeType": "ParameterList", "parameters": [], "src": "2487:2:4"}, "src": "2466:103:4", "virtual": false, "visibility": "internal"}, {"body": {"id": 411, "nodeType": "Block", "src": "2614:268:4", "statements": [{"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 401, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 399, "name": "_status", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 374, "src": "2702:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"id": 400, "name": "ENTERED", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 372, "src": "2713:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2702:18:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 406, "nodeType": "IfStatement", "src": "2698:86:4", "trueBody": {"id": 405, "nodeType": "Block", "src": "2722:62:4", "statements": [{"errorCall": {"arguments": [], "expression": {"argumentTypes": [], "id": 402, "name": "ReentrancyGuardReentrantCall", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 377, "src": "2743:28:4", "typeDescriptions": {"typeIdentifier": "t_function_error_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 403, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2743:30:4", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 404, "nodeType": "RevertStatement", "src": "2736:37:4"}]}}, {"expression": {"id": 409, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 407, "name": "_status", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 374, "src": "2858:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 408, "name": "ENTERED", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 372, "src": "2868:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "2858:17:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 410, "nodeType": "ExpressionStatement", "src": "2858:17:4"}]}, "id": 412, "implemented": true, "kind": "function", "modifiers": [], "name": "_nonReentrantBefore", "nameLocation": "2584:19:4", "nodeType": "FunctionDefinition", "parameters": {"id": 397, "nodeType": "ParameterList", "parameters": [], "src": "2603:2:4"}, "returnParameters": {"id": 398, "nodeType": "ParameterList", "parameters": [], "src": "2614:0:4"}, "scope": 432, "src": "2575:307:4", "stateMutability": "nonpayable", "virtual": false, "visibility": "private"}, {"body": {"id": 419, "nodeType": "Block", "src": "2926:170:4", "statements": [{"expression": {"id": 417, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 415, "name": "_status", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 374, "src": "3068:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 416, "name": "NOT_ENTERED", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 369, "src": "3078:11:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3068:21:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 418, "nodeType": "ExpressionStatement", "src": "3068:21:4"}]}, "id": 420, "implemented": true, "kind": "function", "modifiers": [], "name": "_nonReentrantAfter", "nameLocation": "2897:18:4", "nodeType": "FunctionDefinition", "parameters": {"id": 413, "nodeType": "ParameterList", "parameters": [], "src": "2915:2:4"}, "returnParameters": {"id": 414, "nodeType": "ParameterList", "parameters": [], "src": "2926:0:4"}, "scope": 432, "src": "2888:208:4", "stateMutability": "nonpayable", "virtual": false, "visibility": "private"}, {"body": {"id": 430, "nodeType": "Block", "src": "3339:42:4", "statements": [{"expression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 428, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 426, "name": "_status", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 374, "src": "3356:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"id": 427, "name": "ENTERED", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 372, "src": "3367:7:4", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3356:18:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "functionReturnParameters": 425, "id": 429, "nodeType": "Return", "src": "3349:25:4"}]}, "documentation": {"id": 421, "nodeType": "StructuredDocumentation", "src": "3102:168:4", "text": " @dev Returns true if the reentrancy guard is currently set to \"entered\", which indicates there is a\n `nonReentrant` function in the call stack."}, "id": 431, "implemented": true, "kind": "function", "modifiers": [], "name": "_reentrancyGuardEntered", "nameLocation": "3284:23:4", "nodeType": "FunctionDefinition", "parameters": {"id": 422, "nodeType": "ParameterList", "parameters": [], "src": "3307:2:4"}, "returnParameters": {"id": 425, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 424, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 431, "src": "3333:4:4", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 423, "name": "bool", "nodeType": "ElementaryTypeName", "src": "3333:4:4", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "3332:6:4"}, "scope": 432, "src": "3275:106:4", "stateMutability": "view", "virtual": false, "visibility": "internal"}], "scope": 433, "src": "1030:2353:4", "usedErrors": [377], "usedEvents": []}], "src": "109:3275:4"}, "id": 4}, "contracts/HAOXVestingV2Ultra.sol": {"ast": {"absolutePath": "contracts/HAOXVestingV2Ultra.sol", "exportedSymbols": {"Context": [255], "HAOXVestingV2Ultra": [1146], "IERC20": [225], "Ownable": [147], "Pausable": [363], "ReentrancyGuard": [432]}, "id": 1147, "license": "MIT", "nodeType": "SourceUnit", "nodes": [{"id": 434, "literals": ["solidity", "^", "0.8", ".20"], "nodeType": "PragmaDirective", "src": "32:24:5"}, {"absolutePath": "@openzeppelin/contracts/token/ERC20/IERC20.sol", "file": "@openzeppelin/contracts/token/ERC20/IERC20.sol", "id": 435, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 1147, "sourceUnit": 226, "src": "58:56:5", "symbolAliases": [], "unitAlias": ""}, {"absolutePath": "@openzeppelin/contracts/access/Ownable.sol", "file": "@openzeppelin/contracts/access/Ownable.sol", "id": 436, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 1147, "sourceUnit": 148, "src": "115:52:5", "symbolAliases": [], "unitAlias": ""}, {"absolutePath": "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "file": "@openzeppelin/contracts/utils/ReentrancyGuard.sol", "id": 437, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 1147, "sourceUnit": 433, "src": "168:59:5", "symbolAliases": [], "unitAlias": ""}, {"absolutePath": "@openzeppelin/contracts/utils/Pausable.sol", "file": "@openzeppelin/contracts/utils/Pausable.sol", "id": 438, "nameLocation": "-1:-1:-1", "nodeType": "ImportDirective", "scope": 1147, "sourceUnit": 364, "src": "228:52:5", "symbolAliases": [], "unitAlias": ""}, {"abstract": false, "baseContracts": [{"baseName": {"id": 440, "name": "Ownable", "nameLocations": ["487:7:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 147, "src": "487:7:5"}, "id": 441, "nodeType": "InheritanceSpecifier", "src": "487:7:5"}, {"baseName": {"id": 442, "name": "Reentrancy<PERSON><PERSON>", "nameLocations": ["496:15:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 432, "src": "496:15:5"}, "id": 443, "nodeType": "InheritanceSpecifier", "src": "496:15:5"}, {"baseName": {"id": 444, "name": "Pausable", "nameLocations": ["513:8:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 363, "src": "513:8:5"}, "id": 445, "nodeType": "InheritanceSpecifier", "src": "513:8:5"}], "canonicalName": "HAOXVestingV2Ultra", "contractDependencies": [], "contractKind": "contract", "documentation": {"id": 439, "nodeType": "StructuredDocumentation", "src": "282:173:5", "text": " @title HAOXVestingV2Ultra\n @dev 超精简版HAOX代币解锁合约 - 极致成本优化版本\n 仅保留最核心的安全功能，最大化降低部署成本"}, "fullyImplemented": true, "id": 1146, "linearizedBaseContracts": [1146, 363, 432, 147, 255], "name": "HAOXVestingV2Ultra", "nameLocation": "465:18:5", "nodeType": "ContractDefinition", "nodes": [{"constant": true, "functionSelector": "ce8dc388", "id": 448, "mutability": "constant", "name": "TOTAL_ROUNDS", "nameLocation": "577:12:5", "nodeType": "VariableDeclaration", "scope": 1146, "src": "553:41:5", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 446, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "553:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": {"hexValue": "3331", "id": 447, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "592:2:5", "typeDescriptions": {"typeIdentifier": "t_rational_31_by_1", "typeString": "int_const 31"}, "value": "31"}, "visibility": "public"}, {"constant": true, "functionSelector": "540797a5", "id": 451, "mutability": "constant", "name": "PRICE_MAINTAIN_DURATION", "nameLocation": "624:23:5", "nodeType": "VariableDeclaration", "scope": 1146, "src": "600:56:5", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 449, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "600:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": {"hexValue": "37", "id": 450, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "650:6:5", "subdenomination": "days", "typeDescriptions": {"typeIdentifier": "t_rational_604800_by_1", "typeString": "int_const 604800"}, "value": "7"}, "visibility": "public"}, {"constant": true, "functionSelector": "82944e2d", "id": 454, "mutability": "constant", "name": "EMERGENCY_DELAY", "nameLocation": "686:15:5", "nodeType": "VariableDeclaration", "scope": 1146, "src": "662:48:5", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 452, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "662:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": {"hexValue": "37", "id": 453, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "704:6:5", "subdenomination": "days", "typeDescriptions": {"typeIdentifier": "t_rational_604800_by_1", "typeString": "int_const 604800"}, "value": "7"}, "visibility": "public"}, {"constant": true, "functionSelector": "cf9d226e", "id": 461, "mutability": "constant", "name": "MAX_EMERGENCY_AMOUNT", "nameLocation": "740:20:5", "nodeType": "VariableDeclaration", "scope": 1146, "src": "716:63:5", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 455, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "716:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": {"commonType": {"typeIdentifier": "t_rational_1000000000000000000000000_by_1", "typeString": "int_const 1000000000000000000000000"}, "id": 460, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "leftExpression": {"hexValue": "31303030303030", "id": 456, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "763:7:5", "typeDescriptions": {"typeIdentifier": "t_rational_1000000_by_1", "typeString": "int_const 1000000"}, "value": "1000000"}, "nodeType": "BinaryOperation", "operator": "*", "rightExpression": {"commonType": {"typeIdentifier": "t_rational_1000000000000000000_by_1", "typeString": "int_const 1000000000000000000"}, "id": 459, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "leftExpression": {"hexValue": "3130", "id": 457, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "773:2:5", "typeDescriptions": {"typeIdentifier": "t_rational_10_by_1", "typeString": "int_const 10"}, "value": "10"}, "nodeType": "BinaryOperation", "operator": "**", "rightExpression": {"hexValue": "3138", "id": 458, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "777:2:5", "typeDescriptions": {"typeIdentifier": "t_rational_18_by_1", "typeString": "int_const 18"}, "value": "18"}, "src": "773:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_1000000000000000000_by_1", "typeString": "int_const 1000000000000000000"}}, "src": "763:16:5", "typeDescriptions": {"typeIdentifier": "t_rational_1000000000000000000000000_by_1", "typeString": "int_const 1000000000000000000000000"}}, "visibility": "public"}, {"constant": false, "functionSelector": "25dbec9d", "id": 464, "mutability": "immutable", "name": "haoxToken", "nameLocation": "840:9:5", "nodeType": "VariableDeclaration", "scope": 1146, "src": "816:33:5", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$225", "typeString": "contract IERC20"}, "typeName": {"id": 463, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 462, "name": "IERC20", "nameLocations": ["816:6:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 225, "src": "816:6:5"}, "referencedDeclaration": 225, "src": "816:6:5", "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$225", "typeString": "contract IERC20"}}, "visibility": "public"}, {"constant": false, "functionSelector": "2630c12f", "id": 466, "mutability": "immutable", "name": "priceOracle", "nameLocation": "880:11:5", "nodeType": "VariableDeclaration", "scope": 1146, "src": "855:36:5", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 465, "name": "address", "nodeType": "ElementaryTypeName", "src": "855:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "public"}, {"constant": false, "functionSelector": "beb08ab9", "id": 468, "mutability": "immutable", "name": "projectWallet", "nameLocation": "922:13:5", "nodeType": "VariableDeclaration", "scope": 1146, "src": "897:38:5", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 467, "name": "address", "nodeType": "ElementaryTypeName", "src": "897:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "public"}, {"constant": false, "functionSelector": "c7574839", "id": 470, "mutability": "immutable", "name": "communityWallet", "nameLocation": "966:15:5", "nodeType": "VariableDeclaration", "scope": 1146, "src": "941:40:5", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 469, "name": "address", "nodeType": "ElementaryTypeName", "src": "941:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "public"}, {"constant": false, "functionSelector": "8a19c8bc", "id": 473, "mutability": "mutable", "name": "currentRound", "nameLocation": "1007:12:5", "nodeType": "VariableDeclaration", "scope": 1146, "src": "992:31:5", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 471, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "992:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": {"hexValue": "31", "id": 472, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1022:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "visibility": "public"}, {"canonicalName": "HAOXVestingV2Ultra.Round", "id": 482, "members": [{"constant": false, "id": 475, "mutability": "mutable", "name": "triggerPrice", "nameLocation": "1112:12:5", "nodeType": "VariableDeclaration", "scope": 482, "src": "1104:20:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint128", "typeString": "uint128"}, "typeName": {"id": 474, "name": "uint128", "nodeType": "ElementaryTypeName", "src": "1104:7:5", "typeDescriptions": {"typeIdentifier": "t_uint128", "typeString": "uint128"}}, "visibility": "internal"}, {"constant": false, "id": 477, "mutability": "mutable", "name": "priceReachedTime", "nameLocation": "1175:16:5", "nodeType": "VariableDeclaration", "scope": 482, "src": "1168:23:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "typeName": {"id": 476, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "1168:6:5", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "visibility": "internal"}, {"constant": false, "id": 479, "mutability": "mutable", "name": "priceConditionMet", "nameLocation": "1230:17:5", "nodeType": "VariableDeclaration", "scope": 482, "src": "1225:22:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 478, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1225:4:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, {"constant": false, "id": 481, "mutability": "mutable", "name": "unlocked", "nameLocation": "1293:8:5", "nodeType": "VariableDeclaration", "scope": 482, "src": "1288:13:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 480, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1288:4:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "name": "Round", "nameLocation": "1088:5:5", "nodeType": "StructDefinition", "scope": 1146, "src": "1081:258:5", "visibility": "public"}, {"canonicalName": "HAOXVestingV2Ultra.EmergencyRequest", "id": 489, "members": [{"constant": false, "id": 484, "mutability": "mutable", "name": "amount", "nameLocation": "1441:6:5", "nodeType": "VariableDeclaration", "scope": 489, "src": "1433:14:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint128", "typeString": "uint128"}, "typeName": {"id": 483, "name": "uint128", "nodeType": "ElementaryTypeName", "src": "1433:7:5", "typeDescriptions": {"typeIdentifier": "t_uint128", "typeString": "uint128"}}, "visibility": "internal"}, {"constant": false, "id": 486, "mutability": "mutable", "name": "requestTime", "nameLocation": "1464:11:5", "nodeType": "VariableDeclaration", "scope": 489, "src": "1457:18:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}, "typeName": {"id": 485, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "1457:6:5", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "visibility": "internal"}, {"constant": false, "id": 488, "mutability": "mutable", "name": "executed", "nameLocation": "1490:8:5", "nodeType": "VariableDeclaration", "scope": 489, "src": "1485:13:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 487, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1485:4:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "name": "EmergencyRequest", "nameLocation": "1406:16:5", "nodeType": "StructDefinition", "scope": 1146, "src": "1399:106:5", "visibility": "public"}, {"constant": false, "functionSelector": "8c65c81f", "id": 494, "mutability": "mutable", "name": "rounds", "nameLocation": "1592:6:5", "nodeType": "VariableDeclaration", "scope": 1146, "src": "1559:39:5", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_Round_$482_storage_$", "typeString": "mapping(uint256 => struct HAOXVestingV2Ultra.Round)"}, "typeName": {"id": 493, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 490, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1567:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Mapping", "src": "1559:25:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_Round_$482_storage_$", "typeString": "mapping(uint256 => struct HAOXVestingV2Ultra.Round)"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 492, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 491, "name": "Round", "nameLocations": ["1578:5:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 482, "src": "1578:5:5"}, "referencedDeclaration": 482, "src": "1578:5:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Round_$482_storage_ptr", "typeString": "struct HAOXVestingV2Ultra.Round"}}}, "visibility": "public"}, {"constant": false, "functionSelector": "0557ac41", "id": 499, "mutability": "mutable", "name": "emergencyRequests", "nameLocation": "1648:17:5", "nodeType": "VariableDeclaration", "scope": 1146, "src": "1604:61:5", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_struct$_EmergencyRequest_$489_storage_$", "typeString": "mapping(bytes32 => struct HAOXVestingV2Ultra.EmergencyRequest)"}, "typeName": {"id": 498, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 495, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "1612:7:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "Mapping", "src": "1604:36:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_struct$_EmergencyRequest_$489_storage_$", "typeString": "mapping(bytes32 => struct HAOXVestingV2Ultra.EmergencyRequest)"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 497, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 496, "name": "EmergencyRequest", "nameLocations": ["1623:16:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 489, "src": "1623:16:5"}, "referencedDeclaration": 489, "src": "1623:16:5", "typeDescriptions": {"typeIdentifier": "t_struct$_EmergencyRequest_$489_storage_ptr", "typeString": "struct HAOXVestingV2Ultra.EmergencyRequest"}}}, "visibility": "public"}, {"constant": false, "functionSelector": "d0f694cb", "id": 503, "mutability": "mutable", "name": "emergencySigners", "nameLocation": "1703:16:5", "nodeType": "VariableDeclaration", "scope": 1146, "src": "1671:48:5", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}, "typeName": {"id": 502, "keyName": "", "keyNameLocation": "-1:-1:-1", "keyType": {"id": 500, "name": "address", "nodeType": "ElementaryTypeName", "src": "1679:7:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Mapping", "src": "1671:24:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}, "valueName": "", "valueNameLocation": "-1:-1:-1", "valueType": {"id": 501, "name": "bool", "nodeType": "ElementaryTypeName", "src": "1690:4:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}}, "visibility": "public"}, {"constant": false, "functionSelector": "8d068043", "id": 506, "mutability": "mutable", "name": "requiredSignatures", "nameLocation": "1745:18:5", "nodeType": "VariableDeclaration", "scope": 1146, "src": "1730:37:5", "stateVariable": true, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 504, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1730:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "value": {"hexValue": "31", "id": 505, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "1766:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "visibility": "public"}, {"anonymous": false, "eventSelector": "a8303d852039dd03da732ca0626a63b998f0add380069633c8f94c2214a3ec01", "id": 512, "name": "PriceConditionMet", "nameLocation": "1822:17:5", "nodeType": "EventDefinition", "parameters": {"id": 511, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 508, "indexed": true, "mutability": "mutable", "name": "roundNumber", "nameLocation": "1856:11:5", "nodeType": "VariableDeclaration", "scope": 512, "src": "1840:27:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 507, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1840:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 510, "indexed": false, "mutability": "mutable", "name": "price", "nameLocation": "1877:5:5", "nodeType": "VariableDeclaration", "scope": 512, "src": "1869:13:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 509, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1869:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1839:44:5"}, "src": "1816:68:5"}, {"anonymous": false, "eventSelector": "21a31473ac3b93c8bfdfe4f59540208cf835cd32697afe67593091c7e15d949c", "id": 518, "name": "RoundUnlocked", "nameLocation": "1895:13:5", "nodeType": "EventDefinition", "parameters": {"id": 517, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 514, "indexed": true, "mutability": "mutable", "name": "roundNumber", "nameLocation": "1925:11:5", "nodeType": "VariableDeclaration", "scope": 518, "src": "1909:27:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 513, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1909:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 516, "indexed": false, "mutability": "mutable", "name": "timestamp", "nameLocation": "1946:9:5", "nodeType": "VariableDeclaration", "scope": 518, "src": "1938:17:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 515, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "1938:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1908:48:5"}, "src": "1889:68:5"}, {"anonymous": false, "eventSelector": "95f1700a944fd9e7555649be76088141ecc47f9eb270e4321e235c7e9f78394f", "id": 524, "name": "EmergencyWithdrawRequested", "nameLocation": "1968:26:5", "nodeType": "EventDefinition", "parameters": {"id": 523, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 520, "indexed": true, "mutability": "mutable", "name": "requestId", "nameLocation": "2011:9:5", "nodeType": "VariableDeclaration", "scope": 524, "src": "1995:25:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 519, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "1995:7:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 522, "indexed": false, "mutability": "mutable", "name": "amount", "nameLocation": "2030:6:5", "nodeType": "VariableDeclaration", "scope": 524, "src": "2022:14:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 521, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2022:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "1994:43:5"}, "src": "1962:76:5"}, {"anonymous": false, "eventSelector": "52dd3ccddd493d9fb871d4f3af402c398e59ac2e679c482008b36d4e84e89a1e", "id": 530, "name": "EmergencyWithdrawExecuted", "nameLocation": "2049:25:5", "nodeType": "EventDefinition", "parameters": {"id": 529, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 526, "indexed": true, "mutability": "mutable", "name": "requestId", "nameLocation": "2091:9:5", "nodeType": "VariableDeclaration", "scope": 530, "src": "2075:25:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 525, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "2075:7:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 528, "indexed": false, "mutability": "mutable", "name": "amount", "nameLocation": "2110:6:5", "nodeType": "VariableDeclaration", "scope": 530, "src": "2102:14:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 527, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2102:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "2074:43:5"}, "src": "2043:75:5"}, {"body": {"id": 613, "nodeType": "Block", "src": "2290:595:5", "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 551, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 546, "name": "_haoxToken", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 532, "src": "2308:10:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"arguments": [{"hexValue": "30", "id": 549, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2330:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 548, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2322:7:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 547, "name": "address", "nodeType": "ElementaryTypeName", "src": "2322:7:5", "typeDescriptions": {}}}, "id": 550, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2322:10:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "2308:24:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "496e76616c696420746f6b656e2061646472657373", "id": 552, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "2334:23:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_d34df3e6e5f402d3417b1a16a0a8a7541b184d7fb338e177a15236f4037e3743", "typeString": "literal_string \"Invalid token address\""}, "value": "Invalid token address"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_d34df3e6e5f402d3417b1a16a0a8a7541b184d7fb338e177a15236f4037e3743", "typeString": "literal_string \"Invalid token address\""}], "id": 545, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "2300:7:5", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 553, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2300:58:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 554, "nodeType": "ExpressionStatement", "src": "2300:58:5"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 561, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 556, "name": "_priceOracle", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 534, "src": "2376:12:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"arguments": [{"hexValue": "30", "id": 559, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2400:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 558, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2392:7:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 557, "name": "address", "nodeType": "ElementaryTypeName", "src": "2392:7:5", "typeDescriptions": {}}}, "id": 560, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2392:10:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "2376:26:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "496e76616c6964206f7261636c652061646472657373", "id": 562, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "2404:24:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_b5c25d5c4ca6addfa23ca815554bb7bee3a9885ce01501a275168a62aa7cded3", "typeString": "literal_string \"Invalid oracle address\""}, "value": "Invalid oracle address"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_b5c25d5c4ca6addfa23ca815554bb7bee3a9885ce01501a275168a62aa7cded3", "typeString": "literal_string \"Invalid oracle address\""}], "id": 555, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "2368:7:5", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 563, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2368:61:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 564, "nodeType": "ExpressionStatement", "src": "2368:61:5"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 571, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 566, "name": "_projectWallet", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 536, "src": "2447:14:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"arguments": [{"hexValue": "30", "id": 569, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2473:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 568, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2465:7:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 567, "name": "address", "nodeType": "ElementaryTypeName", "src": "2465:7:5", "typeDescriptions": {}}}, "id": 570, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2465:10:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "2447:28:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "496e76616c69642070726f6a6563742077616c6c6574", "id": 572, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "2477:24:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_3a3f94df6d15db77f2805bb59b9e0afe0c4650a28437780e8225365c242eca43", "typeString": "literal_string \"Invalid project wallet\""}, "value": "Invalid project wallet"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_3a3f94df6d15db77f2805bb59b9e0afe0c4650a28437780e8225365c242eca43", "typeString": "literal_string \"Invalid project wallet\""}], "id": 565, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "2439:7:5", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 573, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2439:63:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 574, "nodeType": "ExpressionStatement", "src": "2439:63:5"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 581, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 576, "name": "_communityWallet", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 538, "src": "2520:16:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "!=", "rightExpression": {"arguments": [{"hexValue": "30", "id": 579, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "2548:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}], "id": 578, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "2540:7:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 577, "name": "address", "nodeType": "ElementaryTypeName", "src": "2540:7:5", "typeDescriptions": {}}}, "id": 580, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2540:10:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "2520:30:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "496e76616c696420636f6d6d756e6974792077616c6c6574", "id": 582, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "2552:26:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_0e6aa9e82df9ed735f6dba8fe33ecac07fe34e5696eb7e317a220817b24d3a2b", "typeString": "literal_string \"Invalid community wallet\""}, "value": "Invalid community wallet"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_0e6aa9e82df9ed735f6dba8fe33ecac07fe34e5696eb7e317a220817b24d3a2b", "typeString": "literal_string \"Invalid community wallet\""}], "id": 575, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "2512:7:5", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 583, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2512:67:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 584, "nodeType": "ExpressionStatement", "src": "2512:67:5"}, {"expression": {"id": 589, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 585, "name": "haoxToken", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 464, "src": "2598:9:5", "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$225", "typeString": "contract IERC20"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"id": 587, "name": "_haoxToken", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 532, "src": "2617:10:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 586, "name": "IERC20", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 225, "src": "2610:6:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IERC20_$225_$", "typeString": "type(contract IERC20)"}}, "id": 588, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2610:18:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$225", "typeString": "contract IERC20"}}, "src": "2598:30:5", "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$225", "typeString": "contract IERC20"}}, "id": 590, "nodeType": "ExpressionStatement", "src": "2598:30:5"}, {"expression": {"id": 593, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 591, "name": "priceOracle", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 466, "src": "2638:11:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 592, "name": "_priceOracle", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 534, "src": "2652:12:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "2638:26:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 594, "nodeType": "ExpressionStatement", "src": "2638:26:5"}, {"expression": {"id": 597, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 595, "name": "projectWallet", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 468, "src": "2674:13:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 596, "name": "_projectWallet", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 536, "src": "2690:14:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "2674:30:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 598, "nodeType": "ExpressionStatement", "src": "2674:30:5"}, {"expression": {"id": 601, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 599, "name": "communityWallet", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 470, "src": "2714:15:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 600, "name": "_communityWallet", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 538, "src": "2732:16:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "2714:34:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 602, "nodeType": "ExpressionStatement", "src": "2714:34:5"}, {"expression": {"id": 608, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 603, "name": "emergencySigners", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 503, "src": "2767:16:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 606, "indexExpression": {"expression": {"id": 604, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "2784:3:5", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 605, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2788:6:5", "memberName": "sender", "nodeType": "MemberAccess", "src": "2784:10:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "2767:28:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "74727565", "id": 607, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "2798:4:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "src": "2767:35:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 609, "nodeType": "ExpressionStatement", "src": "2767:35:5"}, {"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 610, "name": "_initializeRounds", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 691, "src": "2859:17:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$__$returns$__$", "typeString": "function ()"}}, "id": 611, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "2859:19:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 612, "nodeType": "ExpressionStatement", "src": "2859:19:5"}]}, "id": 614, "implemented": true, "kind": "constructor", "modifiers": [{"arguments": [{"expression": {"id": 541, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "2278:3:5", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 542, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "2282:6:5", "memberName": "sender", "nodeType": "MemberAccess", "src": "2278:10:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "id": 543, "kind": "baseConstructorSpecifier", "modifierName": {"id": 540, "name": "Ownable", "nameLocations": ["2270:7:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 147, "src": "2270:7:5"}, "nodeType": "ModifierInvocation", "src": "2270:19:5"}], "name": "", "nameLocation": "-1:-1:-1", "nodeType": "FunctionDefinition", "parameters": {"id": 539, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 532, "mutability": "mutable", "name": "_haoxToken", "nameLocation": "2157:10:5", "nodeType": "VariableDeclaration", "scope": 614, "src": "2149:18:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 531, "name": "address", "nodeType": "ElementaryTypeName", "src": "2149:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 534, "mutability": "mutable", "name": "_priceOracle", "nameLocation": "2185:12:5", "nodeType": "VariableDeclaration", "scope": 614, "src": "2177:20:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 533, "name": "address", "nodeType": "ElementaryTypeName", "src": "2177:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 536, "mutability": "mutable", "name": "_projectWallet", "nameLocation": "2215:14:5", "nodeType": "VariableDeclaration", "scope": 614, "src": "2207:22:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 535, "name": "address", "nodeType": "ElementaryTypeName", "src": "2207:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 538, "mutability": "mutable", "name": "_communityWallet", "nameLocation": "2247:16:5", "nodeType": "VariableDeclaration", "scope": 614, "src": "2239:24:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 537, "name": "address", "nodeType": "ElementaryTypeName", "src": "2239:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "2139:130:5"}, "returnParameters": {"id": 544, "nodeType": "ParameterList", "parameters": [], "src": "2290:0:5"}, "scope": 1146, "src": "2128:757:5", "stateMutability": "nonpayable", "virtual": false, "visibility": "public"}, {"body": {"id": 690, "nodeType": "Block", "src": "2988:633:5", "statements": [{"assignments": [623], "declarations": [{"constant": false, "id": 623, "mutability": "mutable", "name": "prices", "nameLocation": "3017:6:5", "nodeType": "VariableDeclaration", "scope": 690, "src": "2998:25:5", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$31_memory_ptr", "typeString": "uint256[31]"}, "typeName": {"baseType": {"id": 621, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "2998:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 622, "length": {"hexValue": "3331", "id": 620, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3006:2:5", "typeDescriptions": {"typeIdentifier": "t_rational_31_by_1", "typeString": "int_const 31"}, "value": "31"}, "nodeType": "ArrayTypeName", "src": "2998:11:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$31_storage_ptr", "typeString": "uint256[31]"}}, "visibility": "internal"}], "id": 659, "initialValue": {"components": [{"arguments": [{"hexValue": "302e30316538", "id": 626, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3048:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_1000000_by_1", "typeString": "int_const 1000000"}, "value": "0.01e8"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_rational_1000000_by_1", "typeString": "int_const 1000000"}], "id": 625, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "3040:7:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 624, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3040:7:5", "typeDescriptions": {}}}, "id": 627, "isConstant": false, "isLValue": false, "isPure": true, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3040:15:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"hexValue": "302e30326538", "id": 628, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3057:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_2000000_by_1", "typeString": "int_const 2000000"}, "value": "0.02e8"}, {"hexValue": "302e30336538", "id": 629, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3065:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_3000000_by_1", "typeString": "int_const 3000000"}, "value": "0.03e8"}, {"hexValue": "302e30346538", "id": 630, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3073:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_4000000_by_1", "typeString": "int_const 4000000"}, "value": "0.04e8"}, {"hexValue": "302e30356538", "id": 631, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3081:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_5000000_by_1", "typeString": "int_const 5000000"}, "value": "0.05e8"}, {"hexValue": "302e30366538", "id": 632, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3089:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_6000000_by_1", "typeString": "int_const 6000000"}, "value": "0.06e8"}, {"hexValue": "302e30376538", "id": 633, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3097:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_7000000_by_1", "typeString": "int_const 7000000"}, "value": "0.07e8"}, {"hexValue": "302e30386538", "id": 634, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3105:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_8000000_by_1", "typeString": "int_const 8000000"}, "value": "0.08e8"}, {"hexValue": "302e30396538", "id": 635, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3113:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_9000000_by_1", "typeString": "int_const 9000000"}, "value": "0.09e8"}, {"hexValue": "302e31306538", "id": 636, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3121:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_10000000_by_1", "typeString": "int_const 10000000"}, "value": "0.10e8"}, {"hexValue": "302e31316538", "id": 637, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3141:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_11000000_by_1", "typeString": "int_const 11000000"}, "value": "0.11e8"}, {"hexValue": "302e31326538", "id": 638, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3149:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_12000000_by_1", "typeString": "int_const 12000000"}, "value": "0.12e8"}, {"hexValue": "302e31336538", "id": 639, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3157:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_13000000_by_1", "typeString": "int_const 13000000"}, "value": "0.13e8"}, {"hexValue": "302e31346538", "id": 640, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3165:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_14000000_by_1", "typeString": "int_const 14000000"}, "value": "0.14e8"}, {"hexValue": "302e31356538", "id": 641, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3173:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_15000000_by_1", "typeString": "int_const 15000000"}, "value": "0.15e8"}, {"hexValue": "302e31366538", "id": 642, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3181:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_16000000_by_1", "typeString": "int_const 16000000"}, "value": "0.16e8"}, {"hexValue": "302e31376538", "id": 643, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3189:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_17000000_by_1", "typeString": "int_const 17000000"}, "value": "0.17e8"}, {"hexValue": "302e31386538", "id": 644, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3197:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_18000000_by_1", "typeString": "int_const 18000000"}, "value": "0.18e8"}, {"hexValue": "302e31396538", "id": 645, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3205:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_19000000_by_1", "typeString": "int_const 19000000"}, "value": "0.19e8"}, {"hexValue": "302e32306538", "id": 646, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3213:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_20000000_by_1", "typeString": "int_const 20000000"}, "value": "0.20e8"}, {"hexValue": "302e32356538", "id": 647, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3233:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_25000000_by_1", "typeString": "int_const 25000000"}, "value": "0.25e8"}, {"hexValue": "302e33306538", "id": 648, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3241:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_30000000_by_1", "typeString": "int_const 30000000"}, "value": "0.30e8"}, {"hexValue": "302e33356538", "id": 649, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3249:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_35000000_by_1", "typeString": "int_const 35000000"}, "value": "0.35e8"}, {"hexValue": "302e34306538", "id": 650, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3257:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_40000000_by_1", "typeString": "int_const 40000000"}, "value": "0.40e8"}, {"hexValue": "302e34356538", "id": 651, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3265:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_45000000_by_1", "typeString": "int_const 45000000"}, "value": "0.45e8"}, {"hexValue": "302e35306538", "id": 652, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3273:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_50000000_by_1", "typeString": "int_const 50000000"}, "value": "0.50e8"}, {"hexValue": "302e36306538", "id": 653, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3281:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_60000000_by_1", "typeString": "int_const 60000000"}, "value": "0.60e8"}, {"hexValue": "302e37306538", "id": 654, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3289:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_70000000_by_1", "typeString": "int_const 70000000"}, "value": "0.70e8"}, {"hexValue": "302e38306538", "id": 655, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3297:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_80000000_by_1", "typeString": "int_const 80000000"}, "value": "0.80e8"}, {"hexValue": "302e39306538", "id": 656, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3305:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_90000000_by_1", "typeString": "int_const 90000000"}, "value": "0.90e8"}, {"hexValue": "312e30306538", "id": 657, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3313:6:5", "typeDescriptions": {"typeIdentifier": "t_rational_100000000_by_1", "typeString": "int_const 100000000"}, "value": "1.00e8"}], "id": 658, "isConstant": false, "isInlineArray": true, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "TupleExpression", "src": "3026:303:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$31_memory_ptr", "typeString": "uint256[31] memory"}}, "nodeType": "VariableDeclarationStatement", "src": "2998:331:5"}, {"body": {"id": 688, "nodeType": "Block", "src": "3391:224:5", "statements": [{"expression": {"id": 686, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 670, "name": "rounds", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 494, "src": "3405:6:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_Round_$482_storage_$", "typeString": "mapping(uint256 => struct HAOXVestingV2Ultra.Round storage ref)"}}, "id": 674, "indexExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 673, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 671, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 661, "src": "3412:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"hexValue": "31", "id": 672, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3416:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "src": "3412:5:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "3405:13:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Round_$482_storage", "typeString": "struct HAOXVestingV2Ultra.Round storage ref"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"arguments": [{"baseExpression": {"id": 678, "name": "prices", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 623, "src": "3467:6:5", "typeDescriptions": {"typeIdentifier": "t_array$_t_uint256_$31_memory_ptr", "typeString": "uint256[31] memory"}}, "id": 680, "indexExpression": {"id": 679, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 661, "src": "3474:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3467:9:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 677, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "3459:7:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint128_$", "typeString": "type(uint128)"}, "typeName": {"id": 676, "name": "uint128", "nodeType": "ElementaryTypeName", "src": "3459:7:5", "typeDescriptions": {}}}, "id": 681, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3459:18:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint128", "typeString": "uint128"}}, {"hexValue": "30", "id": 682, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3513:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, {"hexValue": "66616c7365", "id": 683, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "3551:5:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}, {"hexValue": "66616c7365", "id": 684, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "3584:5:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint128", "typeString": "uint128"}, {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, {"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 675, "name": "Round", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 482, "src": "3421:5:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_Round_$482_storage_ptr_$", "typeString": "type(struct HAOXVestingV2Ultra.Round storage pointer)"}}, "id": 685, "isConstant": false, "isLValue": false, "isPure": false, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": ["3445:12:5", "3495:16:5", "3532:17:5", "3574:8:5"], "names": ["triggerPrice", "priceReachedTime", "priceConditionMet", "unlocked"], "nodeType": "FunctionCall", "src": "3421:183:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_Round_$482_memory_ptr", "typeString": "struct HAOXVestingV2Ultra.Round memory"}}, "src": "3405:199:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Round_$482_storage", "typeString": "struct HAOXVestingV2Ultra.Round storage ref"}}, "id": 687, "nodeType": "ExpressionStatement", "src": "3405:199:5"}]}, "condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 666, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 664, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 661, "src": "3368:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 665, "name": "TOTAL_ROUNDS", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 448, "src": "3372:12:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3368:16:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 689, "initializationExpression": {"assignments": [661], "declarations": [{"constant": false, "id": 661, "mutability": "mutable", "name": "i", "nameLocation": "3361:1:5", "nodeType": "VariableDeclaration", "scope": 689, "src": "3353:9:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 660, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3353:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 663, "initialValue": {"hexValue": "30", "id": 662, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "3365:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "nodeType": "VariableDeclarationStatement", "src": "3353:13:5"}, "loopExpression": {"expression": {"id": 668, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "3386:3:5", "subExpression": {"id": 667, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 661, "src": "3386:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 669, "nodeType": "ExpressionStatement", "src": "3386:3:5"}, "nodeType": "ForStatement", "src": "3348:267:5"}]}, "documentation": {"id": 615, "nodeType": "StructuredDocumentation", "src": "2895:50:5", "text": " @dev 初始化31轮价格阶梯"}, "id": 691, "implemented": true, "kind": "function", "modifiers": [], "name": "_initializeRounds", "nameLocation": "2959:17:5", "nodeType": "FunctionDefinition", "parameters": {"id": 616, "nodeType": "ParameterList", "parameters": [], "src": "2976:2:5"}, "returnParameters": {"id": 617, "nodeType": "ParameterList", "parameters": [], "src": "2988:0:5"}, "scope": 1146, "src": "2950:671:5", "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"body": {"id": 792, "nodeType": "Block", "src": "3750:928:5", "statements": [{"assignments": [698], "declarations": [{"constant": false, "id": 698, "mutability": "mutable", "name": "roundNumber", "nameLocation": "3768:11:5", "nodeType": "VariableDeclaration", "scope": 792, "src": "3760:19:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 697, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3760:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 700, "initialValue": {"id": 699, "name": "currentRound", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 473, "src": "3782:12:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "3760:34:5"}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 703, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 701, "name": "roundNumber", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 698, "src": "3808:11:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"id": 702, "name": "TOTAL_ROUNDS", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 448, "src": "3822:12:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "3808:26:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 705, "nodeType": "IfStatement", "src": "3804:39:5", "trueBody": {"functionReturnParameters": 696, "id": 704, "nodeType": "Return", "src": "3836:7:5"}}, {"assignments": [708], "declarations": [{"constant": false, "id": 708, "mutability": "mutable", "name": "round", "nameLocation": "3875:5:5", "nodeType": "VariableDeclaration", "scope": 792, "src": "3861:19:5", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_Round_$482_storage_ptr", "typeString": "struct HAOXVestingV2Ultra.Round"}, "typeName": {"id": 707, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 706, "name": "Round", "nameLocations": ["3861:5:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 482, "src": "3861:5:5"}, "referencedDeclaration": 482, "src": "3861:5:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Round_$482_storage_ptr", "typeString": "struct HAOXVestingV2Ultra.Round"}}, "visibility": "internal"}], "id": 712, "initialValue": {"baseExpression": {"id": 709, "name": "rounds", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 494, "src": "3883:6:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_Round_$482_storage_$", "typeString": "mapping(uint256 => struct HAOXVestingV2Ultra.Round storage ref)"}}, "id": 711, "indexExpression": {"id": 710, "name": "roundNumber", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 698, "src": "3890:11:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "3883:19:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Round_$482_storage", "typeString": "struct HAOXVestingV2Ultra.Round storage ref"}}, "nodeType": "VariableDeclarationStatement", "src": "3861:41:5"}, {"condition": {"expression": {"id": 713, "name": "round", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 708, "src": "3916:5:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Round_$482_storage_ptr", "typeString": "struct HAOXVestingV2Ultra.Round storage pointer"}}, "id": 714, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "3922:8:5", "memberName": "unlocked", "nodeType": "MemberAccess", "referencedDeclaration": 481, "src": "3916:14:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 716, "nodeType": "IfStatement", "src": "3912:27:5", "trueBody": {"functionReturnParameters": 696, "id": 715, "nodeType": "Return", "src": "3932:7:5"}}, {"assignments": [718], "declarations": [{"constant": false, "id": 718, "mutability": "mutable", "name": "currentPrice", "nameLocation": "3965:12:5", "nodeType": "VariableDeclaration", "scope": 792, "src": "3957:20:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 717, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "3957:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 721, "initialValue": {"arguments": [], "expression": {"argumentTypes": [], "id": 719, "name": "_getCurrentPrice", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 867, "src": "3980:16:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_uint256_$", "typeString": "function () view returns (uint256)"}}, "id": 720, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "3980:18:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "VariableDeclarationStatement", "src": "3957:41:5"}, {"assignments": [723], "declarations": [{"constant": false, "id": 723, "mutability": "mutable", "name": "conditionMet", "nameLocation": "4013:12:5", "nodeType": "VariableDeclaration", "scope": 792, "src": "4008:17:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 722, "name": "bool", "nodeType": "ElementaryTypeName", "src": "4008:4:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "id": 728, "initialValue": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 727, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 724, "name": "currentPrice", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 718, "src": "4028:12:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">=", "rightExpression": {"expression": {"id": 725, "name": "round", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 708, "src": "4044:5:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Round_$482_storage_ptr", "typeString": "struct HAOXVestingV2Ultra.Round storage pointer"}}, "id": 726, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "4050:12:5", "memberName": "triggerPrice", "nodeType": "MemberAccess", "referencedDeclaration": 475, "src": "4044:18:5", "typeDescriptions": {"typeIdentifier": "t_uint128", "typeString": "uint128"}}, "src": "4028:34:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "VariableDeclarationStatement", "src": "4008:54:5"}, {"condition": {"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 733, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 729, "name": "conditionMet", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 723, "src": "4085:12:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"id": 732, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "4101:24:5", "subExpression": {"expression": {"id": 730, "name": "round", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 708, "src": "4102:5:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Round_$482_storage_ptr", "typeString": "struct HAOXVestingV2Ultra.Round storage pointer"}}, "id": 731, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "4108:17:5", "memberName": "priceConditionMet", "nodeType": "MemberAccess", "referencedDeclaration": 479, "src": "4102:23:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "4085:40:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"condition": {"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 760, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 757, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "4317:13:5", "subExpression": {"id": 756, "name": "conditionMet", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 723, "src": "4318:12:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"expression": {"id": 758, "name": "round", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 708, "src": "4334:5:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Round_$482_storage_ptr", "typeString": "struct HAOXVestingV2Ultra.Round storage pointer"}}, "id": 759, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "4340:17:5", "memberName": "priceConditionMet", "nodeType": "MemberAccess", "referencedDeclaration": 479, "src": "4334:23:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "4317:40:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 774, "nodeType": "IfStatement", "src": "4313:142:5", "trueBody": {"id": 773, "nodeType": "Block", "src": "4359:96:5", "statements": [{"expression": {"id": 765, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 761, "name": "round", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 708, "src": "4373:5:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Round_$482_storage_ptr", "typeString": "struct HAOXVestingV2Ultra.Round storage pointer"}}, "id": 763, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "4379:17:5", "memberName": "priceConditionMet", "nodeType": "MemberAccess", "referencedDeclaration": 479, "src": "4373:23:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "66616c7365", "id": 764, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "4399:5:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}, "src": "4373:31:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 766, "nodeType": "ExpressionStatement", "src": "4373:31:5"}, {"expression": {"id": 771, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 767, "name": "round", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 708, "src": "4418:5:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Round_$482_storage_ptr", "typeString": "struct HAOXVestingV2Ultra.Round storage pointer"}}, "id": 769, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "4424:16:5", "memberName": "priceReachedTime", "nodeType": "MemberAccess", "referencedDeclaration": 477, "src": "4418:22:5", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "30", "id": 770, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "4443:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "4418:26:5", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "id": 772, "nodeType": "ExpressionStatement", "src": "4418:26:5"}]}}, "id": 775, "nodeType": "IfStatement", "src": "4081:374:5", "trueBody": {"id": 755, "nodeType": "Block", "src": "4127:180:5", "statements": [{"expression": {"id": 738, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 734, "name": "round", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 708, "src": "4141:5:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Round_$482_storage_ptr", "typeString": "struct HAOXVestingV2Ultra.Round storage pointer"}}, "id": 736, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "4147:17:5", "memberName": "priceConditionMet", "nodeType": "MemberAccess", "referencedDeclaration": 479, "src": "4141:23:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "74727565", "id": 737, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "4167:4:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "src": "4141:30:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 739, "nodeType": "ExpressionStatement", "src": "4141:30:5"}, {"expression": {"id": 748, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 740, "name": "round", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 708, "src": "4185:5:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Round_$482_storage_ptr", "typeString": "struct HAOXVestingV2Ultra.Round storage pointer"}}, "id": 742, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "4191:16:5", "memberName": "priceReachedTime", "nodeType": "MemberAccess", "referencedDeclaration": 477, "src": "4185:22:5", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"expression": {"id": 745, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "4217:5:5", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 746, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4223:9:5", "memberName": "timestamp", "nodeType": "MemberAccess", "src": "4217:15:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 744, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "4210:6:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint64_$", "typeString": "type(uint64)"}, "typeName": {"id": 743, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "4210:6:5", "typeDescriptions": {}}}, "id": 747, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4210:23:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "src": "4185:48:5", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "id": 749, "nodeType": "ExpressionStatement", "src": "4185:48:5"}, {"eventCall": {"arguments": [{"id": 751, "name": "roundNumber", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 698, "src": "4270:11:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 752, "name": "currentPrice", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 718, "src": "4283:12:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 750, "name": "PriceConditionMet", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 512, "src": "4252:17:5", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_uint256_$_t_uint256_$returns$__$", "typeString": "function (uint256,uint256)"}}, "id": 753, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4252:44:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 754, "nodeType": "EmitStatement", "src": "4247:49:5"}]}}, {"condition": {"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 785, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 776, "name": "round", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 708, "src": "4513:5:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Round_$482_storage_ptr", "typeString": "struct HAOXVestingV2Ultra.Round storage pointer"}}, "id": 777, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "4519:17:5", "memberName": "priceConditionMet", "nodeType": "MemberAccess", "referencedDeclaration": 479, "src": "4513:23:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 784, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 778, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "4553:5:5", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 779, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4559:9:5", "memberName": "timestamp", "nodeType": "MemberAccess", "src": "4553:15:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">=", "rightExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 783, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 780, "name": "round", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 708, "src": "4572:5:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Round_$482_storage_ptr", "typeString": "struct HAOXVestingV2Ultra.Round storage pointer"}}, "id": 781, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "4578:16:5", "memberName": "priceReachedTime", "nodeType": "MemberAccess", "referencedDeclaration": 477, "src": "4572:22:5", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"id": 782, "name": "PRICE_MAINTAIN_DURATION", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 451, "src": "4597:23:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4572:48:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4553:67:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "4513:107:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 791, "nodeType": "IfStatement", "src": "4509:163:5", "trueBody": {"id": 790, "nodeType": "Block", "src": "4622:50:5", "statements": [{"expression": {"arguments": [{"id": 787, "name": "roundNumber", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 698, "src": "4649:11:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 786, "name": "_unlockRound", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 830, "src": "4636:12:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$_t_uint256_$returns$__$", "typeString": "function (uint256)"}}, "id": 788, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4636:25:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 789, "nodeType": "ExpressionStatement", "src": "4636:25:5"}]}}]}, "documentation": {"id": 692, "nodeType": "StructuredDocumentation", "src": "3631:60:5", "text": " @dev 检查价格条件（超精简版）"}, "functionSelector": "687404b2", "id": 793, "implemented": true, "kind": "function", "modifiers": [{"id": 695, "kind": "modifierInvocation", "modifierName": {"id": 694, "name": "whenNotPaused", "nameLocations": ["3736:13:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 288, "src": "3736:13:5"}, "nodeType": "ModifierInvocation", "src": "3736:13:5"}], "name": "checkPriceCondition", "nameLocation": "3705:19:5", "nodeType": "FunctionDefinition", "parameters": {"id": 693, "nodeType": "ParameterList", "parameters": [], "src": "3724:2:5"}, "returnParameters": {"id": 696, "nodeType": "ParameterList", "parameters": [], "src": "3750:0:5"}, "scope": 1146, "src": "3696:982:5", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"body": {"id": 829, "nodeType": "Block", "src": "4781:261:5", "statements": [{"assignments": [801], "declarations": [{"constant": false, "id": 801, "mutability": "mutable", "name": "round", "nameLocation": "4805:5:5", "nodeType": "VariableDeclaration", "scope": 829, "src": "4791:19:5", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_Round_$482_storage_ptr", "typeString": "struct HAOXVestingV2Ultra.Round"}, "typeName": {"id": 800, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 799, "name": "Round", "nameLocations": ["4791:5:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 482, "src": "4791:5:5"}, "referencedDeclaration": 482, "src": "4791:5:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Round_$482_storage_ptr", "typeString": "struct HAOXVestingV2Ultra.Round"}}, "visibility": "internal"}], "id": 805, "initialValue": {"baseExpression": {"id": 802, "name": "rounds", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 494, "src": "4813:6:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_Round_$482_storage_$", "typeString": "mapping(uint256 => struct HAOXVestingV2Ultra.Round storage ref)"}}, "id": 804, "indexExpression": {"id": 803, "name": "roundNumber", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 796, "src": "4820:11:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "4813:19:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Round_$482_storage", "typeString": "struct HAOXVestingV2Ultra.Round storage ref"}}, "nodeType": "VariableDeclarationStatement", "src": "4791:41:5"}, {"expression": {"id": 810, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 806, "name": "round", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 801, "src": "4842:5:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Round_$482_storage_ptr", "typeString": "struct HAOXVestingV2Ultra.Round storage pointer"}}, "id": 808, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "4848:8:5", "memberName": "unlocked", "nodeType": "MemberAccess", "referencedDeclaration": 481, "src": "4842:14:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "74727565", "id": 809, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "4859:4:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "src": "4842:21:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 811, "nodeType": "ExpressionStatement", "src": "4842:21:5"}, {"eventCall": {"arguments": [{"id": 813, "name": "roundNumber", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 796, "src": "4901:11:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"expression": {"id": 814, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "4914:5:5", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 815, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "4920:9:5", "memberName": "timestamp", "nodeType": "MemberAccess", "src": "4914:15:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 812, "name": "RoundUnlocked", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 518, "src": "4887:13:5", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_uint256_$_t_uint256_$returns$__$", "typeString": "function (uint256,uint256)"}}, "id": 816, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "4887:43:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 817, "nodeType": "EmitStatement", "src": "4882:48:5"}, {"condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 820, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 818, "name": "roundNumber", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 796, "src": "4953:11:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<", "rightExpression": {"id": 819, "name": "TOTAL_ROUNDS", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 448, "src": "4967:12:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4953:26:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 828, "nodeType": "IfStatement", "src": "4949:87:5", "trueBody": {"id": 827, "nodeType": "Block", "src": "4981:55:5", "statements": [{"expression": {"id": 825, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 821, "name": "currentRound", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 473, "src": "4995:12:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 824, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 822, "name": "roundNumber", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 796, "src": "5010:11:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"hexValue": "31", "id": 823, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "5024:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "src": "5010:15:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "4995:30:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 826, "nodeType": "ExpressionStatement", "src": "4995:30:5"}]}}]}, "documentation": {"id": 794, "nodeType": "StructuredDocumentation", "src": "4688:36:5", "text": " @dev 解锁轮次"}, "id": 830, "implemented": true, "kind": "function", "modifiers": [], "name": "_unlockRound", "nameLocation": "4738:12:5", "nodeType": "FunctionDefinition", "parameters": {"id": 797, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 796, "mutability": "mutable", "name": "roundNumber", "nameLocation": "4759:11:5", "nodeType": "VariableDeclaration", "scope": 830, "src": "4751:19:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 795, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "4751:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "4750:21:5"}, "returnParameters": {"id": 798, "nodeType": "ParameterList", "parameters": [], "src": "4781:0:5"}, "scope": 1146, "src": "4729:313:5", "stateMutability": "nonpayable", "virtual": false, "visibility": "internal"}, {"body": {"id": 866, "nodeType": "Block", "src": "5159:261:5", "statements": [{"assignments": [837, 839], "declarations": [{"constant": false, "id": 837, "mutability": "mutable", "name": "success", "nameLocation": "5175:7:5", "nodeType": "VariableDeclaration", "scope": 866, "src": "5170:12:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 836, "name": "bool", "nodeType": "ElementaryTypeName", "src": "5170:4:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, {"constant": false, "id": 839, "mutability": "mutable", "name": "data", "nameLocation": "5197:4:5", "nodeType": "VariableDeclaration", "scope": 866, "src": "5184:17:5", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes"}, "typeName": {"id": 838, "name": "bytes", "nodeType": "ElementaryTypeName", "src": "5184:5:5", "typeDescriptions": {"typeIdentifier": "t_bytes_storage_ptr", "typeString": "bytes"}}, "visibility": "internal"}], "id": 847, "initialValue": {"arguments": [{"arguments": [{"hexValue": "6765744c617465737450726963652829", "id": 844, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "5265:18:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_8e15f47336b2c01075cbe7ae5c7a343e8fd355444421a579d8279b8ce7a6d809", "typeString": "literal_string \"getLatestPrice()\""}, "value": "getLatestPrice()"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_stringliteral_8e15f47336b2c01075cbe7ae5c7a343e8fd355444421a579d8279b8ce7a6d809", "typeString": "literal_string \"getLatestPrice()\""}], "expression": {"id": 842, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "5241:3:5", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 843, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "5245:19:5", "memberName": "encodeWithSignature", "nodeType": "MemberAccess", "src": "5241:23:5", "typeDescriptions": {"typeIdentifier": "t_function_abiencodewithsignature_pure$_t_string_memory_ptr_$returns$_t_bytes_memory_ptr_$", "typeString": "function (string memory) pure returns (bytes memory)"}}, "id": 845, "isConstant": false, "isLValue": false, "isPure": true, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5241:43:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "expression": {"id": 840, "name": "priceOracle", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 466, "src": "5205:11:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "id": 841, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5217:10:5", "memberName": "staticcall", "nodeType": "MemberAccess", "src": "5205:22:5", "typeDescriptions": {"typeIdentifier": "t_function_barestaticcall_view$_t_bytes_memory_ptr_$returns$_t_bool_$_t_bytes_memory_ptr_$", "typeString": "function (bytes memory) view returns (bool,bytes memory)"}}, "id": 846, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5205:89:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$_t_bool_$_t_bytes_memory_ptr_$", "typeString": "tuple(bool,bytes memory)"}}, "nodeType": "VariableDeclarationStatement", "src": "5169:125:5"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_bool", "typeString": "bool"}, "id": 854, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 849, "name": "success", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 837, "src": "5312:7:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "BinaryOperation", "operator": "&&", "rightExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 853, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 850, "name": "data", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 839, "src": "5323:4:5", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, "id": 851, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5328:6:5", "memberName": "length", "nodeType": "MemberAccess", "src": "5323:11:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">=", "rightExpression": {"hexValue": "3332", "id": 852, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "5338:2:5", "typeDescriptions": {"typeIdentifier": "t_rational_32_by_1", "typeString": "int_const 32"}, "value": "32"}, "src": "5323:17:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "5312:28:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "5072696365206f7261636c652063616c6c206661696c6564", "id": 855, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "5342:26:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_2801072bbcd609be8268697fa6475d2d921453e7b73807838b5fe38ad7c3348c", "typeString": "literal_string \"Price oracle call failed\""}, "value": "Price oracle call failed"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_2801072bbcd609be8268697fa6475d2d921453e7b73807838b5fe38ad7c3348c", "typeString": "literal_string \"Price oracle call failed\""}], "id": 848, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "5304:7:5", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 856, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5304:65:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 857, "nodeType": "ExpressionStatement", "src": "5304:65:5"}, {"expression": {"arguments": [{"id": 860, "name": "data", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 839, "src": "5397:4:5", "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}, {"components": [{"id": 862, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "5404:7:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}, "typeName": {"id": 861, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "5404:7:5", "typeDescriptions": {}}}], "id": 863, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "TupleExpression", "src": "5403:9:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}, {"typeIdentifier": "t_type$_t_uint256_$", "typeString": "type(uint256)"}], "expression": {"id": 858, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "5386:3:5", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 859, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "5390:6:5", "memberName": "decode", "nodeType": "MemberAccess", "src": "5386:10:5", "typeDescriptions": {"typeIdentifier": "t_function_abidecode_pure$__$returns$__$", "typeString": "function () pure"}}, "id": 864, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5386:27:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 835, "id": 865, "nodeType": "Return", "src": "5379:34:5"}]}, "documentation": {"id": 831, "nodeType": "StructuredDocumentation", "src": "5052:42:5", "text": " @dev 获取当前价格"}, "id": 867, "implemented": true, "kind": "function", "modifiers": [], "name": "_getCurrentPrice", "nameLocation": "5108:16:5", "nodeType": "FunctionDefinition", "parameters": {"id": 832, "nodeType": "ParameterList", "parameters": [], "src": "5124:2:5"}, "returnParameters": {"id": 835, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 834, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 867, "src": "5150:7:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 833, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "5150:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "5149:9:5"}, "scope": 1146, "src": "5099:321:5", "stateMutability": "view", "virtual": false, "visibility": "internal"}, {"body": {"id": 924, "nodeType": "Block", "src": "5601:503:5", "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 882, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 880, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 872, "src": "5619:6:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"id": 881, "name": "MAX_EMERGENCY_AMOUNT", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 461, "src": "5629:20:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "5619:30:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "416d6f756e742065786365656473206d6178696d756d", "id": 883, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "5651:24:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_064faf8bb7dc0a8beefe8ba8264132c0722c18281b97f5173e5857910c249dce", "typeString": "literal_string \"Amount exceeds maximum\""}, "value": "Amount exceeds maximum"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_064faf8bb7dc0a8beefe8ba8264132c0722c18281b97f5173e5857910c249dce", "typeString": "literal_string \"Amount exceeds maximum\""}], "id": 879, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "5611:7:5", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 884, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5611:65:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 885, "nodeType": "ExpressionStatement", "src": "5611:65:5"}, {"assignments": [887], "declarations": [{"constant": false, "id": 887, "mutability": "mutable", "name": "requestId", "nameLocation": "5703:9:5", "nodeType": "VariableDeclaration", "scope": 924, "src": "5695:17:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 886, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "5695:7:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "id": 899, "initialValue": {"arguments": [{"arguments": [{"id": 891, "name": "token", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 870, "src": "5755:5:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"id": 892, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 872, "src": "5762:6:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"expression": {"id": 893, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "5770:5:5", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 894, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5776:9:5", "memberName": "timestamp", "nodeType": "MemberAccess", "src": "5770:15:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"expression": {"id": 895, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "5787:3:5", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 896, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5791:6:5", "memberName": "sender", "nodeType": "MemberAccess", "src": "5787:10:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}, {"typeIdentifier": "t_address", "typeString": "address"}], "expression": {"id": 889, "name": "abi", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -1, "src": "5725:3:5", "typeDescriptions": {"typeIdentifier": "t_magic_abi", "typeString": "abi"}}, "id": 890, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "memberLocation": "5729:12:5", "memberName": "encodePacked", "nodeType": "MemberAccess", "src": "5725:16:5", "typeDescriptions": {"typeIdentifier": "t_function_abiencodepacked_pure$__$returns$_t_bytes_memory_ptr_$", "typeString": "function () pure returns (bytes memory)"}}, "id": 897, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5725:82:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes_memory_ptr", "typeString": "bytes memory"}], "id": 888, "name": "keccak256", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -8, "src": "5715:9:5", "typeDescriptions": {"typeIdentifier": "t_function_keccak256_pure$_t_bytes_memory_ptr_$returns$_t_bytes32_$", "typeString": "function (bytes memory) pure returns (bytes32)"}}, "id": 898, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5715:93:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "nodeType": "VariableDeclarationStatement", "src": "5695:113:5"}, {"expression": {"id": 915, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 900, "name": "emergencyRequests", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 499, "src": "5827:17:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_struct$_EmergencyRequest_$489_storage_$", "typeString": "mapping(bytes32 => struct HAOXVestingV2Ultra.EmergencyRequest storage ref)"}}, "id": 902, "indexExpression": {"id": 901, "name": "requestId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 887, "src": "5845:9:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "5827:28:5", "typeDescriptions": {"typeIdentifier": "t_struct$_EmergencyRequest_$489_storage", "typeString": "struct HAOXVestingV2Ultra.EmergencyRequest storage ref"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"arguments": [{"arguments": [{"id": 906, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 872, "src": "5905:6:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 905, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "5897:7:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint128_$", "typeString": "type(uint128)"}, "typeName": {"id": 904, "name": "uint128", "nodeType": "ElementaryTypeName", "src": "5897:7:5", "typeDescriptions": {}}}, "id": 907, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5897:15:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint128", "typeString": "uint128"}}, {"arguments": [{"expression": {"id": 910, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "5946:5:5", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 911, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "5952:9:5", "memberName": "timestamp", "nodeType": "MemberAccess", "src": "5946:15:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 909, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "5939:6:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_uint64_$", "typeString": "type(uint64)"}, "typeName": {"id": 908, "name": "uint64", "nodeType": "ElementaryTypeName", "src": "5939:6:5", "typeDescriptions": {}}}, "id": 912, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "5939:23:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, {"hexValue": "66616c7365", "id": 913, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "5986:5:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "false"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_uint128", "typeString": "uint128"}, {"typeIdentifier": "t_uint64", "typeString": "uint64"}, {"typeIdentifier": "t_bool", "typeString": "bool"}], "id": 903, "name": "EmergencyRequest", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 489, "src": "5858:16:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_struct$_EmergencyRequest_$489_storage_ptr_$", "typeString": "type(struct HAOXVestingV2Ultra.EmergencyRequest storage pointer)"}}, "id": 914, "isConstant": false, "isLValue": false, "isPure": false, "kind": "structConstructorCall", "lValueRequested": false, "nameLocations": ["5889:6:5", "5926:11:5", "5976:8:5"], "names": ["amount", "requestTime", "executed"], "nodeType": "FunctionCall", "src": "5858:144:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_struct$_EmergencyRequest_$489_memory_ptr", "typeString": "struct HAOXVestingV2Ultra.EmergencyRequest memory"}}, "src": "5827:175:5", "typeDescriptions": {"typeIdentifier": "t_struct$_EmergencyRequest_$489_storage", "typeString": "struct HAOXVestingV2Ultra.EmergencyRequest storage ref"}}, "id": 916, "nodeType": "ExpressionStatement", "src": "5827:175:5"}, {"eventCall": {"arguments": [{"id": 918, "name": "requestId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 887, "src": "6053:9:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"id": 919, "name": "amount", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 872, "src": "6064:6:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_uint256", "typeString": "uint256"}], "id": 917, "name": "EmergencyWithdrawRequested", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 524, "src": "6026:26:5", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_bytes32_$_t_uint256_$returns$__$", "typeString": "function (bytes32,uint256)"}}, "id": 920, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6026:45:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 921, "nodeType": "EmitStatement", "src": "6021:50:5"}, {"expression": {"id": 922, "name": "requestId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 887, "src": "6088:9:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "functionReturnParameters": 878, "id": 923, "nodeType": "Return", "src": "6081:16:5"}]}, "documentation": {"id": 868, "nodeType": "StructuredDocumentation", "src": "5430:42:5", "text": " @dev 紧急提取请求"}, "functionSelector": "7c7f4ce5", "id": 925, "implemented": true, "kind": "function", "modifiers": [{"id": 875, "kind": "modifierInvocation", "modifierName": {"id": 874, "name": "only<PERSON><PERSON>er", "nameLocations": ["5573:9:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 58, "src": "5573:9:5"}, "nodeType": "ModifierInvocation", "src": "5573:9:5"}], "name": "requestEmergencyWithdraw", "nameLocation": "5486:24:5", "nodeType": "FunctionDefinition", "parameters": {"id": 873, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 870, "mutability": "mutable", "name": "token", "nameLocation": "5528:5:5", "nodeType": "VariableDeclaration", "scope": 925, "src": "5520:13:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 869, "name": "address", "nodeType": "ElementaryTypeName", "src": "5520:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 872, "mutability": "mutable", "name": "amount", "nameLocation": "5551:6:5", "nodeType": "VariableDeclaration", "scope": 925, "src": "5543:14:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 871, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "5543:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "5510:53:5"}, "returnParameters": {"id": 878, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 877, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 925, "src": "5592:7:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 876, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "5592:7:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}], "src": "5591:9:5"}, "scope": 1146, "src": "5477:627:5", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"body": {"id": 1006, "nodeType": "Block", "src": "6273:678:5", "statements": [{"expression": {"arguments": [{"baseExpression": {"id": 936, "name": "emergencySigners", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 503, "src": "6291:16:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 939, "indexExpression": {"expression": {"id": 937, "name": "msg", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -15, "src": "6308:3:5", "typeDescriptions": {"typeIdentifier": "t_magic_message", "typeString": "msg"}}, "id": 938, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "6312:6:5", "memberName": "sender", "nodeType": "MemberAccess", "src": "6308:10:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "6291:28:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "4e6f7420617574686f72697a6564207369676e6572", "id": 940, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "6321:23:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_f6ad76aa582ae39da26c5d2a2dbb292ff394509af45179299935fd0094256a65", "typeString": "literal_string \"Not authorized signer\""}, "value": "Not authorized signer"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_f6ad76aa582ae39da26c5d2a2dbb292ff394509af45179299935fd0094256a65", "typeString": "literal_string \"Not authorized signer\""}], "id": 935, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "6283:7:5", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 941, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6283:62:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 942, "nodeType": "ExpressionStatement", "src": "6283:62:5"}, {"assignments": [945], "declarations": [{"constant": false, "id": 945, "mutability": "mutable", "name": "request", "nameLocation": "6389:7:5", "nodeType": "VariableDeclaration", "scope": 1006, "src": "6364:32:5", "stateVariable": false, "storageLocation": "storage", "typeDescriptions": {"typeIdentifier": "t_struct$_EmergencyRequest_$489_storage_ptr", "typeString": "struct HAOXVestingV2Ultra.EmergencyRequest"}, "typeName": {"id": 944, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 943, "name": "EmergencyRequest", "nameLocations": ["6364:16:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 489, "src": "6364:16:5"}, "referencedDeclaration": 489, "src": "6364:16:5", "typeDescriptions": {"typeIdentifier": "t_struct$_EmergencyRequest_$489_storage_ptr", "typeString": "struct HAOXVestingV2Ultra.EmergencyRequest"}}, "visibility": "internal"}], "id": 949, "initialValue": {"baseExpression": {"id": 946, "name": "emergencyRequests", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 499, "src": "6399:17:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_bytes32_$_t_struct$_EmergencyRequest_$489_storage_$", "typeString": "mapping(bytes32 => struct HAOXVestingV2Ultra.EmergencyRequest storage ref)"}}, "id": 948, "indexExpression": {"id": 947, "name": "requestId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 928, "src": "6417:9:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "6399:28:5", "typeDescriptions": {"typeIdentifier": "t_struct$_EmergencyRequest_$489_storage", "typeString": "struct HAOXVestingV2Ultra.EmergencyRequest storage ref"}}, "nodeType": "VariableDeclarationStatement", "src": "6364:63:5"}, {"expression": {"arguments": [{"id": 953, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "!", "prefix": true, "src": "6445:17:5", "subExpression": {"expression": {"id": 951, "name": "request", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 945, "src": "6446:7:5", "typeDescriptions": {"typeIdentifier": "t_struct$_EmergencyRequest_$489_storage_ptr", "typeString": "struct HAOXVestingV2Ultra.EmergencyRequest storage pointer"}}, "id": 952, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "6454:8:5", "memberName": "executed", "nodeType": "MemberAccess", "referencedDeclaration": 488, "src": "6446:16:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "416c7265616479206578656375746564", "id": 954, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "6464:18:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_58f47ea4e737df2d9cc4764db26c111751884cf0a1856aca9f4c66cadc811e1b", "typeString": "literal_string \"Already executed\""}, "value": "Already executed"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_58f47ea4e737df2d9cc4764db26c111751884cf0a1856aca9f4c66cadc811e1b", "typeString": "literal_string \"Already executed\""}], "id": 950, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "6437:7:5", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 955, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6437:46:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 956, "nodeType": "ExpressionStatement", "src": "6437:46:5"}, {"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 964, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 958, "name": "block", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": -4, "src": "6514:5:5", "typeDescriptions": {"typeIdentifier": "t_magic_block", "typeString": "block"}}, "id": 959, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "6520:9:5", "memberName": "timestamp", "nodeType": "MemberAccess", "src": "6514:15:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">=", "rightExpression": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 963, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"expression": {"id": 960, "name": "request", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 945, "src": "6533:7:5", "typeDescriptions": {"typeIdentifier": "t_struct$_EmergencyRequest_$489_storage_ptr", "typeString": "struct HAOXVestingV2Ultra.EmergencyRequest storage pointer"}}, "id": 961, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "6541:11:5", "memberName": "requestTime", "nodeType": "MemberAccess", "referencedDeclaration": 486, "src": "6533:19:5", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}, "nodeType": "BinaryOperation", "operator": "+", "rightExpression": {"id": 962, "name": "EMERGENCY_DELAY", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 454, "src": "6555:15:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "6533:37:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "6514:56:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "54696d65206c6f636b206e6f742065787069726564", "id": 965, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "6584:23:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_8cd4916fb526a1cff3aced3768d96b085812a97a0170ac4a508ffcd607dc038f", "typeString": "literal_string \"Time lock not expired\""}, "value": "Time lock not expired"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_8cd4916fb526a1cff3aced3768d96b085812a97a0170ac4a508ffcd607dc038f", "typeString": "literal_string \"Time lock not expired\""}], "id": 957, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "6493:7:5", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 966, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6493:124:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 967, "nodeType": "ExpressionStatement", "src": "6493:124:5"}, {"expression": {"id": 972, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"expression": {"id": 968, "name": "request", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 945, "src": "6636:7:5", "typeDescriptions": {"typeIdentifier": "t_struct$_EmergencyRequest_$489_storage_ptr", "typeString": "struct HAOXVestingV2Ultra.EmergencyRequest storage pointer"}}, "id": 970, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "memberLocation": "6644:8:5", "memberName": "executed", "nodeType": "MemberAccess", "referencedDeclaration": 488, "src": "6636:16:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"hexValue": "74727565", "id": 971, "isConstant": false, "isLValue": false, "isPure": true, "kind": "bool", "lValueRequested": false, "nodeType": "Literal", "src": "6655:4:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "value": "true"}, "src": "6636:23:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 973, "nodeType": "ExpressionStatement", "src": "6636:23:5"}, {"condition": {"commonType": {"typeIdentifier": "t_address", "typeString": "address"}, "id": 979, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 974, "name": "token", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 930, "src": "6682:5:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "nodeType": "BinaryOperation", "operator": "==", "rightExpression": {"arguments": [{"id": 977, "name": "haoxToken", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 464, "src": "6699:9:5", "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$225", "typeString": "contract IERC20"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_contract$_IERC20_$225", "typeString": "contract IERC20"}], "id": 976, "isConstant": false, "isLValue": false, "isPure": true, "lValueRequested": false, "nodeType": "ElementaryTypeNameExpression", "src": "6691:7:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_address_$", "typeString": "type(address)"}, "typeName": {"id": 975, "name": "address", "nodeType": "ElementaryTypeName", "src": "6691:7:5", "typeDescriptions": {}}}, "id": 978, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6691:18:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "src": "6682:27:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "falseBody": {"id": 998, "nodeType": "Block", "src": "6791:78:5", "statements": [{"expression": {"arguments": [{"id": 993, "name": "projectWallet", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 468, "src": "6828:13:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"expression": {"id": 994, "name": "request", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 945, "src": "6843:7:5", "typeDescriptions": {"typeIdentifier": "t_struct$_EmergencyRequest_$489_storage_ptr", "typeString": "struct HAOXVestingV2Ultra.EmergencyRequest storage pointer"}}, "id": 995, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "6851:6:5", "memberName": "amount", "nodeType": "MemberAccess", "referencedDeclaration": 484, "src": "6843:14:5", "typeDescriptions": {"typeIdentifier": "t_uint128", "typeString": "uint128"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint128", "typeString": "uint128"}], "expression": {"arguments": [{"id": 990, "name": "token", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 930, "src": "6812:5:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}], "id": 989, "name": "IERC20", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 225, "src": "6805:6:5", "typeDescriptions": {"typeIdentifier": "t_type$_t_contract$_IERC20_$225_$", "typeString": "type(contract IERC20)"}}, "id": 991, "isConstant": false, "isLValue": false, "isPure": false, "kind": "typeConversion", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6805:13:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$225", "typeString": "contract IERC20"}}, "id": 992, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "6819:8:5", "memberName": "transfer", "nodeType": "MemberAccess", "referencedDeclaration": 192, "src": "6805:22:5", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_address_$_t_uint256_$returns$_t_bool_$", "typeString": "function (address,uint256) external returns (bool)"}}, "id": 996, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6805:53:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 997, "nodeType": "ExpressionStatement", "src": "6805:53:5"}]}, "id": 999, "nodeType": "IfStatement", "src": "6678:191:5", "trueBody": {"id": 988, "nodeType": "Block", "src": "6711:74:5", "statements": [{"expression": {"arguments": [{"id": 983, "name": "projectWallet", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 468, "src": "6744:13:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, {"expression": {"id": 984, "name": "request", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 945, "src": "6759:7:5", "typeDescriptions": {"typeIdentifier": "t_struct$_EmergencyRequest_$489_storage_ptr", "typeString": "struct HAOXVestingV2Ultra.EmergencyRequest storage pointer"}}, "id": 985, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "6767:6:5", "memberName": "amount", "nodeType": "MemberAccess", "referencedDeclaration": 484, "src": "6759:14:5", "typeDescriptions": {"typeIdentifier": "t_uint128", "typeString": "uint128"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_address", "typeString": "address"}, {"typeIdentifier": "t_uint128", "typeString": "uint128"}], "expression": {"id": 980, "name": "haoxToken", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 464, "src": "6725:9:5", "typeDescriptions": {"typeIdentifier": "t_contract$_IERC20_$225", "typeString": "contract IERC20"}}, "id": 982, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "memberLocation": "6735:8:5", "memberName": "transfer", "nodeType": "MemberAccess", "referencedDeclaration": 192, "src": "6725:18:5", "typeDescriptions": {"typeIdentifier": "t_function_external_nonpayable$_t_address_$_t_uint256_$returns$_t_bool_$", "typeString": "function (address,uint256) external returns (bool)"}}, "id": 986, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6725:49:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 987, "nodeType": "ExpressionStatement", "src": "6725:49:5"}]}}, {"eventCall": {"arguments": [{"id": 1001, "name": "requestId", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 928, "src": "6918:9:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, {"expression": {"id": 1002, "name": "request", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 945, "src": "6929:7:5", "typeDescriptions": {"typeIdentifier": "t_struct$_EmergencyRequest_$489_storage_ptr", "typeString": "struct HAOXVestingV2Ultra.EmergencyRequest storage pointer"}}, "id": 1003, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "6937:6:5", "memberName": "amount", "nodeType": "MemberAccess", "referencedDeclaration": 484, "src": "6929:14:5", "typeDescriptions": {"typeIdentifier": "t_uint128", "typeString": "uint128"}}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, {"typeIdentifier": "t_uint128", "typeString": "uint128"}], "id": 1000, "name": "EmergencyWithdrawExecuted", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 530, "src": "6892:25:5", "typeDescriptions": {"typeIdentifier": "t_function_event_nonpayable$_t_bytes32_$_t_uint256_$returns$__$", "typeString": "function (bytes32,uint256)"}}, "id": 1004, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "6892:52:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 1005, "nodeType": "EmitStatement", "src": "6887:57:5"}]}, "documentation": {"id": 926, "nodeType": "StructuredDocumentation", "src": "6114:42:5", "text": " @dev 执行紧急提取"}, "functionSelector": "34f4aca1", "id": 1007, "implemented": true, "kind": "function", "modifiers": [{"id": 933, "kind": "modifierInvocation", "modifierName": {"id": 932, "name": "nonReentrant", "nameLocations": ["6260:12:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 396, "src": "6260:12:5"}, "nodeType": "ModifierInvocation", "src": "6260:12:5"}], "name": "executeEmergencyWithdraw", "nameLocation": "6170:24:5", "nodeType": "FunctionDefinition", "parameters": {"id": 931, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 928, "mutability": "mutable", "name": "requestId", "nameLocation": "6212:9:5", "nodeType": "VariableDeclaration", "scope": 1007, "src": "6204:17:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}, "typeName": {"id": 927, "name": "bytes32", "nodeType": "ElementaryTypeName", "src": "6204:7:5", "typeDescriptions": {"typeIdentifier": "t_bytes32", "typeString": "bytes32"}}, "visibility": "internal"}, {"constant": false, "id": 930, "mutability": "mutable", "name": "token", "nameLocation": "6239:5:5", "nodeType": "VariableDeclaration", "scope": 1007, "src": "6231:13:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 929, "name": "address", "nodeType": "ElementaryTypeName", "src": "6231:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}], "src": "6194:56:5"}, "returnParameters": {"id": 934, "nodeType": "ParameterList", "parameters": [], "src": "6273:0:5"}, "scope": 1146, "src": "6161:790:5", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"body": {"id": 1023, "nodeType": "Block", "src": "7087:50:5", "statements": [{"expression": {"id": 1021, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"baseExpression": {"id": 1017, "name": "emergencySigners", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 503, "src": "7097:16:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_address_$_t_bool_$", "typeString": "mapping(address => bool)"}}, "id": 1019, "indexExpression": {"id": 1018, "name": "signer", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1010, "src": "7114:6:5", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": true, "nodeType": "IndexAccess", "src": "7097:24:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 1020, "name": "status", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1012, "src": "7124:6:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "src": "7097:33:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 1022, "nodeType": "ExpressionStatement", "src": "7097:33:5"}]}, "documentation": {"id": 1008, "nodeType": "StructuredDocumentation", "src": "6961:45:5", "text": " @dev 管理紧急签名者"}, "functionSelector": "200dfd5b", "id": 1024, "implemented": true, "kind": "function", "modifiers": [{"id": 1015, "kind": "modifierInvocation", "modifierName": {"id": 1014, "name": "only<PERSON><PERSON>er", "nameLocations": ["7077:9:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 58, "src": "7077:9:5"}, "nodeType": "ModifierInvocation", "src": "7077:9:5"}], "name": "setEmergency<PERSON><PERSON>er", "nameLocation": "7020:18:5", "nodeType": "FunctionDefinition", "parameters": {"id": 1013, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1010, "mutability": "mutable", "name": "signer", "nameLocation": "7047:6:5", "nodeType": "VariableDeclaration", "scope": 1024, "src": "7039:14:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}, "typeName": {"id": 1009, "name": "address", "nodeType": "ElementaryTypeName", "src": "7039:7:5", "stateMutability": "nonpayable", "typeDescriptions": {"typeIdentifier": "t_address", "typeString": "address"}}, "visibility": "internal"}, {"constant": false, "id": 1012, "mutability": "mutable", "name": "status", "nameLocation": "7060:6:5", "nodeType": "VariableDeclaration", "scope": 1024, "src": "7055:11:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 1011, "name": "bool", "nodeType": "ElementaryTypeName", "src": "7055:4:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}], "src": "7038:29:5"}, "returnParameters": {"id": 1016, "nodeType": "ParameterList", "parameters": [], "src": "7087:0:5"}, "scope": 1146, "src": "7011:126:5", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"body": {"id": 1043, "nodeType": "Block", "src": "7269:106:5", "statements": [{"expression": {"arguments": [{"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 1035, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 1033, "name": "_required", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1027, "src": "7287:9:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": ">", "rightExpression": {"hexValue": "30", "id": 1034, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "7299:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "src": "7287:13:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"hexValue": "496e76616c6964207369676e617475726520636f756e74", "id": 1036, "isConstant": false, "isLValue": false, "isPure": true, "kind": "string", "lValueRequested": false, "nodeType": "Literal", "src": "7302:25:5", "typeDescriptions": {"typeIdentifier": "t_stringliteral_89770c226a9e3d3a2bc991e190709457d0225bb21c2081dc811f8f4fe696274c", "typeString": "literal_string \"Invalid signature count\""}, "value": "Invalid signature count"}], "expression": {"argumentTypes": [{"typeIdentifier": "t_bool", "typeString": "bool"}, {"typeIdentifier": "t_stringliteral_89770c226a9e3d3a2bc991e190709457d0225bb21c2081dc811f8f4fe696274c", "typeString": "literal_string \"Invalid signature count\""}], "id": 1032, "name": "require", "nodeType": "Identifier", "overloadedDeclarations": [-18, -18], "referencedDeclaration": -18, "src": "7279:7:5", "typeDescriptions": {"typeIdentifier": "t_function_require_pure$_t_bool_$_t_string_memory_ptr_$returns$__$", "typeString": "function (bool,string memory) pure"}}, "id": 1037, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7279:49:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 1038, "nodeType": "ExpressionStatement", "src": "7279:49:5"}, {"expression": {"id": 1041, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftHandSide": {"id": 1039, "name": "requiredSignatures", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 506, "src": "7338:18:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "Assignment", "operator": "=", "rightHandSide": {"id": 1040, "name": "_required", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1027, "src": "7359:9:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "7338:30:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 1042, "nodeType": "ExpressionStatement", "src": "7338:30:5"}]}, "documentation": {"id": 1025, "nodeType": "StructuredDocumentation", "src": "7147:48:5", "text": " @dev 设置所需签名数量"}, "functionSelector": "7d2b9cc0", "id": 1044, "implemented": true, "kind": "function", "modifiers": [{"id": 1030, "kind": "modifierInvocation", "modifierName": {"id": 1029, "name": "only<PERSON><PERSON>er", "nameLocations": ["7259:9:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 58, "src": "7259:9:5"}, "nodeType": "ModifierInvocation", "src": "7259:9:5"}], "name": "setRequiredSignatures", "nameLocation": "7209:21:5", "nodeType": "FunctionDefinition", "parameters": {"id": 1028, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1027, "mutability": "mutable", "name": "_required", "nameLocation": "7239:9:5", "nodeType": "VariableDeclaration", "scope": 1044, "src": "7231:17:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1026, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7231:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "7230:19:5"}, "returnParameters": {"id": 1031, "nodeType": "ParameterList", "parameters": [], "src": "7269:0:5"}, "scope": 1146, "src": "7200:175:5", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"body": {"id": 1053, "nodeType": "Block", "src": "7469:25:5", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 1050, "name": "_pause", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 346, "src": "7479:6:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$__$returns$__$", "typeString": "function ()"}}, "id": 1051, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7479:8:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 1052, "nodeType": "ExpressionStatement", "src": "7479:8:5"}]}, "documentation": {"id": 1045, "nodeType": "StructuredDocumentation", "src": "7385:43:5", "text": " @dev 暂停/恢复合约"}, "functionSelector": "8456cb59", "id": 1054, "implemented": true, "kind": "function", "modifiers": [{"id": 1048, "kind": "modifierInvocation", "modifierName": {"id": 1047, "name": "only<PERSON><PERSON>er", "nameLocations": ["7459:9:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 58, "src": "7459:9:5"}, "nodeType": "ModifierInvocation", "src": "7459:9:5"}], "name": "pause", "nameLocation": "7442:5:5", "nodeType": "FunctionDefinition", "parameters": {"id": 1046, "nodeType": "ParameterList", "parameters": [], "src": "7447:2:5"}, "returnParameters": {"id": 1049, "nodeType": "ParameterList", "parameters": [], "src": "7469:0:5"}, "scope": 1146, "src": "7433:61:5", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"body": {"id": 1062, "nodeType": "Block", "src": "7542:27:5", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 1059, "name": "_unpause", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 362, "src": "7552:8:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_nonpayable$__$returns$__$", "typeString": "function ()"}}, "id": 1060, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "7552:10:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_tuple$__$", "typeString": "tuple()"}}, "id": 1061, "nodeType": "ExpressionStatement", "src": "7552:10:5"}]}, "functionSelector": "3f4ba83a", "id": 1063, "implemented": true, "kind": "function", "modifiers": [{"id": 1057, "kind": "modifierInvocation", "modifierName": {"id": 1056, "name": "only<PERSON><PERSON>er", "nameLocations": ["7532:9:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 58, "src": "7532:9:5"}, "nodeType": "ModifierInvocation", "src": "7532:9:5"}], "name": "unpause", "nameLocation": "7513:7:5", "nodeType": "FunctionDefinition", "parameters": {"id": 1055, "nodeType": "ParameterList", "parameters": [], "src": "7520:2:5"}, "returnParameters": {"id": 1058, "nodeType": "ParameterList", "parameters": [], "src": "7542:0:5"}, "scope": 1146, "src": "7504:65:5", "stateMutability": "nonpayable", "virtual": false, "visibility": "external"}, {"body": {"id": 1094, "nodeType": "Block", "src": "7817:217:5", "statements": [{"assignments": [1079], "declarations": [{"constant": false, "id": 1079, "mutability": "mutable", "name": "round", "nameLocation": "7840:5:5", "nodeType": "VariableDeclaration", "scope": 1094, "src": "7827:18:5", "stateVariable": false, "storageLocation": "memory", "typeDescriptions": {"typeIdentifier": "t_struct$_Round_$482_memory_ptr", "typeString": "struct HAOXVestingV2Ultra.Round"}, "typeName": {"id": 1078, "nodeType": "UserDefinedTypeName", "pathNode": {"id": 1077, "name": "Round", "nameLocations": ["7827:5:5"], "nodeType": "IdentifierPath", "referencedDeclaration": 482, "src": "7827:5:5"}, "referencedDeclaration": 482, "src": "7827:5:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Round_$482_storage_ptr", "typeString": "struct HAOXVestingV2Ultra.Round"}}, "visibility": "internal"}], "id": 1083, "initialValue": {"baseExpression": {"id": 1080, "name": "rounds", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 494, "src": "7848:6:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_Round_$482_storage_$", "typeString": "mapping(uint256 => struct HAOXVestingV2Ultra.Round storage ref)"}}, "id": 1082, "indexExpression": {"id": 1081, "name": "roundNumber", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1066, "src": "7855:11:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "7848:19:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Round_$482_storage", "typeString": "struct HAOXVestingV2Ultra.Round storage ref"}}, "nodeType": "VariableDeclarationStatement", "src": "7827:40:5"}, {"expression": {"components": [{"expression": {"id": 1084, "name": "round", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1079, "src": "7898:5:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Round_$482_memory_ptr", "typeString": "struct HAOXVestingV2Ultra.Round memory"}}, "id": 1085, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "7904:12:5", "memberName": "triggerPrice", "nodeType": "MemberAccess", "referencedDeclaration": 475, "src": "7898:18:5", "typeDescriptions": {"typeIdentifier": "t_uint128", "typeString": "uint128"}}, {"expression": {"id": 1086, "name": "round", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1079, "src": "7930:5:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Round_$482_memory_ptr", "typeString": "struct HAOXVestingV2Ultra.Round memory"}}, "id": 1087, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "7936:17:5", "memberName": "priceConditionMet", "nodeType": "MemberAccess", "referencedDeclaration": 479, "src": "7930:23:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"expression": {"id": 1088, "name": "round", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1079, "src": "7967:5:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Round_$482_memory_ptr", "typeString": "struct HAOXVestingV2Ultra.Round memory"}}, "id": 1089, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "7973:8:5", "memberName": "unlocked", "nodeType": "MemberAccess", "referencedDeclaration": 481, "src": "7967:14:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, {"expression": {"id": 1090, "name": "round", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1079, "src": "7995:5:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Round_$482_memory_ptr", "typeString": "struct HAOXVestingV2Ultra.Round memory"}}, "id": 1091, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "8001:16:5", "memberName": "priceReachedTime", "nodeType": "MemberAccess", "referencedDeclaration": 477, "src": "7995:22:5", "typeDescriptions": {"typeIdentifier": "t_uint64", "typeString": "uint64"}}], "id": 1092, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "7884:143:5", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_uint128_$_t_bool_$_t_bool_$_t_uint64_$", "typeString": "tuple(uint128,bool,bool,uint64)"}}, "functionReturnParameters": 1076, "id": 1093, "nodeType": "Return", "src": "7877:150:5"}]}, "documentation": {"id": 1064, "nodeType": "StructuredDocumentation", "src": "7579:42:5", "text": " @dev 获取轮次信息"}, "functionSelector": "88c3ffb0", "id": 1095, "implemented": true, "kind": "function", "modifiers": [], "name": "getRoundInfo", "nameLocation": "7635:12:5", "nodeType": "FunctionDefinition", "parameters": {"id": 1067, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1066, "mutability": "mutable", "name": "roundNumber", "nameLocation": "7656:11:5", "nodeType": "VariableDeclaration", "scope": 1095, "src": "7648:19:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1065, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7648:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "7647:21:5"}, "returnParameters": {"id": 1076, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1069, "mutability": "mutable", "name": "triggerPrice", "nameLocation": "7709:12:5", "nodeType": "VariableDeclaration", "scope": 1095, "src": "7701:20:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1068, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7701:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 1071, "mutability": "mutable", "name": "priceConditionMet", "nameLocation": "7736:17:5", "nodeType": "VariableDeclaration", "scope": 1095, "src": "7731:22:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 1070, "name": "bool", "nodeType": "ElementaryTypeName", "src": "7731:4:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, {"constant": false, "id": 1073, "mutability": "mutable", "name": "unlocked", "nameLocation": "7768:8:5", "nodeType": "VariableDeclaration", "scope": 1095, "src": "7763:13:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}, "typeName": {"id": 1072, "name": "bool", "nodeType": "ElementaryTypeName", "src": "7763:4:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "visibility": "internal"}, {"constant": false, "id": 1075, "mutability": "mutable", "name": "priceReachedTime", "nameLocation": "7794:16:5", "nodeType": "VariableDeclaration", "scope": 1095, "src": "7786:24:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1074, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "7786:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "7691:125:5"}, "scope": 1146, "src": "7626:408:5", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"body": {"id": 1134, "nodeType": "Block", "src": "8246:213:5", "statements": [{"assignments": [1106], "declarations": [{"constant": false, "id": 1106, "mutability": "mutable", "name": "unlocked", "nameLocation": "8264:8:5", "nodeType": "VariableDeclaration", "scope": 1134, "src": "8256:16:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1105, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "8256:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 1108, "initialValue": {"hexValue": "30", "id": 1107, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "8275:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_0_by_1", "typeString": "int_const 0"}, "value": "0"}, "nodeType": "VariableDeclarationStatement", "src": "8256:20:5"}, {"body": {"id": 1127, "nodeType": "Block", "src": "8330:59:5", "statements": [{"condition": {"expression": {"baseExpression": {"id": 1119, "name": "rounds", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 494, "src": "8348:6:5", "typeDescriptions": {"typeIdentifier": "t_mapping$_t_uint256_$_t_struct$_Round_$482_storage_$", "typeString": "mapping(uint256 => struct HAOXVestingV2Ultra.Round storage ref)"}}, "id": 1121, "indexExpression": {"id": 1120, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1110, "src": "8355:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "nodeType": "IndexAccess", "src": "8348:9:5", "typeDescriptions": {"typeIdentifier": "t_struct$_Round_$482_storage", "typeString": "struct HAOXVestingV2Ultra.Round storage ref"}}, "id": 1122, "isConstant": false, "isLValue": true, "isPure": false, "lValueRequested": false, "memberLocation": "8358:8:5", "memberName": "unlocked", "nodeType": "MemberAccess", "referencedDeclaration": 481, "src": "8348:18:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 1126, "nodeType": "IfStatement", "src": "8344:34:5", "trueBody": {"expression": {"id": 1124, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "8368:10:5", "subExpression": {"id": 1123, "name": "unlocked", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1106, "src": "8368:8:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 1125, "nodeType": "ExpressionStatement", "src": "8368:10:5"}}]}, "condition": {"commonType": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "id": 1115, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "leftExpression": {"id": 1113, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1110, "src": "8306:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "nodeType": "BinaryOperation", "operator": "<=", "rightExpression": {"id": 1114, "name": "TOTAL_ROUNDS", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 448, "src": "8311:12:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "src": "8306:17:5", "typeDescriptions": {"typeIdentifier": "t_bool", "typeString": "bool"}}, "id": 1128, "initializationExpression": {"assignments": [1110], "declarations": [{"constant": false, "id": 1110, "mutability": "mutable", "name": "i", "nameLocation": "8299:1:5", "nodeType": "VariableDeclaration", "scope": 1128, "src": "8291:9:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1109, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "8291:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "id": 1112, "initialValue": {"hexValue": "31", "id": 1111, "isConstant": false, "isLValue": false, "isPure": true, "kind": "number", "lValueRequested": false, "nodeType": "Literal", "src": "8303:1:5", "typeDescriptions": {"typeIdentifier": "t_rational_1_by_1", "typeString": "int_const 1"}, "value": "1"}, "nodeType": "VariableDeclarationStatement", "src": "8291:13:5"}, "loopExpression": {"expression": {"id": 1117, "isConstant": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "UnaryOperation", "operator": "++", "prefix": false, "src": "8325:3:5", "subExpression": {"id": 1116, "name": "i", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1110, "src": "8325:1:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "id": 1118, "nodeType": "ExpressionStatement", "src": "8325:3:5"}, "nodeType": "ForStatement", "src": "8286:103:5"}, {"expression": {"components": [{"id": 1129, "name": "TOTAL_ROUNDS", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 448, "src": "8415:12:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 1130, "name": "currentRound", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 473, "src": "8429:12:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, {"id": 1131, "name": "unlocked", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 1106, "src": "8443:8:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}], "id": 1132, "isConstant": false, "isInlineArray": false, "isLValue": false, "isPure": false, "lValueRequested": false, "nodeType": "TupleExpression", "src": "8414:38:5", "typeDescriptions": {"typeIdentifier": "t_tuple$_t_uint256_$_t_uint256_$_t_uint256_$", "typeString": "tuple(uint256,uint256,uint256)"}}, "functionReturnParameters": 1104, "id": 1133, "nodeType": "Return", "src": "8407:45:5"}]}, "documentation": {"id": 1096, "nodeType": "StructuredDocumentation", "src": "8044:42:5", "text": " @dev 获取解锁进度"}, "functionSelector": "ff5f1e91", "id": 1135, "implemented": true, "kind": "function", "modifiers": [], "name": "getUnlockProgress", "nameLocation": "8100:17:5", "nodeType": "FunctionDefinition", "parameters": {"id": 1097, "nodeType": "ParameterList", "parameters": [], "src": "8117:2:5"}, "returnParameters": {"id": 1104, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1099, "mutability": "mutable", "name": "totalRounds", "nameLocation": "8160:11:5", "nodeType": "VariableDeclaration", "scope": 1135, "src": "8152:19:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1098, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "8152:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 1101, "mutability": "mutable", "name": "currentRoundNumber", "nameLocation": "8189:18:5", "nodeType": "VariableDeclaration", "scope": 1135, "src": "8181:26:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1100, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "8181:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}, {"constant": false, "id": 1103, "mutability": "mutable", "name": "unlockedRounds", "nameLocation": "8225:14:5", "nodeType": "VariableDeclaration", "scope": 1135, "src": "8217:22:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1102, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "8217:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "8142:103:5"}, "scope": 1146, "src": "8091:368:5", "stateMutability": "view", "virtual": false, "visibility": "external"}, {"body": {"id": 1144, "nodeType": "Block", "src": "8593:42:5", "statements": [{"expression": {"arguments": [], "expression": {"argumentTypes": [], "id": 1141, "name": "_getCurrentPrice", "nodeType": "Identifier", "overloadedDeclarations": [], "referencedDeclaration": 867, "src": "8610:16:5", "typeDescriptions": {"typeIdentifier": "t_function_internal_view$__$returns$_t_uint256_$", "typeString": "function () view returns (uint256)"}}, "id": 1142, "isConstant": false, "isLValue": false, "isPure": false, "kind": "functionCall", "lValueRequested": false, "nameLocations": [], "names": [], "nodeType": "FunctionCall", "src": "8610:18:5", "tryCall": false, "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "functionReturnParameters": 1140, "id": 1143, "nodeType": "Return", "src": "8603:25:5"}]}, "documentation": {"id": 1136, "nodeType": "StructuredDocumentation", "src": "8469:60:5", "text": " @dev 获取当前价格（外部调用）"}, "functionSelector": "eb91d37e", "id": 1145, "implemented": true, "kind": "function", "modifiers": [], "name": "getCurrentPrice", "nameLocation": "8543:15:5", "nodeType": "FunctionDefinition", "parameters": {"id": 1137, "nodeType": "ParameterList", "parameters": [], "src": "8558:2:5"}, "returnParameters": {"id": 1140, "nodeType": "ParameterList", "parameters": [{"constant": false, "id": 1139, "mutability": "mutable", "name": "", "nameLocation": "-1:-1:-1", "nodeType": "VariableDeclaration", "scope": 1145, "src": "8584:7:5", "stateVariable": false, "storageLocation": "default", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}, "typeName": {"id": 1138, "name": "uint256", "nodeType": "ElementaryTypeName", "src": "8584:7:5", "typeDescriptions": {"typeIdentifier": "t_uint256", "typeString": "uint256"}}, "visibility": "internal"}], "src": "8583:9:5"}, "scope": 1146, "src": "8534:101:5", "stateMutability": "view", "virtual": false, "visibility": "external"}], "scope": 1147, "src": "456:8181:5", "usedErrors": [13, 18, 277, 280, 377], "usedEvents": [24, 269, 274, 512, 518, 524, 530]}], "src": "32:8606:5"}, "id": 5}}, "contracts": {"@openzeppelin/contracts/access/Ownable.sol": {"Ownable": {"abi": [{"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {"owner()": "8da5cb5b", "renounceOwnership()": "715018a6", "transferOwnership(address)": "f2fde38b"}}, "metadata": "{\"compiler\":{\"version\":\"0.8.20+commit.a1b79de6\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Contract module which provides a basic access control mechanism, where there is an account (an owner) that can be granted exclusive access to specific functions. The initial owner is set to the address provided by the deployer. This can later be changed with {transferOwnership}. This module is used through inheritance. It will make available the modifier `onlyOwner`, which can be applied to your functions to restrict their use to the owner.\",\"errors\":{\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}]},\"kind\":\"dev\",\"methods\":{\"constructor\":{\"details\":\"Initializes the contract setting the address provided by the deployer as the initial owner.\"},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"@openzeppelin/contracts/access/Ownable.sol\":\"Ownable\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[],\"viaIR\":true},\"sources\":{\"@openzeppelin/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"@openzeppelin/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]}},\"version\":1}"}}, "@openzeppelin/contracts/token/ERC20/IERC20.sol": {"IERC20": {"abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {"allowance(address,address)": "dd62ed3e", "approve(address,uint256)": "095ea7b3", "balanceOf(address)": "70a08231", "totalSupply()": "18160ddd", "transfer(address,uint256)": "a9059cbb", "transferFrom(address,address,uint256)": "23b872dd"}}, "metadata": "{\"compiler\":{\"version\":\"0.8.20+commit.a1b79de6\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Approval\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"Transfer\",\"type\":\"event\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"}],\"name\":\"allowance\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"spender\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"approve\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"balanceOf\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"totalSupply\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transfer\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"from\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"to\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"value\",\"type\":\"uint256\"}],\"name\":\"transferFrom\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Interface of the ERC-20 standard as defined in the ERC.\",\"events\":{\"Approval(address,address,uint256)\":{\"details\":\"Emitted when the allowance of a `spender` for an `owner` is set by a call to {approve}. `value` is the new allowance.\"},\"Transfer(address,address,uint256)\":{\"details\":\"Emitted when `value` tokens are moved from one account (`from`) to another (`to`). Note that `value` may be zero.\"}},\"kind\":\"dev\",\"methods\":{\"allowance(address,address)\":{\"details\":\"Returns the remaining number of tokens that `spender` will be allowed to spend on behalf of `owner` through {transferFrom}. This is zero by default. This value changes when {approve} or {transferFrom} are called.\"},\"approve(address,uint256)\":{\"details\":\"Sets a `value` amount of tokens as the allowance of `spender` over the caller's tokens. Returns a boolean value indicating whether the operation succeeded. IMPORTANT: Beware that changing an allowance with this method brings the risk that someone may use both the old and the new allowance by unfortunate transaction ordering. One possible solution to mitigate this race condition is to first reduce the spender's allowance to 0 and set the desired value afterwards: https://github.com/ethereum/EIPs/issues/20#issuecomment-********* Emits an {Approval} event.\"},\"balanceOf(address)\":{\"details\":\"Returns the value of tokens owned by `account`.\"},\"totalSupply()\":{\"details\":\"Returns the value of tokens in existence.\"},\"transfer(address,uint256)\":{\"details\":\"Moves a `value` amount of tokens from the caller's account to `to`. Returns a boolean value indicating whether the operation succeeded. Emits a {Transfer} event.\"},\"transferFrom(address,address,uint256)\":{\"details\":\"Moves a `value` amount of tokens from `from` to `to` using the allowance mechanism. `value` is then deducted from the caller's allowance. Returns a boolean value indicating whether the operation succeeded. Emits a {Transfer} event.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"@openzeppelin/contracts/token/ERC20/IERC20.sol\":\"IERC20\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[],\"viaIR\":true},\"sources\":{\"@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303\",\"dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV\"]}},\"version\":1}"}}, "@openzeppelin/contracts/utils/Context.sol": {"Context": {"abi": [], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {}}, "metadata": "{\"compiler\":{\"version\":\"0.8.20+commit.a1b79de6\"},\"language\":\"Solidity\",\"output\":{\"abi\":[],\"devdoc\":{\"details\":\"Provides information about the current execution context, including the sender of the transaction and its data. While these are generally available via msg.sender and msg.data, they should not be accessed in such a direct manner, since when dealing with meta-transactions the account sending and paying for execution may not be the actual sender (as far as an application is concerned). This contract is only required for intermediate, library-like contracts.\",\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"@openzeppelin/contracts/utils/Context.sol\":\"Context\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[],\"viaIR\":true},\"sources\":{\"@openzeppelin/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]}},\"version\":1}"}}, "@openzeppelin/contracts/utils/Pausable.sol": {"Pausable": {"abi": [{"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {"paused()": "5c975abb"}}, "metadata": "{\"compiler\":{\"version\":\"0.8.20+commit.a1b79de6\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"EnforcedPause\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ExpectedPause\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Paused\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Unpaused\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"paused\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"Contract module which allows children to implement an emergency stop mechanism that can be triggered by an authorized account. This module is used through inheritance. It will make available the modifiers `whenNotPaused` and `whenPaused`, which can be applied to the functions of your contract. Note that they will not be pausable by simply including this module, only once the modifiers are put in place.\",\"errors\":{\"EnforcedPause()\":[{\"details\":\"The operation failed because the contract is paused.\"}],\"ExpectedPause()\":[{\"details\":\"The operation failed because the contract is not paused.\"}]},\"events\":{\"Paused(address)\":{\"details\":\"Emitted when the pause is triggered by `account`.\"},\"Unpaused(address)\":{\"details\":\"Emitted when the pause is lifted by `account`.\"}},\"kind\":\"dev\",\"methods\":{\"paused()\":{\"details\":\"Returns true if the contract is paused, and false otherwise.\"}},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"@openzeppelin/contracts/utils/Pausable.sol\":\"Pausable\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[],\"viaIR\":true},\"sources\":{\"@openzeppelin/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"@openzeppelin/contracts/utils/Pausable.sol\":{\"keccak256\":\"0xdb484371dfbb848cb6f5d70464e9ac9b2900e4164ead76bbce4fef0b44bcc68f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f9d6f6f6600a2bec622f699081b58350873b5e63ce05464d17d674a290bb8a7c\",\"dweb:/ipfs/QmQKVzSQY1PM3Bid4QhgVVZyx6B4Jx7XgaQzLKHj38vJz8\"]}},\"version\":1}"}}, "@openzeppelin/contracts/utils/ReentrancyGuard.sol": {"ReentrancyGuard": {"abi": [{"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}], "evm": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "methodIdentifiers": {}}, "metadata": "{\"compiler\":{\"version\":\"0.8.20+commit.a1b79de6\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[],\"name\":\"ReentrancyGuardReentrantCall\",\"type\":\"error\"}],\"devdoc\":{\"details\":\"Contract module that helps prevent reentrant calls to a function. Inheriting from `ReentrancyGuard` will make the {nonReentrant} modifier available, which can be applied to functions to make sure there are no nested (reentrant) calls to them. Note that because there is a single `nonReentrant` guard, functions marked as `nonReentrant` may not call one another. This can be worked around by making those functions `private`, and then adding `external` `nonReentrant` entry points to them. TIP: If EIP-1153 (transient storage) is available on the chain you're deploying at, consider using {ReentrancyGuardTransient} instead. TIP: If you would like to learn more about reentrancy and alternative ways to protect against it, check out our blog post https://blog.openzeppelin.com/reentrancy-after-istanbul/[Reentrancy After Istanbul].\",\"errors\":{\"ReentrancyGuardReentrantCall()\":[{\"details\":\"Unauthorized reentrant call.\"}]},\"kind\":\"dev\",\"methods\":{},\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"@openzeppelin/contracts/utils/ReentrancyGuard.sol\":\"ReentrancyGuard\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[],\"viaIR\":true},\"sources\":{\"@openzeppelin/contracts/utils/ReentrancyGuard.sol\":{\"keccak256\":\"0x11a5a79827df29e915a12740caf62fe21ebe27c08c9ae3e09abe9ee3ba3866d3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3cf0c69ab827e3251db9ee6a50647d62c90ba580a4d7bbff21f2bea39e7b2f4a\",\"dweb:/ipfs/QmZiKwtKU1SBX4RGfQtY7PZfiapbbu6SZ9vizGQD9UHjRA\"]}},\"version\":1}"}}, "contracts/HAOXVestingV2Ultra.sol": {"HAOXVestingV2Ultra": {"abi": [{"inputs": [{"internalType": "address", "name": "_haoxToken", "type": "address"}, {"internalType": "address", "name": "_priceOracle", "type": "address"}, {"internalType": "address", "name": "_projectWallet", "type": "address"}, {"internalType": "address", "name": "_communityWallet", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "EmergencyWithdrawExecuted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "EmergencyWithdrawRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "price", "type": "uint256"}], "name": "PriceConditionMet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "RoundUnlocked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "EMERGENCY_DELAY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_EMERGENCY_AMOUNT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PRICE_MAINTAIN_DURATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOTAL_ROUNDS", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "checkPriceCondition", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "communityWallet", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "currentRound", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "emergencyRequests", "outputs": [{"internalType": "uint128", "name": "amount", "type": "uint128"}, {"internalType": "uint64", "name": "requestTime", "type": "uint64"}, {"internalType": "bool", "name": "executed", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "emergencySigners", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"internalType": "address", "name": "token", "type": "address"}], "name": "executeEmergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getCurrentPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "roundNumber", "type": "uint256"}], "name": "getRoundInfo", "outputs": [{"internalType": "uint256", "name": "triggerPrice", "type": "uint256"}, {"internalType": "bool", "name": "priceConditionMet", "type": "bool"}, {"internalType": "bool", "name": "unlocked", "type": "bool"}, {"internalType": "uint256", "name": "priceReachedTime", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getUnlockProgress", "outputs": [{"internalType": "uint256", "name": "totalRounds", "type": "uint256"}, {"internalType": "uint256", "name": "currentRoundNumber", "type": "uint256"}, {"internalType": "uint256", "name": "unlockedRounds", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "haoxToken", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "priceOracle", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "projectWallet", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "requestEmergencyWithdraw", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "requiredSignatures", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "rounds", "outputs": [{"internalType": "uint128", "name": "triggerPrice", "type": "uint128"}, {"internalType": "uint64", "name": "priceReachedTime", "type": "uint64"}, {"internalType": "bool", "name": "priceConditionMet", "type": "bool"}, {"internalType": "bool", "name": "unlocked", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "bool", "name": "status", "type": "bool"}], "name": "setEmergency<PERSON><PERSON>er", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_required", "type": "uint256"}], "name": "setRequiredSignatures", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "evm": {"bytecode": {"functionDebugData": {"abi_decode_address_fromMemory": {"entryPoint": 1279, "id": null, "parameterSlots": 1, "returnSlots": 1}}, "generatedSources": [], "linkReferences": {}, "object": "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", "opcodes": "PUSH2 0x100 CALLVALUE PUSH3 0x4FA JUMPI PUSH1 0x1F PUSH3 0x15E5 CODESIZE DUP2 SWAP1 SUB SWAP2 DUP3 ADD PUSH1 0x1F NOT AND DUP4 ADD SWAP2 PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP4 GT DUP5 DUP5 LT OR PUSH3 0x3BA JUMPI DUP1 DUP5 SWAP3 PUSH1 0x80 SWAP5 PUSH1 0x40 MSTORE DUP4 CODECOPY DUP2 ADD SUB SLT PUSH3 0x4FA JUMPI PUSH3 0x4E DUP2 PUSH3 0x4FF JUMP JUMPDEST SWAP1 PUSH3 0x5D PUSH1 0x20 DUP3 ADD PUSH3 0x4FF JUMP JUMPDEST PUSH3 0x79 PUSH1 0x60 PUSH3 0x71 PUSH1 0x40 DUP6 ADD PUSH3 0x4FF JUMP JUMPDEST SWAP4 ADD PUSH3 0x4FF JUMP JUMPDEST SWAP3 CALLER ISZERO PUSH3 0x4E1 JUMPI PUSH1 0x0 DUP1 SLOAD CALLER PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB NOT DUP3 AND DUP2 OR DUP4 SSTORE PUSH1 0x40 MLOAD SWAP4 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP4 DUP5 SWAP4 SWAP2 SWAP3 SWAP2 DUP5 AND SWAP1 PUSH32 0x8BE0079C531659141344CD1FD0A4F28419497F9722A3DAAFE3B4186F6B6457E0 SWAP1 DUP1 LOG3 PUSH1 0x1 DUP1 SSTORE PUSH1 0x1 PUSH1 0x3 SSTORE PUSH1 0x1 PUSH1 0x7 SSTORE AND SWAP2 DUP3 ISZERO PUSH3 0x49F JUMPI POP DUP1 DUP4 AND ISZERO PUSH3 0x45A JUMPI DUP1 DUP5 AND ISZERO PUSH3 0x415 JUMPI DUP5 AND ISZERO PUSH3 0x3D0 JUMPI PUSH1 0x80 MSTORE PUSH1 0xA0 MSTORE PUSH1 0xC0 MSTORE PUSH1 0xE0 MSTORE CALLER PUSH1 0x0 SWAP1 DUP2 MSTORE PUSH1 0x6 PUSH1 0x20 MSTORE PUSH1 0x40 SWAP1 DUP2 SWAP1 KECCAK256 DUP1 SLOAD PUSH1 0xFF NOT AND PUSH1 0x1 OR SWAP1 SSTORE MLOAD PUSH2 0x3E0 DUP2 ADD PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP2 GT DUP3 DUP3 LT OR PUSH3 0x3BA JUMPI PUSH1 0x40 MSTORE PUSH3 0xF4240 DUP2 MSTORE PUSH3 0x1E8480 PUSH1 0x20 DUP3 ADD MSTORE PUSH3 0x2DC6C0 PUSH1 0x40 DUP3 ADD MSTORE PUSH3 0x3D0900 PUSH1 0x60 DUP3 ADD MSTORE PUSH3 0x4C4B40 PUSH1 0x80 DUP3 ADD MSTORE PUSH3 0x5B8D80 PUSH1 0xA0 DUP3 ADD MSTORE PUSH3 0x6ACFC0 PUSH1 0xC0 DUP3 ADD MSTORE PUSH3 0x7A1200 PUSH1 0xE0 DUP3 ADD MSTORE PUSH3 0x895440 PUSH2 0x100 DUP3 ADD MSTORE PUSH3 0x989680 PUSH2 0x120 DUP3 ADD MSTORE PUSH3 0xA7D8C0 PUSH2 0x140 DUP3 ADD MSTORE PUSH3 0xB71B00 PUSH2 0x160 DUP3 ADD MSTORE PUSH3 0xC65D40 PUSH2 0x180 DUP3 ADD MSTORE PUSH3 0xD59F80 PUSH2 0x1A0 DUP3 ADD MSTORE PUSH3 0xE4E1C0 PUSH2 0x1C0 DUP3 ADD MSTORE PUSH3 0xF42400 PUSH2 0x1E0 DUP3 ADD MSTORE PUSH4 0x1036640 PUSH2 0x200 DUP3 ADD MSTORE PUSH4 0x112A880 PUSH2 0x220 DUP3 ADD MSTORE PUSH4 0x121EAC0 PUSH2 0x240 DUP3 ADD MSTORE PUSH4 0x1312D00 PUSH2 0x260 DUP3 ADD MSTORE PUSH4 0x17D7840 PUSH2 0x280 DUP3 ADD MSTORE PUSH4 0x1C9C380 PUSH2 0x2A0 DUP3 ADD MSTORE PUSH4 0x2160EC0 PUSH2 0x2C0 DUP3 ADD MSTORE PUSH4 0x2625A00 PUSH2 0x2E0 DUP3 ADD MSTORE PUSH4 0x2AEA540 PUSH2 0x300 DUP3 ADD MSTORE PUSH4 0x2FAF080 PUSH2 0x320 DUP3 ADD MSTORE PUSH4 0x3938700 PUSH2 0x340 DUP3 ADD MSTORE PUSH4 0x42C1D80 PUSH2 0x360 DUP3 ADD MSTORE PUSH4 0x4C4B400 PUSH2 0x380 DUP3 ADD MSTORE PUSH4 0x55D4A80 PUSH2 0x3A0 DUP3 ADD MSTORE PUSH4 0x5F5E100 PUSH2 0x3C0 DUP3 ADD MSTORE PUSH1 0x0 JUMPDEST PUSH1 0x1F DUP2 LT PUSH3 0x2DA JUMPI PUSH1 0x40 MLOAD PUSH2 0x10D0 SWAP1 DUP2 PUSH3 0x515 DUP3 CODECOPY PUSH1 0x80 MLOAD DUP2 DUP2 DUP2 PUSH2 0x902 ADD MSTORE PUSH2 0xBF6 ADD MSTORE PUSH1 0xA0 MLOAD DUP2 DUP2 DUP2 PUSH2 0xBB2 ADD MSTORE PUSH2 0xF84 ADD MSTORE PUSH1 0xC0 MLOAD DUP2 DUP2 DUP2 PUSH2 0x34A ADD MSTORE DUP2 DUP2 PUSH2 0x94A ADD MSTORE PUSH2 0xA28 ADD MSTORE PUSH1 0xE0 MLOAD DUP2 PUSH2 0x306 ADD MSTORE RETURN JUMPDEST PUSH1 0x5 DUP2 SWAP1 SHL DUP3 ADD MLOAD PUSH1 0x40 MLOAD SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0x80 SHL SUB AND PUSH1 0x80 DUP3 ADD PUSH1 0x1 PUSH1 0x1 PUSH1 0x40 SHL SUB DUP2 GT DUP4 DUP3 LT OR PUSH3 0x3BA JUMPI PUSH1 0x40 MSTORE DUP2 MSTORE PUSH1 0x0 PUSH1 0x20 DUP3 ADD MSTORE PUSH1 0x0 PUSH1 0x40 DUP3 ADD MSTORE PUSH1 0x0 PUSH1 0x60 DUP3 ADD MSTORE PUSH1 0x1 DUP3 ADD DUP3 GT PUSH3 0x3A4 JUMPI PUSH1 0x1 DUP3 ADD PUSH1 0x0 MSTORE PUSH1 0x4 PUSH1 0x20 MSTORE PUSH1 0x40 PUSH1 0x0 KECCAK256 SWAP1 PUSH1 0x1 DUP1 PUSH1 0x80 SHL SUB DUP2 MLOAD AND SWAP1 DUP3 SLOAD SWAP2 PUSH1 0x1 PUSH1 0x80 SHL PUSH1 0x1 PUSH1 0xC0 SHL SUB PUSH1 0x20 DUP4 ADD MLOAD PUSH1 0x80 SHL AND SWAP1 PUSH1 0xFF PUSH1 0xC0 SHL PUSH1 0x40 DUP5 ADD MLOAD ISZERO ISZERO PUSH1 0xC0 SHL AND SWAP3 PUSH1 0x60 PUSH1 0xFF PUSH1 0xC8 SHL SWAP2 ADD MLOAD ISZERO ISZERO PUSH1 0xC8 SHL AND SWAP4 PUSH1 0x1 DUP1 PUSH1 0xD0 SHL SUB NOT AND OR OR OR OR SWAP1 SSTORE PUSH1 0x0 NOT DUP2 EQ PUSH3 0x3A4 JUMPI PUSH1 0x1 ADD PUSH3 0x281 JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x11 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x41 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x18 PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x496E76616C696420636F6D6D756E6974792077616C6C65740000000000000000 PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x16 PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x496E76616C69642070726F6A6563742077616C6C657400000000000000000000 PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x16 PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x496E76616C6964206F7261636C65206164647265737300000000000000000000 PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x15 PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x496E76616C696420746F6B656E20616464726573730000000000000000000000 PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST PUSH1 0x40 MLOAD PUSH4 0x1E4FBDF7 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x0 PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST MLOAD SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP3 AND DUP3 SUB PUSH3 0x4FA JUMPI JUMP INVALID PUSH1 0x80 PUSH1 0x40 DUP2 DUP2 MSTORE PUSH1 0x4 SWAP2 DUP3 CALLDATASIZE LT ISZERO PUSH2 0x16 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH1 0x0 SWAP1 DUP2 CALLDATALOAD PUSH1 0xE0 SHR SWAP1 DUP2 PUSH4 0x557AC41 EQ PUSH2 0xC7B JUMPI POP DUP1 PUSH4 0x200DFD5B EQ PUSH2 0xC25 JUMPI DUP1 PUSH4 0x25DBEC9D EQ PUSH2 0xBE1 JUMPI DUP1 PUSH4 0x2630C12F EQ PUSH2 0xB9D JUMPI DUP1 PUSH4 0x34F4ACA1 EQ PUSH2 0x866 JUMPI DUP1 PUSH4 0x3F4BA83A EQ PUSH2 0x7FC JUMPI DUP1 PUSH4 0x540797A5 EQ PUSH2 0x539 JUMPI DUP1 PUSH4 0x5C975ABB EQ PUSH2 0x7D8 JUMPI DUP1 PUSH4 0x687404B2 EQ PUSH2 0x7B4 JUMPI DUP1 PUSH4 0x715018A6 EQ PUSH2 0x75A JUMPI DUP1 PUSH4 0x7C7F4CE5 EQ PUSH2 0x5AD JUMPI DUP1 PUSH4 0x7D2B9CC0 EQ PUSH2 0x53E JUMPI DUP1 PUSH4 0x82944E2D EQ PUSH2 0x539 JUMPI DUP1 PUSH4 0x8456CB59 EQ PUSH2 0x4DE JUMPI DUP1 PUSH4 0x88C3FFB0 EQ PUSH2 0x43C JUMPI DUP1 PUSH4 0x8A19C8BC EQ PUSH2 0x41D JUMPI DUP1 PUSH4 0x8C65C81F EQ PUSH2 0x3C0 JUMPI DUP1 PUSH4 0x8D068043 EQ PUSH2 0x3A1 JUMPI DUP1 PUSH4 0x8DA5CB5B EQ PUSH2 0x379 JUMPI DUP1 PUSH4 0xBEB08AB9 EQ PUSH2 0x335 JUMPI DUP1 PUSH4 0xC7574839 EQ PUSH2 0x2F1 JUMPI DUP1 PUSH4 0xCE8DC388 EQ PUSH2 0x2D5 JUMPI DUP1 PUSH4 0xCF9D226E EQ PUSH2 0x2B0 JUMPI DUP1 PUSH4 0xD0F694CB EQ PUSH2 0x272 JUMPI DUP1 PUSH4 0xEB91D37E EQ PUSH2 0x24A JUMPI DUP1 PUSH4 0xF2FDE38B EQ PUSH2 0x1BF JUMPI PUSH4 0xFF5F1E91 EQ PUSH2 0x14D JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST CALLVALUE PUSH2 0x1BC JUMPI DUP1 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x1BC JUMPI DUP1 PUSH1 0x1 JUMPDEST PUSH1 0x1F DUP2 GT ISZERO PUSH2 0x183 JUMPI PUSH1 0x60 DUP5 DUP5 PUSH1 0x3 SLOAD SWAP2 DUP1 MLOAD SWAP3 PUSH1 0x1F DUP5 MSTORE PUSH1 0x20 DUP5 ADD MSTORE DUP3 ADD MSTORE RETURN JUMPDEST DUP1 DUP3 MSTORE DUP5 PUSH1 0x20 MSTORE PUSH1 0xFF DUP5 DUP4 KECCAK256 SLOAD PUSH1 0xC8 SHR AND PUSH2 0x1A7 JUMPI JUMPDEST PUSH2 0x1A2 SWAP1 PUSH2 0x1075 JUMP JUMPDEST PUSH2 0x161 JUMP JUMPDEST SWAP2 PUSH2 0x1B4 PUSH2 0x1A2 SWAP2 PUSH2 0x1075 JUMP JUMPDEST SWAP3 SWAP1 POP PUSH2 0x199 JUMP JUMPDEST DUP1 REVERT JUMPDEST POP SWAP2 CALLVALUE PUSH2 0x246 JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x246 JUMPI PUSH2 0x1DA PUSH2 0xCCC JUMP JUMPDEST SWAP1 PUSH2 0x1E3 PUSH2 0xD05 JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP2 DUP3 AND SWAP3 DUP4 ISZERO PUSH2 0x230 JUMPI POP POP DUP3 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB NOT DUP2 AND DUP4 OR DUP5 SSTORE AND PUSH32 0x8BE0079C531659141344CD1FD0A4F28419497F9722A3DAAFE3B4186F6B6457E0 DUP4 DUP1 LOG3 DUP1 RETURN JUMPDEST MLOAD PUSH4 0x1E4FBDF7 PUSH1 0xE0 SHL DUP2 MSTORE SWAP1 DUP2 ADD DUP5 SWAP1 MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST DUP3 DUP1 REVERT JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x26E JUMPI PUSH1 0x20 SWAP1 PUSH2 0x267 PUSH2 0xF46 JUMP JUMPDEST SWAP1 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST POP DUP1 REVERT JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x26E JUMPI PUSH1 0x20 SWAP2 PUSH1 0xFF SWAP1 DUP3 SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB PUSH2 0x29D PUSH2 0xCCC JUMP JUMPDEST AND DUP2 MSTORE PUSH1 0x6 DUP6 MSTORE KECCAK256 SLOAD AND SWAP1 MLOAD SWAP1 ISZERO ISZERO DUP2 MSTORE RETURN JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x26E JUMPI PUSH1 0x20 SWAP1 MLOAD PUSH10 0xD3C21BCECCEDA1000000 DUP2 MSTORE RETURN JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x26E JUMPI PUSH1 0x20 SWAP1 MLOAD PUSH1 0x1F DUP2 MSTORE RETURN JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x26E JUMPI MLOAD PUSH32 0x0 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND DUP2 MSTORE PUSH1 0x20 SWAP1 RETURN JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x26E JUMPI MLOAD PUSH32 0x0 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND DUP2 MSTORE PUSH1 0x20 SWAP1 RETURN JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x26E JUMPI SWAP1 SLOAD SWAP1 MLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP1 SWAP2 AND DUP2 MSTORE PUSH1 0x20 SWAP1 RETURN JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x26E JUMPI PUSH1 0x20 SWAP1 PUSH1 0x7 SLOAD SWAP1 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST POP SWAP2 CALLVALUE PUSH2 0x246 JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x246 JUMPI DUP2 PUSH1 0x80 SWAP4 DUP3 PUSH1 0xFF SWAP4 CALLDATALOAD DUP3 MSTORE PUSH1 0x20 MSTORE KECCAK256 SLOAD DUP3 MLOAD SWAP3 PUSH1 0x1 PUSH1 0x1 PUSH1 0x80 SHL SUB DUP3 AND DUP5 MSTORE PUSH8 0xFFFFFFFFFFFFFFFF DUP3 DUP7 SHR AND PUSH1 0x20 DUP6 ADD MSTORE DUP3 DUP3 PUSH1 0xC0 SHR AND ISZERO ISZERO SWAP1 DUP5 ADD MSTORE PUSH1 0xC8 SHR AND ISZERO ISZERO PUSH1 0x60 DUP3 ADD MSTORE RETURN JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x26E JUMPI PUSH1 0x20 SWAP1 PUSH1 0x3 SLOAD SWAP1 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x26E JUMPI DUP3 CALLDATALOAD DUP3 MSTORE DUP3 PUSH1 0x20 MSTORE DUP1 DUP3 KECCAK256 SWAP1 DUP1 MLOAD SWAP2 PUSH1 0x80 DUP4 ADD PUSH8 0xFFFFFFFFFFFFFFFF SWAP5 DUP5 DUP3 LT DUP7 DUP4 GT OR PUSH2 0x4CB JUMPI PUSH1 0x80 DUP7 DUP7 PUSH1 0x60 DUP8 DUP8 DUP8 DUP3 MSTORE SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0x80 SHL SUB DUP2 AND SWAP5 DUP6 DUP6 MSTORE DUP2 DUP8 SHR AND SWAP4 DUP5 PUSH1 0x20 DUP3 ADD MSTORE PUSH1 0xFF DUP1 DUP4 PUSH1 0xC0 SHR AND ISZERO ISZERO SWAP3 DUP4 DUP6 DUP5 ADD MSTORE PUSH1 0xC8 SHR AND ISZERO ISZERO SWAP4 DUP5 SWAP2 ADD MSTORE DUP2 MLOAD SWAP5 DUP6 MSTORE PUSH1 0x20 DUP6 ADD MSTORE DUP4 ADD MSTORE PUSH1 0x60 DUP3 ADD MSTORE RETURN JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x41 DUP8 MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x26E JUMPI PUSH1 0x20 PUSH32 0x62E78CEA01BEE320CD4E420270B5EA74000D11B0C9F74754EBDBFC544B05A258 SWAP2 PUSH2 0x51C PUSH2 0xD05 JUMP JUMPDEST PUSH2 0x524 PUSH2 0xEF0 JUMP JUMPDEST PUSH1 0x1 PUSH1 0xFF NOT PUSH1 0x2 SLOAD AND OR PUSH1 0x2 SSTORE MLOAD CALLER DUP2 MSTORE LOG1 DUP1 RETURN JUMPDEST PUSH2 0xCE7 JUMP JUMPDEST POP SWAP2 CALLVALUE PUSH2 0x246 JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x246 JUMPI DUP1 CALLDATALOAD SWAP2 PUSH2 0x55C PUSH2 0xD05 JUMP JUMPDEST DUP3 ISZERO PUSH2 0x56A JUMPI POP POP PUSH1 0x7 SSTORE DUP1 RETURN JUMPDEST SWAP1 PUSH1 0x20 PUSH1 0x64 SWAP3 MLOAD SWAP2 PUSH3 0x461BCD PUSH1 0xE5 SHL DUP4 MSTORE DUP3 ADD MSTORE PUSH1 0x17 PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x496E76616C6964207369676E617475726520636F756E74000000000000000000 PUSH1 0x44 DUP3 ADD MSTORE REVERT JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI DUP1 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x26E JUMPI PUSH2 0x5C7 PUSH2 0xCCC JUMP JUMPDEST PUSH1 0x24 CALLDATALOAD SWAP1 PUSH2 0x5D3 PUSH2 0xD05 JUMP JUMPDEST PUSH10 0xD3C21BCECCEDA1000000 DUP3 GT PUSH2 0x71E JUMPI DUP3 MLOAD SWAP4 PUSH1 0x20 DUP6 ADD SWAP2 PUSH12 0xFFFFFFFFFFFFFFFFFFFFFFFF NOT SWAP1 PUSH1 0x60 SHL AND DUP3 MSTORE DUP3 PUSH1 0x34 DUP7 ADD MSTORE TIMESTAMP PUSH1 0x54 DUP7 ADD MSTORE CALLER PUSH1 0x60 SHL PUSH1 0x74 DUP7 ADD MSTORE PUSH1 0x68 DUP6 MSTORE PUSH1 0xA0 DUP6 ADD SWAP2 PUSH8 0xFFFFFFFFFFFFFFFF SWAP1 DUP7 DUP5 LT DUP3 DUP6 GT OR PUSH2 0x70B JUMPI DUP4 DUP7 MSTORE DUP7 MLOAD SWAP1 KECCAK256 SWAP6 PUSH2 0x100 DUP2 ADD DUP5 DUP2 LT DUP4 DUP3 GT OR PUSH2 0x6F8 JUMPI DUP7 MSTORE PUSH1 0x1 PUSH1 0x1 PUSH1 0x80 SHL SUB DUP1 DUP7 AND DUP6 MSTORE TIMESTAMP DUP4 AND PUSH1 0xC0 DUP4 ADD SWAP1 DUP2 MSTORE PUSH1 0xE0 SWAP1 SWAP3 ADD DUP5 DUP2 MSTORE DUP9 DUP6 MSTORE PUSH1 0x5 PUSH1 0x20 SWAP1 DUP2 MSTORE SWAP5 DUP9 SWAP1 KECCAK256 SWAP6 MLOAD DUP7 SLOAD SWAP4 MLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xC0 SHL SUB NOT SWAP1 SWAP5 AND SWAP3 AND SWAP2 SWAP1 SWAP2 OR SWAP2 SWAP1 SWAP3 AND PUSH1 0x80 SHL PUSH8 0xFFFFFFFFFFFFFFFF PUSH1 0x80 SHL AND OR DUP4 SSTORE SWAP1 SWAP6 POP DUP5 SWAP3 PUSH32 0x95F1700A944FD9E7555649BE76088141ECC47F9EB270E4321E235C7E9F78394F SWAP3 DUP8 SWAP3 MLOAD DUP2 SLOAD PUSH1 0xFF PUSH1 0xC0 SHL NOT AND SWAP1 ISZERO ISZERO PUSH1 0xC0 SHL PUSH1 0xFF PUSH1 0xC0 SHL AND OR SWAP1 SSTORE DUP5 MLOAD SWAP1 DUP2 MSTORE LOG2 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL DUP5 MSTORE PUSH1 0x41 DUP10 MSTORE PUSH1 0x24 DUP5 REVERT JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL DUP4 MSTORE PUSH1 0x41 DUP9 MSTORE PUSH1 0x24 DUP4 REVERT JUMPDEST DUP3 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 DUP2 DUP8 ADD MSTORE PUSH1 0x16 PUSH1 0x24 DUP3 ADD MSTORE PUSH22 0x416D6F756E742065786365656473206D6178696D756D PUSH1 0x50 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST POP CALLVALUE PUSH2 0x1BC JUMPI DUP1 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x1BC JUMPI PUSH2 0x773 PUSH2 0xD05 JUMP JUMPDEST DUP1 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB NOT DUP2 AND DUP3 SSTORE DUP2 SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND PUSH32 0x8BE0079C531659141344CD1FD0A4F28419497F9722A3DAAFE3B4186F6B6457E0 DUP3 DUP1 LOG3 DUP1 RETURN JUMPDEST POP CALLVALUE PUSH2 0x1BC JUMPI DUP1 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x1BC JUMPI PUSH2 0x7CD PUSH2 0xEF0 JUMP JUMPDEST PUSH2 0x7D5 PUSH2 0xD31 JUMP JUMPDEST DUP1 RETURN JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x26E JUMPI PUSH1 0x20 SWAP1 PUSH1 0xFF PUSH1 0x2 SLOAD AND SWAP1 MLOAD SWAP1 ISZERO ISZERO DUP2 MSTORE RETURN JUMPDEST POP SWAP2 CALLVALUE PUSH2 0x246 JUMPI DUP3 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x246 JUMPI PUSH2 0x816 PUSH2 0xD05 JUMP JUMPDEST PUSH1 0x2 SLOAD SWAP1 PUSH1 0xFF DUP3 AND ISZERO PUSH2 0x858 JUMPI POP PUSH1 0xFF NOT AND PUSH1 0x2 SSTORE MLOAD CALLER DUP2 MSTORE PUSH32 0x5DB9EE0A495BF2E6FF9C91A7834C1BA4FDD244A5E8AA4E537BD38AEAE4B073AA SWAP1 PUSH1 0x20 SWAP1 LOG1 DUP1 RETURN JUMPDEST DUP3 MLOAD PUSH4 0x8DFC202B PUSH1 0xE0 SHL DUP2 MSTORE REVERT JUMPDEST POP SWAP2 CALLVALUE PUSH2 0x246 JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x246 JUMPI DUP1 CALLDATALOAD SWAP1 PUSH1 0x24 CALLDATALOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP2 DUP2 AND SWAP2 DUP3 SWAP1 SUB PUSH2 0xB99 JUMPI PUSH1 0x2 PUSH1 0x1 SLOAD EQ PUSH2 0xB89 JUMPI PUSH1 0x2 PUSH1 0x1 SSTORE CALLER DUP7 MSTORE PUSH1 0x20 SWAP3 PUSH1 0x6 DUP5 MSTORE PUSH1 0xFF DUP7 DUP9 KECCAK256 SLOAD AND ISZERO PUSH2 0xB4E JUMPI DUP5 DUP8 MSTORE PUSH1 0x5 DUP5 MSTORE DUP6 DUP8 KECCAK256 SWAP3 DUP4 SLOAD SWAP3 PUSH1 0xFF DUP5 PUSH1 0xC0 SHR AND PUSH2 0xB18 JUMPI PUSH8 0xFFFFFFFFFFFFFFFF DUP5 PUSH1 0x80 SHR AND PUSH3 0x93A80 DUP2 ADD DUP1 SWAP2 GT PUSH2 0xB05 JUMPI TIMESTAMP LT PUSH2 0xACA JUMPI PUSH1 0xFF PUSH1 0xC0 SHL NOT DUP5 AND PUSH1 0x1 PUSH1 0xC0 SHL OR DUP6 SSTORE PUSH32 0x0 AND SWAP3 DUP6 SWAP3 SWAP2 SWAP1 DUP10 SWAP1 DUP6 DUP4 SUB PUSH2 0xA12 JUMPI POP POP DUP5 SLOAD DUP9 MLOAD PUSH4 0xA9059CBB PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB PUSH32 0x0 AND SWAP4 DUP2 ADD SWAP4 DUP5 MSTORE PUSH1 0x1 PUSH1 0x1 PUSH1 0x80 SHL SUB SWAP1 SWAP2 AND PUSH1 0x20 DUP5 ADD MSTORE SWAP4 DUP5 SWAP3 DUP4 SWAP1 SUB PUSH1 0x40 ADD SWAP2 POP DUP3 SWAP1 DUP11 SWAP1 GAS CALL DUP1 ISZERO PUSH2 0xA08 JUMPI SWAP2 PUSH1 0x1 PUSH1 0x1 PUSH1 0x80 SHL SUB SWAP2 PUSH32 0x52DD3CCDDD493D9FB871D4F3AF402C398E59AC2E679C482008B36D4E84E89A1E SWAP6 SWAP7 SWAP4 PUSH2 0x9DB JUMPI JUMPDEST POP JUMPDEST SLOAD AND SWAP1 MLOAD SWAP1 DUP2 MSTORE LOG2 PUSH1 0x1 DUP1 SSTORE DUP1 RETURN JUMPDEST PUSH2 0x9FA SWAP1 DUP6 RETURNDATASIZE DUP8 GT PUSH2 0xA01 JUMPI JUMPDEST PUSH2 0x9F2 DUP2 DUP4 PUSH2 0xF0E JUMP JUMPDEST DUP2 ADD SWAP1 PUSH2 0x105D JUMP JUMPDEST POP CODESIZE PUSH2 0x9CA JUMP JUMPDEST POP RETURNDATASIZE PUSH2 0x9E8 JUMP JUMPDEST DUP6 MLOAD RETURNDATASIZE DUP9 DUP3 RETURNDATACOPY RETURNDATASIZE SWAP1 REVERT JUMPDEST DUP10 MLOAD PUSH4 0xA9059CBB PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB PUSH32 0x0 AND SWAP5 DUP2 ADD SWAP5 DUP6 MSTORE PUSH1 0x1 PUSH1 0x1 PUSH1 0x80 SHL SUB SWAP1 SWAP2 AND PUSH1 0x20 DUP6 ADD MSTORE SWAP5 POP DUP5 SWAP3 DUP4 SWAP2 SWAP1 DUP3 SWAP1 PUSH1 0x40 ADD SUB SWAP3 GAS CALL DUP1 ISZERO PUSH2 0xA08 JUMPI SWAP2 PUSH1 0x1 PUSH1 0x1 PUSH1 0x80 SHL SUB SWAP2 PUSH32 0x52DD3CCDDD493D9FB871D4F3AF402C398E59AC2E679C482008B36D4E84E89A1E SWAP6 SWAP7 SWAP4 PUSH2 0xAAD JUMPI JUMPDEST POP PUSH2 0x9CC JUMP JUMPDEST PUSH2 0xAC3 SWAP1 DUP6 RETURNDATASIZE DUP8 GT PUSH2 0xA01 JUMPI PUSH2 0x9F2 DUP2 DUP4 PUSH2 0xF0E JUMP JUMPDEST POP CODESIZE PUSH2 0xAA7 JUMP JUMPDEST DUP8 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE DUP1 DUP5 ADD DUP8 SWAP1 MSTORE PUSH1 0x15 PUSH1 0x24 DUP3 ADD MSTORE PUSH21 0x151A5B59481B1BD8DAC81B9BDD08195E1C1A5C9959 PUSH1 0x5A SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL DUP11 MSTORE PUSH1 0x11 DUP5 MSTORE PUSH1 0x24 DUP11 REVERT JUMPDEST DUP8 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE DUP1 DUP5 ADD DUP8 SWAP1 MSTORE PUSH1 0x10 PUSH1 0x24 DUP3 ADD MSTORE PUSH16 0x105B1C9958591E48195E1958DD5D1959 PUSH1 0x82 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST DUP6 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE SWAP1 DUP2 ADD DUP5 SWAP1 MSTORE PUSH1 0x15 PUSH1 0x24 DUP3 ADD MSTORE PUSH21 0x2737BA1030BABA3437B934BD32B21039B4B3B732B9 PUSH1 0x59 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST DUP5 MLOAD PUSH4 0x3EE5AEB5 PUSH1 0xE0 SHL DUP2 MSTORE DUP4 SWAP1 REVERT JUMPDEST DUP6 DUP1 REVERT JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x26E JUMPI MLOAD PUSH32 0x0 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND DUP2 MSTORE PUSH1 0x20 SWAP1 RETURN JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x26E JUMPI MLOAD PUSH32 0x0 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND DUP2 MSTORE PUSH1 0x20 SWAP1 RETURN JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI DUP1 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x26E JUMPI PUSH2 0xC3F PUSH2 0xCCC JUMP JUMPDEST SWAP1 PUSH1 0x24 CALLDATALOAD SWAP2 DUP3 ISZERO ISZERO DUP1 SWAP4 SUB PUSH2 0xC77 JUMPI PUSH2 0xC56 PUSH2 0xD05 JUMP JUMPDEST PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB AND DUP4 MSTORE PUSH1 0x6 PUSH1 0x20 MSTORE DUP3 KECCAK256 SWAP1 PUSH1 0xFF DUP1 NOT DUP4 SLOAD AND SWAP2 AND OR SWAP1 SSTORE DUP1 RETURN JUMPDEST DUP4 DUP1 REVERT JUMPDEST SWAP1 POP DUP3 DUP5 CALLVALUE PUSH2 0xC77 JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0xC77 JUMPI DUP2 PUSH1 0x60 SWAP5 PUSH1 0xFF SWAP3 CALLDATALOAD DUP2 MSTORE PUSH1 0x5 PUSH1 0x20 MSTORE KECCAK256 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0x80 SHL SUB DUP2 AND DUP5 MSTORE PUSH8 0xFFFFFFFFFFFFFFFF DUP2 PUSH1 0x80 SHR AND PUSH1 0x20 DUP6 ADD MSTORE PUSH1 0xC0 SHR AND ISZERO ISZERO SWAP1 DUP3 ADD MSTORE RETURN JUMPDEST PUSH1 0x4 CALLDATALOAD SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP3 AND DUP3 SUB PUSH2 0xCE2 JUMPI JUMP JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST CALLVALUE PUSH2 0xCE2 JUMPI PUSH1 0x0 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0xCE2 JUMPI PUSH1 0x20 PUSH1 0x40 MLOAD PUSH3 0x93A80 DUP2 MSTORE RETURN JUMPDEST PUSH1 0x0 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND CALLER SUB PUSH2 0xD19 JUMPI JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH4 0x118CDAA7 PUSH1 0xE0 SHL DUP2 MSTORE CALLER PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST PUSH1 0x3 SLOAD PUSH1 0x1F DUP2 GT PUSH2 0xEED JUMPI PUSH1 0x0 DUP2 DUP2 MSTORE PUSH1 0x4 PUSH1 0x20 MSTORE PUSH1 0x40 DUP2 KECCAK256 DUP1 SLOAD PUSH1 0xFF DUP2 PUSH1 0xC8 SHR AND PUSH2 0xEE7 JUMPI DUP4 SWAP1 PUSH2 0xD61 PUSH2 0xF46 JUMP JUMPDEST SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0x80 SHL SUB DUP2 AND DUP3 LT DUP1 ISZERO DUP1 PUSH2 0xED9 JUMPI JUMPDEST ISZERO PUSH2 0xEA0 JUMPI POP POP DUP3 SLOAD PUSH9 0xFFFFFFFFFFFFFFFFFF PUSH1 0x80 SHL NOT AND TIMESTAMP PUSH1 0x80 SHL PUSH8 0xFFFFFFFFFFFFFFFF PUSH1 0x80 SHL AND OR PUSH1 0x1 PUSH1 0xC0 SHL OR DUP4 SSTORE PUSH32 0xA8303D852039DD03DA732CA0626A63B998F0ADD380069633C8F94C2214A3EC01 SWAP1 PUSH1 0x20 SWAP1 PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE LOG2 JUMPDEST SLOAD PUSH1 0xFF DUP2 PUSH1 0xC0 SHR AND SWAP1 DUP2 PUSH2 0xE67 JUMPI JUMPDEST POP PUSH2 0xDED JUMPI POP POP JUMP JUMPDEST DUP2 DUP2 MSTORE PUSH1 0x4 PUSH1 0x20 SWAP1 DUP2 MSTORE PUSH1 0x40 DUP1 DUP4 KECCAK256 DUP1 SLOAD PUSH1 0xFF PUSH1 0xC8 SHL NOT AND PUSH1 0x1 PUSH1 0xC8 SHL OR SWAP1 SSTORE MLOAD TIMESTAMP DUP2 MSTORE DUP4 SWAP2 PUSH32 0x21A31473AC3B93C8BFDFE4F59540208CF835CD32697AFE67593091C7E15D949C SWAP2 LOG2 PUSH1 0x1F DUP3 LT PUSH2 0xE42 JUMPI POP POP JUMP JUMPDEST PUSH1 0x1 DUP3 ADD DUP1 SWAP3 GT PUSH2 0xE53 JUMPI POP PUSH1 0x3 SSTORE JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x11 PUSH1 0x4 MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST PUSH8 0xFFFFFFFFFFFFFFFF SWAP2 POP PUSH1 0x80 SHR AND PUSH3 0x93A80 DUP2 ADD DUP1 SWAP2 GT PUSH2 0xE8C JUMPI TIMESTAMP LT ISZERO CODESIZE PUSH2 0xDE4 JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL DUP3 MSTORE PUSH1 0x11 PUSH1 0x4 MSTORE PUSH1 0x24 DUP3 REVERT JUMPDEST SWAP2 POP SWAP2 POP DUP1 PUSH2 0xECC JUMPI JUMPDEST PUSH2 0xEB5 JUMPI JUMPDEST POP PUSH2 0xDD5 JUMP JUMPDEST PUSH9 0xFFFFFFFFFFFFFFFFFF PUSH1 0x80 SHL NOT AND DUP2 SSTORE CODESIZE PUSH2 0xEAF JUMP JUMPDEST POP PUSH1 0xFF DUP2 PUSH1 0xC0 SHR AND PUSH2 0xEAA JUMP JUMPDEST POP PUSH1 0xFF DUP3 PUSH1 0xC0 SHR AND ISZERO PUSH2 0xD76 JUMP JUMPDEST POP POP POP POP JUMP JUMPDEST POP JUMP JUMPDEST PUSH1 0xFF PUSH1 0x2 SLOAD AND PUSH2 0xEFC JUMPI JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH4 0xD93C0665 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x4 SWAP1 REVERT JUMPDEST SWAP1 PUSH1 0x1F DUP1 NOT SWAP2 ADD AND DUP2 ADD SWAP1 DUP2 LT PUSH8 0xFFFFFFFFFFFFFFFF DUP3 GT OR PUSH2 0xF30 JUMPI PUSH1 0x40 MSTORE JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x41 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH1 0x40 DUP1 MLOAD PUSH4 0x8E15F473 PUSH1 0xE0 SHL PUSH1 0x20 DUP1 DUP4 ADD SWAP2 DUP3 MSTORE PUSH1 0x4 DUP4 MSTORE SWAP3 PUSH8 0xFFFFFFFFFFFFFFFF SWAP3 SWAP1 DUP2 ADD DUP4 DUP2 GT DUP3 DUP3 LT OR PUSH2 0xF30 JUMPI PUSH1 0x40 MSTORE PUSH1 0x0 DUP1 SWAP3 DUP2 SWAP3 MLOAD SWAP1 PUSH32 0x0 GAS STATICCALL RETURNDATASIZE ISZERO PUSH2 0x1054 JUMPI RETURNDATASIZE SWAP3 DUP4 GT PUSH2 0x1040 JUMPI PUSH1 0x40 MLOAD SWAP3 PUSH2 0xFCA PUSH1 0x1F DUP3 ADD PUSH1 0x1F NOT AND DUP7 ADD DUP6 PUSH2 0xF0E JUMP JUMPDEST DUP4 MSTORE RETURNDATASIZE DUP3 DUP6 DUP6 ADD RETURNDATACOPY JUMPDEST DUP1 PUSH2 0x1035 JUMPI JUMPDEST ISZERO PUSH2 0xFF0 JUMPI DUP3 DUP3 DUP1 MLOAD DUP2 ADD SUB SLT PUSH2 0x1BC JUMPI POP ADD MLOAD SWAP1 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x4 DUP2 ADD DUP5 SWAP1 MSTORE PUSH1 0x18 PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x5072696365206F7261636C652063616C6C206661696C65640000000000000000 PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST POP DUP3 DUP3 MLOAD LT ISZERO PUSH2 0xFD9 JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL DUP3 MSTORE PUSH1 0x41 PUSH1 0x4 MSTORE PUSH1 0x24 DUP3 REVERT JUMPDEST PUSH1 0x60 SWAP3 POP PUSH2 0xFD3 JUMP JUMPDEST SWAP1 DUP2 PUSH1 0x20 SWAP2 SUB SLT PUSH2 0xCE2 JUMPI MLOAD DUP1 ISZERO ISZERO DUP2 SUB PUSH2 0xCE2 JUMPI SWAP1 JUMP JUMPDEST PUSH1 0x0 NOT DUP2 EQ PUSH2 0x1084 JUMPI PUSH1 0x1 ADD SWAP1 JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x11 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT INVALID LOG2 PUSH5 0x6970667358 0x22 SLT KECCAK256 SWAP12 0xB2 CREATE 0xDB RETURNDATACOPY PUSH6 0xE5A2EE93C01C SWAP16 DUP14 0xCD INVALID SWAP8 0xAA BYTE JUMPDEST 0xDB 0xAD XOR 0x26 CREATE 0xCF 0xBA PUSH20 0xB18A99EB64736F6C634300081400330000000000 ", "sourceMap": "456:8181:5:-:0;;;;;;;;;;;;;-1:-1:-1;;456:8181:5;;;;-1:-1:-1;;;;;456:8181:5;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;:::i;:::-;;;;;;;;:::i;:::-;;;;:::i;:::-;2278:10;;1273:26:0;1269:95;;-1:-1:-1;456:8181:5;;2278:10;-1:-1:-1;;;;;;456:8181:5;;;;;;;;;-1:-1:-1;;;;;456:8181:5;;;;;2278:10;456:8181;;;3052:40:0;;-1:-1:-1;3052:40:0;456:8181:5;;;;1022:1;456:8181;;1766:1;456:8181;;2308:24;;;456:8181;;;;;;2376:26;456:8181;;;;;2447:28;456:8181;;;;2520:30;456:8181;;;2598:30;456:8181;2638:26;2674:30;;2714:34;;2278:10;-1:-1:-1;456:8181:5;;;2767:16;456:8181;;;;;;;;;-1:-1:-1;;456:8181:5;;;;;;;;;-1:-1:-1;;;;;456:8181:5;;;;;;;;;;3048:6;456:8181;;3057:6;456:8181;3026:303;;456:8181;3065:6;456:8181;3026:303;;456:8181;3073:6;456:8181;3026:303;;456:8181;3081:6;456:8181;3026:303;;456:8181;3089:6;456:8181;3026:303;;456:8181;3097:6;2674:30;3026:303;;456:8181;3105:6;2714:34;3026:303;;456:8181;3113:6;3026:303;;;456:8181;3121:6;3026:303;;;456:8181;3141:6;3026:303;;;456:8181;3149:6;3026:303;;;456:8181;3157:6;3026:303;;;456:8181;3165:6;3026:303;;;456:8181;3173:6;3026:303;;;456:8181;3181:6;3026:303;;;456:8181;3189:6;3026:303;;;456:8181;3197:6;3026:303;;;456:8181;3205:6;3026:303;;;456:8181;3213:6;3026:303;;;456:8181;3233:6;3026:303;;;456:8181;3241:6;3026:303;;;456:8181;3249:6;3026:303;;;456:8181;3257:6;3026:303;;;456:8181;3265:6;3026:303;;;456:8181;3273:6;3026:303;;;456:8181;3281:6;3026:303;;;456:8181;3289:6;3026:303;;;456:8181;3297:6;3026:303;;;456:8181;3305:6;3026:303;;;456:8181;3313:6;3026:303;;;456:8181;-1:-1:-1;3368:16:5;456:8181;3368:16;;;;456:8181;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2674:30;456:8181;;;;;;;;;;;;;;;2714:34;456:8181;;;;;;3386:3;456:8181;592:2;;;;;;456:8181;;;-1:-1:-1;;;;;592:2:5;;456:8181;;-1:-1:-1;;;;;456:8181:5;;;;;;;;;;592:2;;-1:-1:-1;456:8181:5;3421:183;;592:2;-1:-1:-1;456:8181:5;3421:183;;592:2;-1:-1:-1;456:8181:5;3421:183;;592:2;456:8181;592:2;;;;;;456:8181;592:2;;-1:-1:-1;592:2:5;3405:6;456:8181;592:2;456:8181;-1:-1:-1;592:2:5;;456:8181;592:2;;;;;;;;;;;456:8181;592:2;;456:8181;592:2;;;456:8181;3421:183;;592:2;456:8181;592:2;;;456:8181;592:2;;456:8181;3421:183;;592:2;456:8181;;2674:30;592:2;;;456:8181;;592:2;;3421:183;;592:2;456:8181;;592:2;;;;456:8181;592:2;;;;;;;;;;;;456:8181;;;;;;;;3353:13;;456:8181;;;;-1:-1:-1;456:8181:5;;3405:6;456:8181;;-1:-1:-1;456:8181:5;;;;;-1:-1:-1;456:8181:5;;3405:6;456:8181;;-1:-1:-1;456:8181:5;;;;-1:-1:-1;;;456:8181:5;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;456:8181:5;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;456:8181:5;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;456:8181:5;;;;;;;;;;;;;;;;;;;;1269:95:0;456:8181:5;;-1:-1:-1;;;1322:31:0;;-1:-1:-1;1322:31:0;;;456:8181:5;;;1322:31:0;456:8181:5;-1:-1:-1;456:8181:5;;;;;-1:-1:-1;;;;;456:8181:5;;;;;;:::o"}, "deployedBytecode": {"functionDebugData": {"abi_decode_address": {"entryPoint": 3276, "id": null, "parameterSlots": 0, "returnSlots": 1}, "abi_decode_bool_fromMemory": {"entryPoint": 4189, "id": null, "parameterSlots": 2, "returnSlots": 1}, "abi_encode_address_uint128": {"entryPoint": null, "id": null, "parameterSlots": 3, "returnSlots": 1}, "external_fun_PRICE_MAINTAIN_DURATION": {"entryPoint": 3303, "id": null, "parameterSlots": 0, "returnSlots": 0}, "finalize_allocation": {"entryPoint": 3854, "id": null, "parameterSlots": 2, "returnSlots": 0}, "fun_checkOwner": {"entryPoint": 3333, "id": 84, "parameterSlots": 0, "returnSlots": 0}, "fun_checkPriceCondition_inner": {"entryPoint": 3377, "id": null, "parameterSlots": 0, "returnSlots": 0}, "fun_getCurrentPrice": {"entryPoint": 3910, "id": 867, "parameterSlots": 0, "returnSlots": 1}, "fun_requireNotPaused": {"entryPoint": 3824, "id": 317, "parameterSlots": 0, "returnSlots": 0}, "increment_uint256": {"entryPoint": 4213, "id": null, "parameterSlots": 1, "returnSlots": 1}, "update_storage_value_offsett_uint64_to_uint64": {"entryPoint": null, "id": null, "parameterSlots": 2, "returnSlots": 0}}, "generatedSources": [], "immutableReferences": {"464": [{"length": 32, "start": 2306}, {"length": 32, "start": 3062}], "466": [{"length": 32, "start": 2994}, {"length": 32, "start": 3972}], "468": [{"length": 32, "start": 842}, {"length": 32, "start": 2378}, {"length": 32, "start": 2600}], "470": [{"length": 32, "start": 774}]}, "linkReferences": {}, "object": "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", "opcodes": "PUSH1 0x80 PUSH1 0x40 DUP2 DUP2 MSTORE PUSH1 0x4 SWAP2 DUP3 CALLDATASIZE LT ISZERO PUSH2 0x16 JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST PUSH1 0x0 SWAP1 DUP2 CALLDATALOAD PUSH1 0xE0 SHR SWAP1 DUP2 PUSH4 0x557AC41 EQ PUSH2 0xC7B JUMPI POP DUP1 PUSH4 0x200DFD5B EQ PUSH2 0xC25 JUMPI DUP1 PUSH4 0x25DBEC9D EQ PUSH2 0xBE1 JUMPI DUP1 PUSH4 0x2630C12F EQ PUSH2 0xB9D JUMPI DUP1 PUSH4 0x34F4ACA1 EQ PUSH2 0x866 JUMPI DUP1 PUSH4 0x3F4BA83A EQ PUSH2 0x7FC JUMPI DUP1 PUSH4 0x540797A5 EQ PUSH2 0x539 JUMPI DUP1 PUSH4 0x5C975ABB EQ PUSH2 0x7D8 JUMPI DUP1 PUSH4 0x687404B2 EQ PUSH2 0x7B4 JUMPI DUP1 PUSH4 0x715018A6 EQ PUSH2 0x75A JUMPI DUP1 PUSH4 0x7C7F4CE5 EQ PUSH2 0x5AD JUMPI DUP1 PUSH4 0x7D2B9CC0 EQ PUSH2 0x53E JUMPI DUP1 PUSH4 0x82944E2D EQ PUSH2 0x539 JUMPI DUP1 PUSH4 0x8456CB59 EQ PUSH2 0x4DE JUMPI DUP1 PUSH4 0x88C3FFB0 EQ PUSH2 0x43C JUMPI DUP1 PUSH4 0x8A19C8BC EQ PUSH2 0x41D JUMPI DUP1 PUSH4 0x8C65C81F EQ PUSH2 0x3C0 JUMPI DUP1 PUSH4 0x8D068043 EQ PUSH2 0x3A1 JUMPI DUP1 PUSH4 0x8DA5CB5B EQ PUSH2 0x379 JUMPI DUP1 PUSH4 0xBEB08AB9 EQ PUSH2 0x335 JUMPI DUP1 PUSH4 0xC7574839 EQ PUSH2 0x2F1 JUMPI DUP1 PUSH4 0xCE8DC388 EQ PUSH2 0x2D5 JUMPI DUP1 PUSH4 0xCF9D226E EQ PUSH2 0x2B0 JUMPI DUP1 PUSH4 0xD0F694CB EQ PUSH2 0x272 JUMPI DUP1 PUSH4 0xEB91D37E EQ PUSH2 0x24A JUMPI DUP1 PUSH4 0xF2FDE38B EQ PUSH2 0x1BF JUMPI PUSH4 0xFF5F1E91 EQ PUSH2 0x14D JUMPI PUSH1 0x0 DUP1 REVERT JUMPDEST CALLVALUE PUSH2 0x1BC JUMPI DUP1 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x1BC JUMPI DUP1 PUSH1 0x1 JUMPDEST PUSH1 0x1F DUP2 GT ISZERO PUSH2 0x183 JUMPI PUSH1 0x60 DUP5 DUP5 PUSH1 0x3 SLOAD SWAP2 DUP1 MLOAD SWAP3 PUSH1 0x1F DUP5 MSTORE PUSH1 0x20 DUP5 ADD MSTORE DUP3 ADD MSTORE RETURN JUMPDEST DUP1 DUP3 MSTORE DUP5 PUSH1 0x20 MSTORE PUSH1 0xFF DUP5 DUP4 KECCAK256 SLOAD PUSH1 0xC8 SHR AND PUSH2 0x1A7 JUMPI JUMPDEST PUSH2 0x1A2 SWAP1 PUSH2 0x1075 JUMP JUMPDEST PUSH2 0x161 JUMP JUMPDEST SWAP2 PUSH2 0x1B4 PUSH2 0x1A2 SWAP2 PUSH2 0x1075 JUMP JUMPDEST SWAP3 SWAP1 POP PUSH2 0x199 JUMP JUMPDEST DUP1 REVERT JUMPDEST POP SWAP2 CALLVALUE PUSH2 0x246 JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x246 JUMPI PUSH2 0x1DA PUSH2 0xCCC JUMP JUMPDEST SWAP1 PUSH2 0x1E3 PUSH2 0xD05 JUMP JUMPDEST PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP2 DUP3 AND SWAP3 DUP4 ISZERO PUSH2 0x230 JUMPI POP POP DUP3 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB NOT DUP2 AND DUP4 OR DUP5 SSTORE AND PUSH32 0x8BE0079C531659141344CD1FD0A4F28419497F9722A3DAAFE3B4186F6B6457E0 DUP4 DUP1 LOG3 DUP1 RETURN JUMPDEST MLOAD PUSH4 0x1E4FBDF7 PUSH1 0xE0 SHL DUP2 MSTORE SWAP1 DUP2 ADD DUP5 SWAP1 MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST DUP3 DUP1 REVERT JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x26E JUMPI PUSH1 0x20 SWAP1 PUSH2 0x267 PUSH2 0xF46 JUMP JUMPDEST SWAP1 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST POP DUP1 REVERT JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x26E JUMPI PUSH1 0x20 SWAP2 PUSH1 0xFF SWAP1 DUP3 SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB PUSH2 0x29D PUSH2 0xCCC JUMP JUMPDEST AND DUP2 MSTORE PUSH1 0x6 DUP6 MSTORE KECCAK256 SLOAD AND SWAP1 MLOAD SWAP1 ISZERO ISZERO DUP2 MSTORE RETURN JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x26E JUMPI PUSH1 0x20 SWAP1 MLOAD PUSH10 0xD3C21BCECCEDA1000000 DUP2 MSTORE RETURN JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x26E JUMPI PUSH1 0x20 SWAP1 MLOAD PUSH1 0x1F DUP2 MSTORE RETURN JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x26E JUMPI MLOAD PUSH32 0x0 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND DUP2 MSTORE PUSH1 0x20 SWAP1 RETURN JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x26E JUMPI MLOAD PUSH32 0x0 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND DUP2 MSTORE PUSH1 0x20 SWAP1 RETURN JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x26E JUMPI SWAP1 SLOAD SWAP1 MLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB SWAP1 SWAP2 AND DUP2 MSTORE PUSH1 0x20 SWAP1 RETURN JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x26E JUMPI PUSH1 0x20 SWAP1 PUSH1 0x7 SLOAD SWAP1 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST POP SWAP2 CALLVALUE PUSH2 0x246 JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x246 JUMPI DUP2 PUSH1 0x80 SWAP4 DUP3 PUSH1 0xFF SWAP4 CALLDATALOAD DUP3 MSTORE PUSH1 0x20 MSTORE KECCAK256 SLOAD DUP3 MLOAD SWAP3 PUSH1 0x1 PUSH1 0x1 PUSH1 0x80 SHL SUB DUP3 AND DUP5 MSTORE PUSH8 0xFFFFFFFFFFFFFFFF DUP3 DUP7 SHR AND PUSH1 0x20 DUP6 ADD MSTORE DUP3 DUP3 PUSH1 0xC0 SHR AND ISZERO ISZERO SWAP1 DUP5 ADD MSTORE PUSH1 0xC8 SHR AND ISZERO ISZERO PUSH1 0x60 DUP3 ADD MSTORE RETURN JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x26E JUMPI PUSH1 0x20 SWAP1 PUSH1 0x3 SLOAD SWAP1 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x26E JUMPI DUP3 CALLDATALOAD DUP3 MSTORE DUP3 PUSH1 0x20 MSTORE DUP1 DUP3 KECCAK256 SWAP1 DUP1 MLOAD SWAP2 PUSH1 0x80 DUP4 ADD PUSH8 0xFFFFFFFFFFFFFFFF SWAP5 DUP5 DUP3 LT DUP7 DUP4 GT OR PUSH2 0x4CB JUMPI PUSH1 0x80 DUP7 DUP7 PUSH1 0x60 DUP8 DUP8 DUP8 DUP3 MSTORE SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0x80 SHL SUB DUP2 AND SWAP5 DUP6 DUP6 MSTORE DUP2 DUP8 SHR AND SWAP4 DUP5 PUSH1 0x20 DUP3 ADD MSTORE PUSH1 0xFF DUP1 DUP4 PUSH1 0xC0 SHR AND ISZERO ISZERO SWAP3 DUP4 DUP6 DUP5 ADD MSTORE PUSH1 0xC8 SHR AND ISZERO ISZERO SWAP4 DUP5 SWAP2 ADD MSTORE DUP2 MLOAD SWAP5 DUP6 MSTORE PUSH1 0x20 DUP6 ADD MSTORE DUP4 ADD MSTORE PUSH1 0x60 DUP3 ADD MSTORE RETURN JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x41 DUP8 MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x26E JUMPI PUSH1 0x20 PUSH32 0x62E78CEA01BEE320CD4E420270B5EA74000D11B0C9F74754EBDBFC544B05A258 SWAP2 PUSH2 0x51C PUSH2 0xD05 JUMP JUMPDEST PUSH2 0x524 PUSH2 0xEF0 JUMP JUMPDEST PUSH1 0x1 PUSH1 0xFF NOT PUSH1 0x2 SLOAD AND OR PUSH1 0x2 SSTORE MLOAD CALLER DUP2 MSTORE LOG1 DUP1 RETURN JUMPDEST PUSH2 0xCE7 JUMP JUMPDEST POP SWAP2 CALLVALUE PUSH2 0x246 JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0x246 JUMPI DUP1 CALLDATALOAD SWAP2 PUSH2 0x55C PUSH2 0xD05 JUMP JUMPDEST DUP3 ISZERO PUSH2 0x56A JUMPI POP POP PUSH1 0x7 SSTORE DUP1 RETURN JUMPDEST SWAP1 PUSH1 0x20 PUSH1 0x64 SWAP3 MLOAD SWAP2 PUSH3 0x461BCD PUSH1 0xE5 SHL DUP4 MSTORE DUP3 ADD MSTORE PUSH1 0x17 PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x496E76616C6964207369676E617475726520636F756E74000000000000000000 PUSH1 0x44 DUP3 ADD MSTORE REVERT JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI DUP1 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x26E JUMPI PUSH2 0x5C7 PUSH2 0xCCC JUMP JUMPDEST PUSH1 0x24 CALLDATALOAD SWAP1 PUSH2 0x5D3 PUSH2 0xD05 JUMP JUMPDEST PUSH10 0xD3C21BCECCEDA1000000 DUP3 GT PUSH2 0x71E JUMPI DUP3 MLOAD SWAP4 PUSH1 0x20 DUP6 ADD SWAP2 PUSH12 0xFFFFFFFFFFFFFFFFFFFFFFFF NOT SWAP1 PUSH1 0x60 SHL AND DUP3 MSTORE DUP3 PUSH1 0x34 DUP7 ADD MSTORE TIMESTAMP PUSH1 0x54 DUP7 ADD MSTORE CALLER PUSH1 0x60 SHL PUSH1 0x74 DUP7 ADD MSTORE PUSH1 0x68 DUP6 MSTORE PUSH1 0xA0 DUP6 ADD SWAP2 PUSH8 0xFFFFFFFFFFFFFFFF SWAP1 DUP7 DUP5 LT DUP3 DUP6 GT OR PUSH2 0x70B JUMPI DUP4 DUP7 MSTORE DUP7 MLOAD SWAP1 KECCAK256 SWAP6 PUSH2 0x100 DUP2 ADD DUP5 DUP2 LT DUP4 DUP3 GT OR PUSH2 0x6F8 JUMPI DUP7 MSTORE PUSH1 0x1 PUSH1 0x1 PUSH1 0x80 SHL SUB DUP1 DUP7 AND DUP6 MSTORE TIMESTAMP DUP4 AND PUSH1 0xC0 DUP4 ADD SWAP1 DUP2 MSTORE PUSH1 0xE0 SWAP1 SWAP3 ADD DUP5 DUP2 MSTORE DUP9 DUP6 MSTORE PUSH1 0x5 PUSH1 0x20 SWAP1 DUP2 MSTORE SWAP5 DUP9 SWAP1 KECCAK256 SWAP6 MLOAD DUP7 SLOAD SWAP4 MLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xC0 SHL SUB NOT SWAP1 SWAP5 AND SWAP3 AND SWAP2 SWAP1 SWAP2 OR SWAP2 SWAP1 SWAP3 AND PUSH1 0x80 SHL PUSH8 0xFFFFFFFFFFFFFFFF PUSH1 0x80 SHL AND OR DUP4 SSTORE SWAP1 SWAP6 POP DUP5 SWAP3 PUSH32 0x95F1700A944FD9E7555649BE76088141ECC47F9EB270E4321E235C7E9F78394F SWAP3 DUP8 SWAP3 MLOAD DUP2 SLOAD PUSH1 0xFF PUSH1 0xC0 SHL NOT AND SWAP1 ISZERO ISZERO PUSH1 0xC0 SHL PUSH1 0xFF PUSH1 0xC0 SHL AND OR SWAP1 SSTORE DUP5 MLOAD SWAP1 DUP2 MSTORE LOG2 MLOAD SWAP1 DUP2 MSTORE RETURN JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL DUP5 MSTORE PUSH1 0x41 DUP10 MSTORE PUSH1 0x24 DUP5 REVERT JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL DUP4 MSTORE PUSH1 0x41 DUP9 MSTORE PUSH1 0x24 DUP4 REVERT JUMPDEST DUP3 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x20 DUP2 DUP8 ADD MSTORE PUSH1 0x16 PUSH1 0x24 DUP3 ADD MSTORE PUSH22 0x416D6F756E742065786365656473206D6178696D756D PUSH1 0x50 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST POP CALLVALUE PUSH2 0x1BC JUMPI DUP1 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x1BC JUMPI PUSH2 0x773 PUSH2 0xD05 JUMP JUMPDEST DUP1 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB NOT DUP2 AND DUP3 SSTORE DUP2 SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND PUSH32 0x8BE0079C531659141344CD1FD0A4F28419497F9722A3DAAFE3B4186F6B6457E0 DUP3 DUP1 LOG3 DUP1 RETURN JUMPDEST POP CALLVALUE PUSH2 0x1BC JUMPI DUP1 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x1BC JUMPI PUSH2 0x7CD PUSH2 0xEF0 JUMP JUMPDEST PUSH2 0x7D5 PUSH2 0xD31 JUMP JUMPDEST DUP1 RETURN JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x26E JUMPI PUSH1 0x20 SWAP1 PUSH1 0xFF PUSH1 0x2 SLOAD AND SWAP1 MLOAD SWAP1 ISZERO ISZERO DUP2 MSTORE RETURN JUMPDEST POP SWAP2 CALLVALUE PUSH2 0x246 JUMPI DUP3 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x246 JUMPI PUSH2 0x816 PUSH2 0xD05 JUMP JUMPDEST PUSH1 0x2 SLOAD SWAP1 PUSH1 0xFF DUP3 AND ISZERO PUSH2 0x858 JUMPI POP PUSH1 0xFF NOT AND PUSH1 0x2 SSTORE MLOAD CALLER DUP2 MSTORE PUSH32 0x5DB9EE0A495BF2E6FF9C91A7834C1BA4FDD244A5E8AA4E537BD38AEAE4B073AA SWAP1 PUSH1 0x20 SWAP1 LOG1 DUP1 RETURN JUMPDEST DUP3 MLOAD PUSH4 0x8DFC202B PUSH1 0xE0 SHL DUP2 MSTORE REVERT JUMPDEST POP SWAP2 CALLVALUE PUSH2 0x246 JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x246 JUMPI DUP1 CALLDATALOAD SWAP1 PUSH1 0x24 CALLDATALOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP2 DUP2 AND SWAP2 DUP3 SWAP1 SUB PUSH2 0xB99 JUMPI PUSH1 0x2 PUSH1 0x1 SLOAD EQ PUSH2 0xB89 JUMPI PUSH1 0x2 PUSH1 0x1 SSTORE CALLER DUP7 MSTORE PUSH1 0x20 SWAP3 PUSH1 0x6 DUP5 MSTORE PUSH1 0xFF DUP7 DUP9 KECCAK256 SLOAD AND ISZERO PUSH2 0xB4E JUMPI DUP5 DUP8 MSTORE PUSH1 0x5 DUP5 MSTORE DUP6 DUP8 KECCAK256 SWAP3 DUP4 SLOAD SWAP3 PUSH1 0xFF DUP5 PUSH1 0xC0 SHR AND PUSH2 0xB18 JUMPI PUSH8 0xFFFFFFFFFFFFFFFF DUP5 PUSH1 0x80 SHR AND PUSH3 0x93A80 DUP2 ADD DUP1 SWAP2 GT PUSH2 0xB05 JUMPI TIMESTAMP LT PUSH2 0xACA JUMPI PUSH1 0xFF PUSH1 0xC0 SHL NOT DUP5 AND PUSH1 0x1 PUSH1 0xC0 SHL OR DUP6 SSTORE PUSH32 0x0 AND SWAP3 DUP6 SWAP3 SWAP2 SWAP1 DUP10 SWAP1 DUP6 DUP4 SUB PUSH2 0xA12 JUMPI POP POP DUP5 SLOAD DUP9 MLOAD PUSH4 0xA9059CBB PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB PUSH32 0x0 AND SWAP4 DUP2 ADD SWAP4 DUP5 MSTORE PUSH1 0x1 PUSH1 0x1 PUSH1 0x80 SHL SUB SWAP1 SWAP2 AND PUSH1 0x20 DUP5 ADD MSTORE SWAP4 DUP5 SWAP3 DUP4 SWAP1 SUB PUSH1 0x40 ADD SWAP2 POP DUP3 SWAP1 DUP11 SWAP1 GAS CALL DUP1 ISZERO PUSH2 0xA08 JUMPI SWAP2 PUSH1 0x1 PUSH1 0x1 PUSH1 0x80 SHL SUB SWAP2 PUSH32 0x52DD3CCDDD493D9FB871D4F3AF402C398E59AC2E679C482008B36D4E84E89A1E SWAP6 SWAP7 SWAP4 PUSH2 0x9DB JUMPI JUMPDEST POP JUMPDEST SLOAD AND SWAP1 MLOAD SWAP1 DUP2 MSTORE LOG2 PUSH1 0x1 DUP1 SSTORE DUP1 RETURN JUMPDEST PUSH2 0x9FA SWAP1 DUP6 RETURNDATASIZE DUP8 GT PUSH2 0xA01 JUMPI JUMPDEST PUSH2 0x9F2 DUP2 DUP4 PUSH2 0xF0E JUMP JUMPDEST DUP2 ADD SWAP1 PUSH2 0x105D JUMP JUMPDEST POP CODESIZE PUSH2 0x9CA JUMP JUMPDEST POP RETURNDATASIZE PUSH2 0x9E8 JUMP JUMPDEST DUP6 MLOAD RETURNDATASIZE DUP9 DUP3 RETURNDATACOPY RETURNDATASIZE SWAP1 REVERT JUMPDEST DUP10 MLOAD PUSH4 0xA9059CBB PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB PUSH32 0x0 AND SWAP5 DUP2 ADD SWAP5 DUP6 MSTORE PUSH1 0x1 PUSH1 0x1 PUSH1 0x80 SHL SUB SWAP1 SWAP2 AND PUSH1 0x20 DUP6 ADD MSTORE SWAP5 POP DUP5 SWAP3 DUP4 SWAP2 SWAP1 DUP3 SWAP1 PUSH1 0x40 ADD SUB SWAP3 GAS CALL DUP1 ISZERO PUSH2 0xA08 JUMPI SWAP2 PUSH1 0x1 PUSH1 0x1 PUSH1 0x80 SHL SUB SWAP2 PUSH32 0x52DD3CCDDD493D9FB871D4F3AF402C398E59AC2E679C482008B36D4E84E89A1E SWAP6 SWAP7 SWAP4 PUSH2 0xAAD JUMPI JUMPDEST POP PUSH2 0x9CC JUMP JUMPDEST PUSH2 0xAC3 SWAP1 DUP6 RETURNDATASIZE DUP8 GT PUSH2 0xA01 JUMPI PUSH2 0x9F2 DUP2 DUP4 PUSH2 0xF0E JUMP JUMPDEST POP CODESIZE PUSH2 0xAA7 JUMP JUMPDEST DUP8 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE DUP1 DUP5 ADD DUP8 SWAP1 MSTORE PUSH1 0x15 PUSH1 0x24 DUP3 ADD MSTORE PUSH21 0x151A5B59481B1BD8DAC81B9BDD08195E1C1A5C9959 PUSH1 0x5A SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL DUP11 MSTORE PUSH1 0x11 DUP5 MSTORE PUSH1 0x24 DUP11 REVERT JUMPDEST DUP8 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE DUP1 DUP5 ADD DUP8 SWAP1 MSTORE PUSH1 0x10 PUSH1 0x24 DUP3 ADD MSTORE PUSH16 0x105B1C9958591E48195E1958DD5D1959 PUSH1 0x82 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST DUP6 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE SWAP1 DUP2 ADD DUP5 SWAP1 MSTORE PUSH1 0x15 PUSH1 0x24 DUP3 ADD MSTORE PUSH21 0x2737BA1030BABA3437B934BD32B21039B4B3B732B9 PUSH1 0x59 SHL PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST DUP5 MLOAD PUSH4 0x3EE5AEB5 PUSH1 0xE0 SHL DUP2 MSTORE DUP4 SWAP1 REVERT JUMPDEST DUP6 DUP1 REVERT JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x26E JUMPI MLOAD PUSH32 0x0 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND DUP2 MSTORE PUSH1 0x20 SWAP1 RETURN JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI DUP2 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x26E JUMPI MLOAD PUSH32 0x0 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND DUP2 MSTORE PUSH1 0x20 SWAP1 RETURN JUMPDEST POP SWAP1 CALLVALUE PUSH2 0x26E JUMPI DUP1 PUSH1 0x3 NOT CALLDATASIZE ADD SLT PUSH2 0x26E JUMPI PUSH2 0xC3F PUSH2 0xCCC JUMP JUMPDEST SWAP1 PUSH1 0x24 CALLDATALOAD SWAP2 DUP3 ISZERO ISZERO DUP1 SWAP4 SUB PUSH2 0xC77 JUMPI PUSH2 0xC56 PUSH2 0xD05 JUMP JUMPDEST PUSH1 0x1 DUP1 PUSH1 0xA0 SHL SUB AND DUP4 MSTORE PUSH1 0x6 PUSH1 0x20 MSTORE DUP3 KECCAK256 SWAP1 PUSH1 0xFF DUP1 NOT DUP4 SLOAD AND SWAP2 AND OR SWAP1 SSTORE DUP1 RETURN JUMPDEST DUP4 DUP1 REVERT JUMPDEST SWAP1 POP DUP3 DUP5 CALLVALUE PUSH2 0xC77 JUMPI PUSH1 0x20 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0xC77 JUMPI DUP2 PUSH1 0x60 SWAP5 PUSH1 0xFF SWAP3 CALLDATALOAD DUP2 MSTORE PUSH1 0x5 PUSH1 0x20 MSTORE KECCAK256 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0x80 SHL SUB DUP2 AND DUP5 MSTORE PUSH8 0xFFFFFFFFFFFFFFFF DUP2 PUSH1 0x80 SHR AND PUSH1 0x20 DUP6 ADD MSTORE PUSH1 0xC0 SHR AND ISZERO ISZERO SWAP1 DUP3 ADD MSTORE RETURN JUMPDEST PUSH1 0x4 CALLDATALOAD SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB DUP3 AND DUP3 SUB PUSH2 0xCE2 JUMPI JUMP JUMPDEST PUSH1 0x0 DUP1 REVERT JUMPDEST CALLVALUE PUSH2 0xCE2 JUMPI PUSH1 0x0 CALLDATASIZE PUSH1 0x3 NOT ADD SLT PUSH2 0xCE2 JUMPI PUSH1 0x20 PUSH1 0x40 MLOAD PUSH3 0x93A80 DUP2 MSTORE RETURN JUMPDEST PUSH1 0x0 SLOAD PUSH1 0x1 PUSH1 0x1 PUSH1 0xA0 SHL SUB AND CALLER SUB PUSH2 0xD19 JUMPI JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH4 0x118CDAA7 PUSH1 0xE0 SHL DUP2 MSTORE CALLER PUSH1 0x4 DUP3 ADD MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST PUSH1 0x3 SLOAD PUSH1 0x1F DUP2 GT PUSH2 0xEED JUMPI PUSH1 0x0 DUP2 DUP2 MSTORE PUSH1 0x4 PUSH1 0x20 MSTORE PUSH1 0x40 DUP2 KECCAK256 DUP1 SLOAD PUSH1 0xFF DUP2 PUSH1 0xC8 SHR AND PUSH2 0xEE7 JUMPI DUP4 SWAP1 PUSH2 0xD61 PUSH2 0xF46 JUMP JUMPDEST SWAP1 PUSH1 0x1 PUSH1 0x1 PUSH1 0x80 SHL SUB DUP2 AND DUP3 LT DUP1 ISZERO DUP1 PUSH2 0xED9 JUMPI JUMPDEST ISZERO PUSH2 0xEA0 JUMPI POP POP DUP3 SLOAD PUSH9 0xFFFFFFFFFFFFFFFFFF PUSH1 0x80 SHL NOT AND TIMESTAMP PUSH1 0x80 SHL PUSH8 0xFFFFFFFFFFFFFFFF PUSH1 0x80 SHL AND OR PUSH1 0x1 PUSH1 0xC0 SHL OR DUP4 SSTORE PUSH32 0xA8303D852039DD03DA732CA0626A63B998F0ADD380069633C8F94C2214A3EC01 SWAP1 PUSH1 0x20 SWAP1 PUSH1 0x40 MLOAD SWAP1 DUP2 MSTORE LOG2 JUMPDEST SLOAD PUSH1 0xFF DUP2 PUSH1 0xC0 SHR AND SWAP1 DUP2 PUSH2 0xE67 JUMPI JUMPDEST POP PUSH2 0xDED JUMPI POP POP JUMP JUMPDEST DUP2 DUP2 MSTORE PUSH1 0x4 PUSH1 0x20 SWAP1 DUP2 MSTORE PUSH1 0x40 DUP1 DUP4 KECCAK256 DUP1 SLOAD PUSH1 0xFF PUSH1 0xC8 SHL NOT AND PUSH1 0x1 PUSH1 0xC8 SHL OR SWAP1 SSTORE MLOAD TIMESTAMP DUP2 MSTORE DUP4 SWAP2 PUSH32 0x21A31473AC3B93C8BFDFE4F59540208CF835CD32697AFE67593091C7E15D949C SWAP2 LOG2 PUSH1 0x1F DUP3 LT PUSH2 0xE42 JUMPI POP POP JUMP JUMPDEST PUSH1 0x1 DUP3 ADD DUP1 SWAP3 GT PUSH2 0xE53 JUMPI POP PUSH1 0x3 SSTORE JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x11 PUSH1 0x4 MSTORE PUSH1 0x24 SWAP1 REVERT JUMPDEST PUSH8 0xFFFFFFFFFFFFFFFF SWAP2 POP PUSH1 0x80 SHR AND PUSH3 0x93A80 DUP2 ADD DUP1 SWAP2 GT PUSH2 0xE8C JUMPI TIMESTAMP LT ISZERO CODESIZE PUSH2 0xDE4 JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL DUP3 MSTORE PUSH1 0x11 PUSH1 0x4 MSTORE PUSH1 0x24 DUP3 REVERT JUMPDEST SWAP2 POP SWAP2 POP DUP1 PUSH2 0xECC JUMPI JUMPDEST PUSH2 0xEB5 JUMPI JUMPDEST POP PUSH2 0xDD5 JUMP JUMPDEST PUSH9 0xFFFFFFFFFFFFFFFFFF PUSH1 0x80 SHL NOT AND DUP2 SSTORE CODESIZE PUSH2 0xEAF JUMP JUMPDEST POP PUSH1 0xFF DUP2 PUSH1 0xC0 SHR AND PUSH2 0xEAA JUMP JUMPDEST POP PUSH1 0xFF DUP3 PUSH1 0xC0 SHR AND ISZERO PUSH2 0xD76 JUMP JUMPDEST POP POP POP POP JUMP JUMPDEST POP JUMP JUMPDEST PUSH1 0xFF PUSH1 0x2 SLOAD AND PUSH2 0xEFC JUMPI JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH4 0xD93C0665 PUSH1 0xE0 SHL DUP2 MSTORE PUSH1 0x4 SWAP1 REVERT JUMPDEST SWAP1 PUSH1 0x1F DUP1 NOT SWAP2 ADD AND DUP2 ADD SWAP1 DUP2 LT PUSH8 0xFFFFFFFFFFFFFFFF DUP3 GT OR PUSH2 0xF30 JUMPI PUSH1 0x40 MSTORE JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x41 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT JUMPDEST PUSH1 0x40 DUP1 MLOAD PUSH4 0x8E15F473 PUSH1 0xE0 SHL PUSH1 0x20 DUP1 DUP4 ADD SWAP2 DUP3 MSTORE PUSH1 0x4 DUP4 MSTORE SWAP3 PUSH8 0xFFFFFFFFFFFFFFFF SWAP3 SWAP1 DUP2 ADD DUP4 DUP2 GT DUP3 DUP3 LT OR PUSH2 0xF30 JUMPI PUSH1 0x40 MSTORE PUSH1 0x0 DUP1 SWAP3 DUP2 SWAP3 MLOAD SWAP1 PUSH32 0x0 GAS STATICCALL RETURNDATASIZE ISZERO PUSH2 0x1054 JUMPI RETURNDATASIZE SWAP3 DUP4 GT PUSH2 0x1040 JUMPI PUSH1 0x40 MLOAD SWAP3 PUSH2 0xFCA PUSH1 0x1F DUP3 ADD PUSH1 0x1F NOT AND DUP7 ADD DUP6 PUSH2 0xF0E JUMP JUMPDEST DUP4 MSTORE RETURNDATASIZE DUP3 DUP6 DUP6 ADD RETURNDATACOPY JUMPDEST DUP1 PUSH2 0x1035 JUMPI JUMPDEST ISZERO PUSH2 0xFF0 JUMPI DUP3 DUP3 DUP1 MLOAD DUP2 ADD SUB SLT PUSH2 0x1BC JUMPI POP ADD MLOAD SWAP1 JUMP JUMPDEST PUSH1 0x40 MLOAD PUSH3 0x461BCD PUSH1 0xE5 SHL DUP2 MSTORE PUSH1 0x4 DUP2 ADD DUP5 SWAP1 MSTORE PUSH1 0x18 PUSH1 0x24 DUP3 ADD MSTORE PUSH32 0x5072696365206F7261636C652063616C6C206661696C65640000000000000000 PUSH1 0x44 DUP3 ADD MSTORE PUSH1 0x64 SWAP1 REVERT JUMPDEST POP DUP3 DUP3 MLOAD LT ISZERO PUSH2 0xFD9 JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL DUP3 MSTORE PUSH1 0x41 PUSH1 0x4 MSTORE PUSH1 0x24 DUP3 REVERT JUMPDEST PUSH1 0x60 SWAP3 POP PUSH2 0xFD3 JUMP JUMPDEST SWAP1 DUP2 PUSH1 0x20 SWAP2 SUB SLT PUSH2 0xCE2 JUMPI MLOAD DUP1 ISZERO ISZERO DUP2 SUB PUSH2 0xCE2 JUMPI SWAP1 JUMP JUMPDEST PUSH1 0x0 NOT DUP2 EQ PUSH2 0x1084 JUMPI PUSH1 0x1 ADD SWAP1 JUMP JUMPDEST PUSH4 0x4E487B71 PUSH1 0xE0 SHL PUSH1 0x0 MSTORE PUSH1 0x11 PUSH1 0x4 MSTORE PUSH1 0x24 PUSH1 0x0 REVERT INVALID LOG2 PUSH5 0x6970667358 0x22 SLT KECCAK256 SWAP12 0xB2 CREATE 0xDB RETURNDATACOPY PUSH6 0xE5A2EE93C01C SWAP16 DUP14 0xCD INVALID SWAP8 0xAA BYTE JUMPDEST 0xDB 0xAD XOR 0x26 CREATE 0xCF 0xBA PUSH20 0xB18A99EB64736F6C634300081400330000000000 ", "sourceMap": "456:8181:5:-:0;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8256:20;8303:1;8306:17;592:2;8306:17;;;;;456:8181;;;;;;;;;592:2;456:8181;;;;;;;;;;8325:3;456:8181;;;;;;;;;;;;;;8344:34;;8325:3;;;;:::i;:::-;8291:13;;8344:34;8368:10;;8325:3;8368:10;;:::i;:::-;8344:34;;;;;456:8181;;;;;;;;;;;-1:-1:-1;;456:8181:5;;;;;;:::i;:::-;1500:62:0;;;:::i;:::-;-1:-1:-1;;;;;456:8181:5;;;;2627:22:0;;2623:91;;-1:-1:-1;;456:8181:5;;-1:-1:-1;;;;;;456:8181:5;;;;;;;3052:40:0;456:8181:5;;3052:40:0;456:8181:5;;2623:91:0;456:8181:5;-1:-1:-1;;;2672:31:0;;;;;456:8181:5;;;;;2672:31:0;456:8181:5;;;;;;;;;;;;;;;;;;;8610:18;;;:::i;:::-;456:8181;;;;;;;;;;;;;;;;;;-1:-1:-1;;456:8181:5;;;;;;;;;;-1:-1:-1;;;;;456:8181:5;;:::i;:::-;;;;1671:48;456:8181;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;763:16;456:8181;;;;;;;;;;;;;;;;;;;;592:2;456:8181;;;;;;;;;;;;;;;;;;941:40;-1:-1:-1;;;;;456:8181:5;;;;;;;;;;;;;;;;;;;;;897:38;-1:-1:-1;;;;;456:8181:5;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;456:8181:5;;;;;;;;;;;;;;;;;;;;;;;;1730:37;456:8181;;;;;;;;;;;;;;;-1:-1:-1;;456:8181:5;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;456:8181:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;456:8181:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;456:8181:5;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;456:8181:5;;;;;;;;;;;;;;;;;;;;;;;2427:20:3;1500:62:0;;;:::i;:::-;1315:72:3;;:::i;:::-;2408:4;456:8181:5;;2398:14:3;456:8181:5;;;2398:14:3;456:8181:5;;735:10:2;456:8181:5;;2427:20:3;456:8181:5;;;;:::i;:::-;;;;;;;;-1:-1:-1;;456:8181:5;;;;;;1500:62:0;;;:::i;:::-;7287:13:5;;456:8181;;;;7338:30;456:8181;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;1500:62:0;;;:::i;:::-;763:16:5;5619:30;;456:8181;;;;5725:82;456:8181;5725:82;;456:8181;;;;;;;;;;;;;;5770:15;456:8181;;;;5787:10;456:8181;;;;;;5725:82;;;456:8181;;;;;;;;;;;;;;;;;;;;5715:93;;456:8181;;;;;;;;;;;;;;;-1:-1:-1;;;;;456:8181:5;;;;;5770:15;456:8181;;5858:144;;;456:8181;;;;5858:144;;;456:8181;;;;;;5827:17;456:8181;;;;;;;;;;;;;;-1:-1:-1;;;;;;456:8181:5;;;;;;;;;;;;;;;-1:-1:-1;;;456:8181:5;;;;;;-1:-1:-1;456:8181:5;;6026:45;;456:8181;;;;;-1:-1:-1;;;;456:8181:5;;;;;;-1:-1:-1;;;456:8181:5;;;;;;;;;6026:45;456:8181;;;;;;-1:-1:-1;;;456:8181:5;;;;;;;;;-1:-1:-1;;;456:8181:5;;;;;;;;;;;-1:-1:-1;;;456:8181:5;;;;;;;;;;;;-1:-1:-1;;;456:8181:5;;;;;;;;;;;;;;;;;;;;1500:62:0;;:::i;:::-;456:8181:5;;-1:-1:-1;;;;;;456:8181:5;;;;;;-1:-1:-1;;;;;456:8181:5;3052:40:0;456:8181:5;;3052:40:0;456:8181:5;;;;;;;;;;;;;;;1315:72:3;;:::i;:::-;1379:1;;:::i;:::-;456:8181:5;;;;;;;;;;;;;;;;;;;1796:7:3;456:8181:5;;;;;;;;;;;;;;;;;;;;;;;;1500:62:0;;:::i;:::-;1796:7:3;456:8181:5;;;;;2140:9:3;2136:62;;-1:-1:-1;;;456:8181:5;1796:7:3;456:8181:5;;735:10:2;456:8181:5;;2674:22:3;;456:8181:5;;2674:22:3;456:8181:5;;2136:62:3;456:8181:5;;-1:-1:-1;;;2172:15:3;;;456:8181:5;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;456:8181:5;;;;;;;;;1899:1:4;456:8181:5;;2702:18:4;2698:86;;1899:1;456:8181:5;;6308:10;456:8181;;;;6291:16;456:8181;;;;;;;;;;;;;;6399:17;456:8181;;;;;;;;;;;;;;;;;;;;;650:6;456:8181;;;;;;;6514:15;:56;456:8181;;-1:-1:-1;;;;456:8181:5;;-1:-1:-1;;;456:8181:5;;;6699:9;456:8181;;;;;;;;6682:27;;;456:8181;;-1:-1:-1;;456:8181:5;;;;-1:-1:-1;;;6725:49:5;;-1:-1:-1;;;;;6744:13:5;456:8181;6725:49;;;456:8181;;;-1:-1:-1;;;;;456:8181:5;;;;;;;;;;6725:49;;;456:8181;6725:49;;-1:-1:-1;456:8181:5;;;;6725:49;;;;;;;-1:-1:-1;;;;;6725:49:5;6892:52;6725:49;;;;;6678:191;;;456:8181;;;;;;;6892:52;456:8181;;;;;6725:49;;;;;;;;;;;;;;:::i;:::-;;;;;:::i;:::-;;;;;;;;;;;456:8181;;;;;;;;;6678:191;456:8181;;-1:-1:-1;;;6805:53:5;;-1:-1:-1;;;;;6828:13:5;456:8181;6805:53;;;456:8181;;;-1:-1:-1;;;;;456:8181:5;;;;;;;;-1:-1:-1;456:8181:5;;;;;;;;;6805:53;;;;;;;;;-1:-1:-1;;;;;6805:53:5;6892:52;6805:53;;;;;6678:191;;;;6805:53;;;;;;;;;;;;;:::i;:::-;;;;;456:8181;;;-1:-1:-1;;;456:8181:5;;;;;;;;;;;;;-1:-1:-1;;;456:8181:5;;;;;;;;-1:-1:-1;;;456:8181:5;;;;;;;;;;;-1:-1:-1;;;456:8181:5;;;;;;;;;;;;;-1:-1:-1;;;456:8181:5;;;;;;;;;;-1:-1:-1;;;456:8181:5;;;;;;;;;;;;;-1:-1:-1;;;456:8181:5;;;;;;;2698:86:4;456:8181:5;;-1:-1:-1;;;2743:30:4;;456:8181:5;;2743:30:4;456:8181:5;;;;;;;;;;;;;;;;;;;855:36;-1:-1:-1;;;;;456:8181:5;;;;;;;;;;;;;;;;;;;;;816:33;-1:-1:-1;;;;;456:8181:5;;;;;;;;;;;;;;;;;;;;;;:::i;:::-;;;;;;;;;;;;;1500:62:0;;:::i;:::-;456:8181:5;;;;;;;;7097:16;456:8181;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;456:8181:5;;;;;;;;;;;;1604:61;456:8181;;;;-1:-1:-1;;;;;456:8181:5;;;;;;;;;;;;;;;;;;;;;;;;;;;-1:-1:-1;;;;;456:8181:5;;;;;;:::o;:::-;;;;;;;;;;-1:-1:-1;;456:8181:5;;;;;;;650:6;456:8181;;;1796:162:0;1710:6;456:8181:5;-1:-1:-1;;;;;456:8181:5;735:10:2;1855:23:0;1851:101;;1796:162::o;1851:101::-;456:8181:5;;-1:-1:-1;;;1901:40:0;;735:10:2;1901:40:0;;;456:8181:5;;;1901:40:0;3696:982:5;3782:12;456:8181;592:2;3808:26;;3804:39;;-1:-1:-1;456:8181:5;;;3883:6;456:8181;;;;;;;;;;;;3912:27;;3980:18;;;;:::i;:::-;456:8181;-1:-1:-1;;;;;456:8181:5;;4028:34;;;;4085:40;;;3696:982;4081:374;;;-1:-1:-1;;456:8181:5;;-1:-1:-1;;;;456:8181:5;4217:15;456:8181;;-1:-1:-1;;;456:8181:5;;-1:-1:-1;;;456:8181:5;;;4252:44;;456:8181;;;;;;;4252:44;4081:374;456:8181;;;;;;4513:107;;;;4081:374;4509:163;;;3696:982;;:::o;4509:163::-;456:8181;;;3883:6;456:8181;;;;;;;;;;-1:-1:-1;;;;456:8181:5;-1:-1:-1;;;456:8181:5;;;;4914:15;456:8181;;;;4887:43;;;592:2;4953:26;;4949:87;;3696:982;;:::o;4949:87::-;456:8181;;;;;;;;;3782:12;456:8181;3696:982::o;456:8181::-;-1:-1:-1;;;456:8181:5;;;3883:6;456:8181;;;;4513:107;456:8181;;;;;;650:6;456:8181;;;;;;;4553:15;:67;;4513:107;;;456:8181;-1:-1:-1;;;456:8181:5;;;3883:6;456:8181;;;;4081:374;4317:40;;;;;;;4081:374;4313:142;;4081:374;;;;4313:142;-1:-1:-1;;;;456:8181:5;;;4313:142;;;4317:40;456:8181;;;;;;4317:40;;4085;456:8181;;;;;;4101:24;4085:40;;3912:27;3932:7;;;;:::o;3804:39::-;3836:7;:::o;1878:128:3:-;456:8181:5;1796:7:3;456:8181:5;;1939:61:3;;1878:128::o;1939:61::-;456:8181:5;;-1:-1:-1;;;1974:15:3;;;;;456:8181:5;;;;;;;;;;;;;;;;;;;;;:::o;:::-;;;;;;;;;;;;5099:321;456:8181;;;-1:-1:-1;;;5241:43:5;;;;;;;;;;;456:8181;;;;;;;;;;;;;;;;5241:43;5205:89;;;;;:11;;:89;;456:8181;;;;;;;;;;;;;;;;;-1:-1:-1;;456:8181:5;;;;;:::i;:::-;;;;;;;;;;5312:28;;;456:8181;;;;;;;;5386:27;;456:8181;;;;5386:27;;456:8181;5099:321;:::o;456:8181::-;;;-1:-1:-1;;;456:8181:5;;5241:43;456:8181;;;;;;5241:43;456:8181;;;;;;;;;;;5312:28;456:8181;;;;5323:17;;5312:28;;456:8181;-1:-1:-1;;;456:8181:5;;;5241:43;456:8181;5241:43;456:8181;;;;;-1:-1:-1;456:8181:5;;;;;;;;;;;;;;;;;;;;:::o;:::-;-1:-1:-1;;456:8181:5;;;;;;;:::o;:::-;;;;;;;;;;;"}, "methodIdentifiers": {"EMERGENCY_DELAY()": "82944e2d", "MAX_EMERGENCY_AMOUNT()": "cf9d226e", "PRICE_MAINTAIN_DURATION()": "540797a5", "TOTAL_ROUNDS()": "ce8dc388", "checkPriceCondition()": "687404b2", "communityWallet()": "c7574839", "currentRound()": "8a19c8bc", "emergencyRequests(bytes32)": "0557ac41", "emergencySigners(address)": "d0f694cb", "executeEmergencyWithdraw(bytes32,address)": "34f4aca1", "getCurrentPrice()": "eb91d37e", "getRoundInfo(uint256)": "88c3ffb0", "getUnlockProgress()": "ff5f1e91", "haoxToken()": "25dbec9d", "owner()": "8da5cb5b", "pause()": "8456cb59", "paused()": "5c975abb", "priceOracle()": "2630c12f", "projectWallet()": "beb08ab9", "renounceOwnership()": "715018a6", "requestEmergencyWithdraw(address,uint256)": "7c7f4ce5", "requiredSignatures()": "8d068043", "rounds(uint256)": "8c65c81f", "setEmergencySigner(address,bool)": "200dfd5b", "setRequiredSignatures(uint256)": "7d2b9cc0", "transferOwnership(address)": "f2fde38b", "unpause()": "3f4ba83a"}}, "metadata": "{\"compiler\":{\"version\":\"0.8.20+commit.a1b79de6\"},\"language\":\"Solidity\",\"output\":{\"abi\":[{\"inputs\":[{\"internalType\":\"address\",\"name\":\"_haoxToken\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_priceOracle\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_projectWallet\",\"type\":\"address\"},{\"internalType\":\"address\",\"name\":\"_communityWallet\",\"type\":\"address\"}],\"stateMutability\":\"nonpayable\",\"type\":\"constructor\"},{\"inputs\":[],\"name\":\"EnforcedPause\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ExpectedPause\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"owner\",\"type\":\"address\"}],\"name\":\"OwnableInvalidOwner\",\"type\":\"error\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"OwnableUnauthorizedAccount\",\"type\":\"error\"},{\"inputs\":[],\"name\":\"ReentrancyGuardReentrantCall\",\"type\":\"error\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"requestId\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"EmergencyWithdrawExecuted\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"bytes32\",\"name\":\"requestId\",\"type\":\"bytes32\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"EmergencyWithdrawRequested\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"address\",\"name\":\"previousOwner\",\"type\":\"address\"},{\"indexed\":true,\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"OwnershipTransferred\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Paused\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"roundNumber\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"price\",\"type\":\"uint256\"}],\"name\":\"PriceConditionMet\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":true,\"internalType\":\"uint256\",\"name\":\"roundNumber\",\"type\":\"uint256\"},{\"indexed\":false,\"internalType\":\"uint256\",\"name\":\"timestamp\",\"type\":\"uint256\"}],\"name\":\"RoundUnlocked\",\"type\":\"event\"},{\"anonymous\":false,\"inputs\":[{\"indexed\":false,\"internalType\":\"address\",\"name\":\"account\",\"type\":\"address\"}],\"name\":\"Unpaused\",\"type\":\"event\"},{\"inputs\":[],\"name\":\"EMERGENCY_DELAY\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"MAX_EMERGENCY_AMOUNT\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"PRICE_MAINTAIN_DURATION\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"TOTAL_ROUNDS\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"checkPriceCondition\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"communityWallet\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"currentRound\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"name\":\"emergencyRequests\",\"outputs\":[{\"internalType\":\"uint128\",\"name\":\"amount\",\"type\":\"uint128\"},{\"internalType\":\"uint64\",\"name\":\"requestTime\",\"type\":\"uint64\"},{\"internalType\":\"bool\",\"name\":\"executed\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"name\":\"emergencySigners\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"bytes32\",\"name\":\"requestId\",\"type\":\"bytes32\"},{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"}],\"name\":\"executeEmergencyWithdraw\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getCurrentPrice\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"roundNumber\",\"type\":\"uint256\"}],\"name\":\"getRoundInfo\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"triggerPrice\",\"type\":\"uint256\"},{\"internalType\":\"bool\",\"name\":\"priceConditionMet\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"unlocked\",\"type\":\"bool\"},{\"internalType\":\"uint256\",\"name\":\"priceReachedTime\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"getUnlockProgress\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"totalRounds\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"currentRoundNumber\",\"type\":\"uint256\"},{\"internalType\":\"uint256\",\"name\":\"unlockedRounds\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"haoxToken\",\"outputs\":[{\"internalType\":\"contract IERC20\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"owner\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"pause\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"paused\",\"outputs\":[{\"internalType\":\"bool\",\"name\":\"\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"priceOracle\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"projectWallet\",\"outputs\":[{\"internalType\":\"address\",\"name\":\"\",\"type\":\"address\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"renounceOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"token\",\"type\":\"address\"},{\"internalType\":\"uint256\",\"name\":\"amount\",\"type\":\"uint256\"}],\"name\":\"requestEmergencyWithdraw\",\"outputs\":[{\"internalType\":\"bytes32\",\"name\":\"\",\"type\":\"bytes32\"}],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"requiredSignatures\",\"outputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"\",\"type\":\"uint256\"}],\"name\":\"rounds\",\"outputs\":[{\"internalType\":\"uint128\",\"name\":\"triggerPrice\",\"type\":\"uint128\"},{\"internalType\":\"uint64\",\"name\":\"priceReachedTime\",\"type\":\"uint64\"},{\"internalType\":\"bool\",\"name\":\"priceConditionMet\",\"type\":\"bool\"},{\"internalType\":\"bool\",\"name\":\"unlocked\",\"type\":\"bool\"}],\"stateMutability\":\"view\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"signer\",\"type\":\"address\"},{\"internalType\":\"bool\",\"name\":\"status\",\"type\":\"bool\"}],\"name\":\"setEmergencySigner\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"uint256\",\"name\":\"_required\",\"type\":\"uint256\"}],\"name\":\"setRequiredSignatures\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[{\"internalType\":\"address\",\"name\":\"newOwner\",\"type\":\"address\"}],\"name\":\"transferOwnership\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"},{\"inputs\":[],\"name\":\"unpause\",\"outputs\":[],\"stateMutability\":\"nonpayable\",\"type\":\"function\"}],\"devdoc\":{\"details\":\"\\u8d85\\u7cbe\\u7b80\\u7248HAOX\\u4ee3\\u5e01\\u89e3\\u9501\\u5408\\u7ea6 - \\u6781\\u81f4\\u6210\\u672c\\u4f18\\u5316\\u7248\\u672c \\u4ec5\\u4fdd\\u7559\\u6700\\u6838\\u5fc3\\u7684\\u5b89\\u5168\\u529f\\u80fd\\uff0c\\u6700\\u5927\\u5316\\u964d\\u4f4e\\u90e8\\u7f72\\u6210\\u672c\",\"errors\":{\"EnforcedPause()\":[{\"details\":\"The operation failed because the contract is paused.\"}],\"ExpectedPause()\":[{\"details\":\"The operation failed because the contract is not paused.\"}],\"OwnableInvalidOwner(address)\":[{\"details\":\"The owner is not a valid owner account. (eg. `address(0)`)\"}],\"OwnableUnauthorizedAccount(address)\":[{\"details\":\"The caller account is not authorized to perform an operation.\"}],\"ReentrancyGuardReentrantCall()\":[{\"details\":\"Unauthorized reentrant call.\"}]},\"events\":{\"Paused(address)\":{\"details\":\"Emitted when the pause is triggered by `account`.\"},\"Unpaused(address)\":{\"details\":\"Emitted when the pause is lifted by `account`.\"}},\"kind\":\"dev\",\"methods\":{\"checkPriceCondition()\":{\"details\":\"\\u68c0\\u67e5\\u4ef7\\u683c\\u6761\\u4ef6\\uff08\\u8d85\\u7cbe\\u7b80\\u7248\\uff09\"},\"executeEmergencyWithdraw(bytes32,address)\":{\"details\":\"\\u6267\\u884c\\u7d27\\u6025\\u63d0\\u53d6\"},\"getCurrentPrice()\":{\"details\":\"\\u83b7\\u53d6\\u5f53\\u524d\\u4ef7\\u683c\\uff08\\u5916\\u90e8\\u8c03\\u7528\\uff09\"},\"getRoundInfo(uint256)\":{\"details\":\"\\u83b7\\u53d6\\u8f6e\\u6b21\\u4fe1\\u606f\"},\"getUnlockProgress()\":{\"details\":\"\\u83b7\\u53d6\\u89e3\\u9501\\u8fdb\\u5ea6\"},\"owner()\":{\"details\":\"Returns the address of the current owner.\"},\"pause()\":{\"details\":\"\\u6682\\u505c/\\u6062\\u590d\\u5408\\u7ea6\"},\"paused()\":{\"details\":\"Returns true if the contract is paused, and false otherwise.\"},\"renounceOwnership()\":{\"details\":\"Leaves the contract without owner. It will not be possible to call `onlyOwner` functions. Can only be called by the current owner. NOTE: Renouncing ownership will leave the contract without an owner, thereby disabling any functionality that is only available to the owner.\"},\"requestEmergencyWithdraw(address,uint256)\":{\"details\":\"\\u7d27\\u6025\\u63d0\\u53d6\\u8bf7\\u6c42\"},\"setEmergencySigner(address,bool)\":{\"details\":\"\\u7ba1\\u7406\\u7d27\\u6025\\u7b7e\\u540d\\u8005\"},\"setRequiredSignatures(uint256)\":{\"details\":\"\\u8bbe\\u7f6e\\u6240\\u9700\\u7b7e\\u540d\\u6570\\u91cf\"},\"transferOwnership(address)\":{\"details\":\"Transfers ownership of the contract to a new account (`newOwner`). Can only be called by the current owner.\"}},\"title\":\"HAOXVestingV2Ultra\",\"version\":1},\"userdoc\":{\"kind\":\"user\",\"methods\":{},\"version\":1}},\"settings\":{\"compilationTarget\":{\"contracts/HAOXVestingV2Ultra.sol\":\"HAOXVestingV2Ultra\"},\"evmVersion\":\"paris\",\"libraries\":{},\"metadata\":{\"bytecodeHash\":\"ipfs\"},\"optimizer\":{\"enabled\":true,\"runs\":200},\"remappings\":[],\"viaIR\":true},\"sources\":{\"@openzeppelin/contracts/access/Ownable.sol\":{\"keccak256\":\"0xff6d0bb2e285473e5311d9d3caacb525ae3538a80758c10649a4d61029b017bb\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://8ed324d3920bb545059d66ab97d43e43ee85fd3bd52e03e401f020afb0b120f6\",\"dweb:/ipfs/QmfEckWLmZkDDcoWrkEvMWhms66xwTLff9DDhegYpvHo1a\"]},\"@openzeppelin/contracts/token/ERC20/IERC20.sol\":{\"keccak256\":\"0x74ed01eb66b923d0d0cfe3be84604ac04b76482a55f9dd655e1ef4d367f95bc2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://5282825a626cfe924e504274b864a652b0023591fa66f06a067b25b51ba9b303\",\"dweb:/ipfs/QmeCfPykghhMc81VJTrHTC7sF6CRvaA1FXVq2pJhwYp1dV\"]},\"@openzeppelin/contracts/utils/Context.sol\":{\"keccak256\":\"0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12\",\"dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF\"]},\"@openzeppelin/contracts/utils/Pausable.sol\":{\"keccak256\":\"0xdb484371dfbb848cb6f5d70464e9ac9b2900e4164ead76bbce4fef0b44bcc68f\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://f9d6f6f6600a2bec622f699081b58350873b5e63ce05464d17d674a290bb8a7c\",\"dweb:/ipfs/QmQKVzSQY1PM3Bid4QhgVVZyx6B4Jx7XgaQzLKHj38vJz8\"]},\"@openzeppelin/contracts/utils/ReentrancyGuard.sol\":{\"keccak256\":\"0x11a5a79827df29e915a12740caf62fe21ebe27c08c9ae3e09abe9ee3ba3866d3\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://3cf0c69ab827e3251db9ee6a50647d62c90ba580a4d7bbff21f2bea39e7b2f4a\",\"dweb:/ipfs/QmZiKwtKU1SBX4RGfQtY7PZfiapbbu6SZ9vizGQD9UHjRA\"]},\"contracts/HAOXVestingV2Ultra.sol\":{\"keccak256\":\"0xb972ebaa24234cd8297cdccdec37e0b7171bd794d0f39e116c08083b6913eb4c\",\"license\":\"MIT\",\"urls\":[\"bzz-raw://ee0c68496b16826c0069e1fb4f4158290744fac377323d8c09b7e1fb21483b67\",\"dweb:/ipfs/QmX87AUJh1q3aQg6WZzHr1YX3YbKvL8uDKDSU3cjg9zEnt\"]}},\"version\":1}"}}}}}