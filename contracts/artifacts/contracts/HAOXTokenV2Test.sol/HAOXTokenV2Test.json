{"_format": "hh-sol-artifact-1", "contractName": "HAOXTokenV2Test", "sourceName": "contracts/HAOXTokenV2Test.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "allowance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "ERC20InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC20InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC20InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC20InvalidSender", "type": "error"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}], "name": "ERC20InvalidSpender", "type": "error"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "string", "name": "contractType", "type": "string"}, {"indexed": false, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": false, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "ContractAddressUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "duration", "type": "uint256"}], "name": "EmergencyPauseActivated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "lockId", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "unlockTime", "type": "uint256"}], "name": "TimeLockCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "lockId", "type": "bytes32"}], "name": "TimeLockExecuted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "INITIAL_UNLOCK", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_PAUSE_DURATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TIMELOCK_DELAY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOTAL_SUPPLY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "autoUnpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "emergencyPause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getContractStatus", "outputs": [{"internalType": "bool", "name": "isPaused", "type": "bool"}, {"internalType": "uint256", "name": "pauseDuration", "type": "uint256"}, {"internalType": "uint256", "name": "remainingPauseTime", "type": "uint256"}, {"internalType": "address", "name": "presale", "type": "address"}, {"internalType": "address", "name": "invitation", "type": "address"}, {"internalType": "address", "name": "vesting", "type": "address"}, {"internalType": "address", "name": "oracle", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "invitationContract", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pauseStartTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "presaleContract", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "priceOracle", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_invitationContract", "type": "address"}], "name": "setInvitationContract", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_presaleContract", "type": "address"}], "name": "setPresaleContract", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_priceOracle", "type": "address"}], "name": "setPriceO<PERSON>le", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_vestingContract", "type": "address"}], "name": "setVestingContract", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "timeLocks", "outputs": [{"internalType": "uint256", "name": "unlockTime", "type": "uint256"}, {"internalType": "bool", "name": "executed", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "vestingContract", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "0x6080604081815260048036101561001557600080fd5b600092833560e01c90816306fdde031461110957508063095ea7b31461106057806318160ddd146110415780631be351fd14610ed757806323b872dd14610dda5780632630c12f14610db15780632799657d14610d94578063313ce56714610d785780633f4ba83a14610d0a5780634b3a5fa814610ce357806351858e2714610c11578063530e784f14610aad5780635ba1c1a914610a905780635c975abb14610a695780635e6f604514610a4057806363d9df8514610a1757806370a08231146109e0578063715018a61461097857806374991569146108145780638da5cb5b146107eb578063902d55a5146107c457806394d7eaa4146107a557806395d89b41146106a1578063a9059cbb14610668578063a9bbd114146104b6578063aca62e0014610398578063b88a3f7c14610362578063c032846b1461027b578063dd62ed3e14610232578063dfeb023b146102055763f2fde38b1461017857600080fd5b3461020157602036600319011261020157610191611248565b9061019a611279565b6001600160a01b039182169283156101eb575050600554826001600160601b0360a01b821617600555167f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e08380a380f35b51631e4fbdf760e01b8152908101849052602490fd5b8280fd5b50503461022e578160031936011261022e5760075490516001600160a01b039091168152602090f35b5080fd5b50503461022e578060031936011261022e5780602092610250611248565b610258611263565b6001600160a01b0391821683526001865283832091168252845220549051908152f35b5091903461022e578160031936011261022e5760055460a01c60ff1691808380610357575b61030a575b600b5460e095508015610303576102bd9150426114cf565b915b60018060a01b039182600654169083600754169284600854169460095416958151971515885260208801528601526060850152608084015260a083015260c0820152f35b50916102bf565b9050610318600b54426114cf565b610258908181101561034b5781039081116103385760e094505b906102a5565b634e487b7160e01b825260118552602482fd5b505060e0935080610332565b50600b5415156102a0565b503461020157602036600319011261020157918192358152600a6020522060ff6001825492015416825191825215156020820152f35b50346102015782600319360112610201576005549060ff8260a01c166103bd816113b8565b600b54801561047d57610258810180911161046a5742106104275715610419575060ff60a01b1916600555513381527f5db9ee0a495bf2e6ff9c91a7834c1ba4fdd244a5e8aa4e537bd38aeae4b073aa90602090a180600b5580f35b8251638dfc202b60e01b8152fd5b835162461bcd60e51b8152602081840152601b60248201527f5061757365206475726174696f6e206e6f7420657863656564656400000000006044820152606490fd5b634e487b7160e01b865260118352602486fd5b845162461bcd60e51b81526020818501526013602482015272496e76616c696420706175736520737461746560681b6044820152606490fd5b5091903461022e57602080600319360112610201576104d3611248565b916104dc611279565b8051828101711cd95d141c995cd85b1950dbdb9d1c9858dd60721b815260018060a01b0391828616956001600160601b03199060601b1660328201524260468201526046815261052b816112d7565b51902093848652600a84528286205480156000146105bf5750505061012c42018042116105ac576000805160206114f383398151915293949550815190610571826112a5565b81526105a2838201878152868852600a855283882092518355511515600183019060ff801983541691151516179055565b549051908152a280f35b634e487b7160e01b855260118652602485fd5b60008051602061153383398151915295969750936105e560a09594929394421015611315565b868852600a81526105ff60ff6001848b2001541615611358565b60065493836001600160601b03871b861617600655878952600a82526001838a2001600160ff1982541617905582519460608652600760608701526670726573616c6560c81b60808701521690840152820152a16000805160206115138339815191528280a280f35b50503461022e578060031936011261022e5760209061069a610688611248565b610690611397565b60243590336113f1565b5160018152f35b5091903461022e578160031936011261022e57805191809380549160019083821c9282851694851561079b575b602095868610811461078857858952908115610764575060011461070c575b61070887876106fe828c03836112f3565b51918291826111ff565b0390f35b81529295507f8a35acfbc15ff81a39ae7d344fd709f28e8600b4aa8c65c6b64bfe7fe36bd19b5b8284106107515750505082610708946106fe928201019438806106ed565b8054868501880152928601928101610733565b60ff19168887015250505050151560051b83010192506106fe8261070838806106ed565b634e487b7160e01b845260228352602484fd5b93607f16936106ce565b50503461022e578160031936011261022e57602090600b549051908152f35b50503461022e578160031936011261022e57602090516ba18f07d736b90be5500000008152f35b50503461022e578160031936011261022e5760055490516001600160a01b039091168152602090f35b5091903461022e5760208060031936011261020157610831611248565b9161083a611279565b8051828101711cd95d15995cdd1a5b99d0dbdb9d1c9858dd60721b815260018060a01b0391828616956001600160601b03199060601b16603282015242604682015260468152610889816112d7565b51902093848652600a84528286205480156000146108cf5750505061012c42018042116105ac576000805160206114f383398151915293949550815190610571826112a5565b60008051602061153383398151915295969750936108f560a09594929394421015611315565b868852600a815261090f60ff6001848b2001541615611358565b60085493836001600160601b03871b861617600855878952600a82526001838a2001600160ff1982541617905582519460608652600760608701526676657374696e6760c81b60808701521690840152820152a16000805160206115138339815191528280a280f35b83346109dd57806003193601126109dd57610991611279565b610999611279565b600580546001600160a01b0319811690915581906001600160a01b03167f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e08280a380f35b80fd5b50503461022e57602036600319011261022e5760209181906001600160a01b03610a08611248565b16815280845220549051908152f35b50503461022e578160031936011261022e5760065490516001600160a01b039091168152602090f35b50503461022e578160031936011261022e5760085490516001600160a01b039091168152602090f35b50503461022e578160031936011261022e5760209060ff60055460a01c1690519015158152f35b50503461022e578160031936011261022e576020905161012c8152f35b5091903461022e5760208060031936011261020157610aca611248565b91610ad3611279565b80518281016d73657450726963654f7261636c6560901b815260018060a01b0391828616956001600160601b03199060601b16602e82015242604282015260428152610b1e816112d7565b51902093848652600a8452828620548015600014610b645750505061012c42018042116105ac576000805160206114f383398151915293949550815190610571826112a5565b6000805160206115338339815191529596975093610b8a60a09594929394421015611315565b868852600a8152610ba460ff6001848b2001541615611358565b60095493836001600160601b03871b861617600955878952600a82526001838a2001600160ff1982541617905582519460608652600b60608701526a70726963654f7261636c6560a81b60808701521690840152820152a16000805160206115138339815191528280a280f35b5034610201578260031936011261020157610c2a611279565b6005549060ff8260a01c16610caf57507fcad6484bde37895e726a492aecc6c7220aeb217858233794e23ea805ed2918f29160209142600b55610c6b611397565b60ff60a01b1916600160a01b1760055580513381527f62e78cea01bee320cd4e420270b5ea74000d11b0c9f74754ebdbfc544b05a258908390a1516102588152a180f35b606490602084519162461bcd60e51b8352820152600e60248201526d105b1c9958591e481c185d5cd95960921b6044820152fd5b50503461022e578160031936011261022e57602090516b019d971e4fe8401e740000008152f35b5034610201578260031936011261020157610d23611279565b6005549060ff8260a01c16610d37816113b8565b15610419575060ff60a01b1916600555513381527f5db9ee0a495bf2e6ff9c91a7834c1ba4fdd244a5e8aa4e537bd38aeae4b073aa90602090a180600b5580f35b50503461022e578160031936011261022e576020905160128152f35b50503461022e578160031936011261022e57602090516102588152f35b50503461022e578160031936011261022e5760095490516001600160a01b039091168152602090f35b5082346109dd5760603660031901126109dd57610df5611248565b610dfd611263565b9160443593610e0a611397565b6001600160a01b03831680835260016020908152878420338552905286832054916000198310610e43575b60208861069a8989896113f1565b868310610eab578115610e94573315610e7d5750825260016020908152868320338452815291869020908590039055829061069a87610e35565b8751634a1406b160e11b8152908101849052602490fd5b875163e602df0560e01b8152908101849052602490fd5b8751637dc7a0d960e11b8152339181019182526020820193909352604081018790528291506060010390fd5b5091903461022e5760208060031936011261020157610ef4611248565b91610efd611279565b8051828101741cd95d125b9d9a5d185d1a5bdb90dbdb9d1c9858dd605a1b815260018060a01b0391828616956001600160601b03199060601b16603582015242604982015260498152610f4f816112d7565b51902093848652600a8452828620548015600014610f955750505061012c42018042116105ac576000805160206114f383398151915293949550815190610571826112a5565b6000805160206115338339815191529596975093610fbb60a09594929394421015611315565b868852600a8152610fd560ff6001848b2001541615611358565b60075493836001600160601b03871b861617600755878952600a82526001838a2001600160ff1982541617905582519460608652600a60608701526934b73b34ba30ba34b7b760b11b60808701521690840152820152a16000805160206115138339815191528280a280f35b50503461022e578160031936011261022e576020906002549051908152f35b5034610201578160031936011261020157611079611248565b6024359033156110f2576001600160a01b03169182156110db57508083602095338152600187528181208582528752205582519081527f8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925843392a35160018152f35b8351634a1406b160e11b8152908101859052602490fd5b835163e602df0560e01b8152808401869052602490fd5b92919050346111fb57836003193601126111fb57600354600181811c91869082811680156111f1575b60209586861082146111de57508488529081156111bc5750600114611163575b61070886866106fe828b03836112f3565b929550600383527fc2575a0e9e593c00f959f8c92f12db2869c3395a3b0502d05e2516446f71f85b5b8284106111a95750505082610708946106fe928201019438611152565b805486850188015292860192810161118c565b60ff191687860152505050151560051b83010192506106fe8261070838611152565b634e487b7160e01b845260229052602483fd5b93607f1693611132565b8380fd5b6020808252825181830181905290939260005b82811061123457505060409293506000838284010152601f8019910116010190565b818101860151848201604001528501611212565b600435906001600160a01b038216820361125e57565b600080fd5b602435906001600160a01b038216820361125e57565b6005546001600160a01b0316330361128d57565b60405163118cdaa760e01b8152336004820152602490fd5b6040810190811067ffffffffffffffff8211176112c157604052565b634e487b7160e01b600052604160045260246000fd5b6080810190811067ffffffffffffffff8211176112c157604052565b90601f8019910116810190811067ffffffffffffffff8211176112c157604052565b1561131c57565b60405162461bcd60e51b8152602060048201526014602482015273151a5b59531bd8dac81b9bdd08195e1c1a5c995960621b6044820152606490fd5b1561135f57565b60405162461bcd60e51b815260206004820152601060248201526f105b1c9958591e48195e1958dd5d195960821b6044820152606490fd5b60ff60055460a01c166113a657565b60405163d93c066560e01b8152600490fd5b156113bf57565b60405162461bcd60e51b815260206004820152600a602482015269139bdd081c185d5cd95960b21b6044820152606490fd5b916001600160a01b038084169283156114b6571692831561149d576000908382528160205260408220549083821061146b575091604082827fddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef958760209652828652038282205586815220818154019055604051908152a3565b60405163391434e360e21b81526001600160a01b03919091166004820152602481019190915260448101839052606490fd5b60405163ec442f0560e01b815260006004820152602490fd5b604051634b637e8f60e11b815260006004820152602490fd5b919082039182116114dc57565b634e487b7160e01b600052601160045260246000fdfeefbccbdc10031e84a512ecf0debd193e5f2f30e8d3f8dad074585b456d3ace720bf2700d9b198711405cd6cf0a34d32bce88b84568d8139a5245d2f9b095e722a90fb318cef160851f542872dfce25dadafdfde152ba8cc05e3f0b882842e53ca26469706673582212201bc5cfd0f26d8168a35c50fbd476ccabde14f550de02ecba9e0caac80b16192d64736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}