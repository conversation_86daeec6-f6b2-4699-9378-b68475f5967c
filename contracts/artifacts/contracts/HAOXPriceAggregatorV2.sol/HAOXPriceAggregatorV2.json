{"_format": "hh-sol-artifact-1", "contractName": "HAOXPriceAggregatorV2", "sourceName": "contracts/HAOXPriceAggregatorV2.sol", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "price", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "EmergencyModeActivated", "type": "event"}, {"anonymous": false, "inputs": [], "name": "EmergencyModeDeactivated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "price", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "sourceCount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "confidence", "type": "uint256"}], "name": "PriceAggregated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "sourceId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "sourcePrice", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "aggregatedPrice", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "deviation", "type": "uint256"}], "name": "PriceDeviationDetected", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "sourceId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "oracle", "type": "address"}, {"indexed": false, "internalType": "string", "name": "name", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "weight", "type": "uint256"}], "name": "PriceSourceAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "sourceId", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "reason", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "failureCount", "type": "uint256"}], "name": "PriceSourceFailed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "sourceId", "type": "uint256"}], "name": "PriceSourceRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "sourceId", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "active", "type": "bool"}, {"indexed": false, "internalType": "uint256", "name": "weight", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "maxFailures", "type": "uint256"}], "name": "PriceSourceUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "MAX_HISTORY_SIZE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_PRICE_DEVIATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_SOURCES", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MIN_SOURCES_REQUIRED", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PRICE_STALENESS_THRESHOLD", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "price", "type": "uint256"}], "name": "activateEmergencyMode", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "oracle", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "weight", "type": "uint256"}, {"internalType": "uint256", "name": "maxFailures", "type": "uint256"}], "name": "addPriceSource", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256[]", "name": "sourceIds", "type": "uint256[]"}, {"internalType": "bool[]", "name": "activeStates", "type": "bool[]"}], "name": "batchUpdateSources", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "deactivateEmergencyMode", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "emergencyMode", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "emergencyPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "emergencyPriceTimestamp", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getActiveSourceCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getAggregatorStatus", "outputs": [{"internalType": "uint256", "name": "totalSources", "type": "uint256"}, {"internalType": "uint256", "name": "activeSources", "type": "uint256"}, {"internalType": "bool", "name": "isEmergencyMode", "type": "bool"}, {"internalType": "uint256", "name": "lastUpdateTime", "type": "uint256"}, {"internalType": "uint256", "name": "latestPriceValue", "type": "uint256"}, {"internalType": "uint256", "name": "confidence", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getAllSources", "outputs": [{"internalType": "address[]", "name": "oracles", "type": "address[]"}, {"internalType": "string[]", "name": "names", "type": "string[]"}, {"internalType": "uint256[]", "name": "weights", "type": "uint256[]"}, {"internalType": "bool[]", "name": "activeStates", "type": "bool[]"}, {"internalType": "uint256[]", "name": "lastPrices", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "failureCounts", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getLastUpdateTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getLatestPrice", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "limit", "type": "uint256"}], "name": "getPriceHistory", "outputs": [{"components": [{"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "confidence", "type": "uint256"}, {"internalType": "uint256", "name": "sourceCount", "type": "uint256"}], "internalType": "struct HAOXPriceAggregatorV2.PriceData[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "sourceId", "type": "uint256"}], "name": "getSourceDetails", "outputs": [{"internalType": "address", "name": "oracle", "type": "address"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "weight", "type": "uint256"}, {"internalType": "bool", "name": "active", "type": "bool"}, {"internalType": "uint256", "name": "lastUpdate", "type": "uint256"}, {"internalType": "uint256", "name": "lastPrice", "type": "uint256"}, {"internalType": "uint256", "name": "failureCount", "type": "uint256"}, {"internalType": "uint256", "name": "maxFailures", "type": "uint256"}, {"internalType": "bool", "name": "isStale", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "latestPrice", "outputs": [{"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "confidence", "type": "uint256"}, {"internalType": "uint256", "name": "sourceCount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "priceHistory", "outputs": [{"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "confidence", "type": "uint256"}, {"internalType": "uint256", "name": "sourceCount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "priceSources", "outputs": [{"internalType": "address", "name": "oracle", "type": "address"}, {"internalType": "uint256", "name": "weight", "type": "uint256"}, {"internalType": "bool", "name": "active", "type": "bool"}, {"internalType": "uint256", "name": "lastUpdate", "type": "uint256"}, {"internalType": "uint256", "name": "lastPrice", "type": "uint256"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "failureCount", "type": "uint256"}, {"internalType": "uint256", "name": "maxFailures", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "sourceId", "type": "uint256"}], "name": "removePriceSource", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "sourceId", "type": "uint256"}], "name": "resetSourceFailures", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "sourceCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "updateAggregatedPrice", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "sourceId", "type": "uint256"}, {"internalType": "bool", "name": "active", "type": "bool"}, {"internalType": "uint256", "name": "weight", "type": "uint256"}, {"internalType": "uint256", "name": "maxFailures", "type": "uint256"}], "name": "updatePriceSource", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x608080604052346100885733156100725760008054336001600160a01b03198216811783556040519290916001600160a01b0316907f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e09080a36001805560ff19600c5416600c5561274e908161008e8239f35b631e4fbdf760e01b815260006004820152602490fd5b600080fdfe608080604052600436101561001357600080fd5b60003560e01c9081630905f56014611f5d575080631d1942ae14611e025780632ee2f12814611de65780633f4ba83a14611d7c5780635115e0a114611c655780635c975abb14611c4257806364aefc0614611c26578063715018a614611bcd5780637bcbb651146117205780637f4e8ee81461170257806382284592146115b55780638456cb591461155b5780638da5cb5b146115325780638e15f4731461151757806396f277b7146114fa57806399b20eaf14611404578063a360731c146113b2578063a3e6ba9414611378578063aba465f31461117c578063b27a04841461111b578063b7f8832014611100578063be3b078c14610df1578063bfa32d8314610dd3578063c106625114610db7578063c607628014610d99578063c7422df214610d76578063ca9cfb5014610595578063d006f57d146104e6578063d9c5e6d714610438578063f25b9caa1461041b578063f2fde38b1461038d578063f7b225ad146102f15763fd1a98a51461018a57600080fd5b346102ec576020806003193601126102ec57600435906005548083116102e4575b6101b4836124d5565b926101c26040519485612117565b808452601f196101d1826124d5565b018360005b8281106102b25750505060005b81811061024557604080518581528651818701819052878701928201908760005b8281106102115784840385f35b8551805185528083015185840152604080820151908601526060908101519085015294810194608090930192600101610204565b8061026461025f6102ad9361025a868861234d565b612533565b612004565b50600360405191610274836120df565b80548352600181015488840152600281015460408401520154606082015261029c828861251f565b526102a7818761251f565b5061228b565b6101e3565b6040516102be816120df565b6000815260008382015260006040820152600060608201528282890101520184906101d6565b9150816101ab565b600080fd5b346102ec5760203660031901126102ec576004356000526003602052604060002060018060a01b0381541660018201549160ff60028201541661037e6003830154600484015461034360058601612139565b9160076006870154960154946040519889988952602089015215156040880152606087015260808601526101008060a0870152850190611f93565b9160c084015260e08301520390f35b346102ec5760203660031901126102ec576103a6611f7d565b6103ae6121df565b6001600160a01b0390811690811561040257600054826bffffffffffffffffffffffff60a01b821617600055167f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0600080a3005b604051631e4fbdf760e01b815260006004820152602490fd5b346102ec5760003660031901126102ec576020604051610e108152f35b346102ec5760203660031901126102ec576004356104546121df565b80156104a15760407f9a412031d3d98959a6d0c98cda92174f7b51270febfb0a6ec88aa632016511f391600160ff19600c541617600c5580600a5542600b558151908152426020820152a1005b60405162461bcd60e51b815260206004820152601760248201527f496e76616c696420656d657267656e63792070726963650000000000000000006044820152606490fd5b346102ec5760203660031901126102ec576004356105026121df565b61050f60045482106122cd565b600081815260036020526040902054610532906001600160a01b0316151561230d565b806000526003602052604060002060006006820155600281019081549060ff82161561055a57005b6000805160206126f983398151915292600160609360ff191617905560076001820154910154604051916001835260208301526040820152a2005b346102ec5760003660031901126102ec57600260015414610d645760026001556105bd6124b7565b60ff600c5416610d27576004546000806105df6105d9846124ed565b936124ed565b60009390845b60045486101561096f5785600052600360205260406000209560ff600288015416156109625760008060405161061a816120c2565b89546001600160a01b0316815260018a8101546020830152604082015260038a0154606082015260048a0154608082015261065760058b01612139565b60a082015260068a015460c082015260078a015460e082015251604051638e15f47360e01b60208201908152600482526001600160a01b03909216919061069d816120fb565b51915afa6106a9612560565b9080610956575b1561094c576020818051810103126102ec57602001519660015b1561090f57816000526003602052600080604081206007604051916106ee836120c2565b80546001600160a01b0316835260018101546020840152600281015460ff1615156040840152600381015460608401526004810154608084015261073460058201612139565b60a0840152600681015460c0840152015460e0820152516040516305bfc41960e51b60208201908152600482526001600160a01b039092169190610777816120fb565b51915afa610783612560565b9080610903575b156108f9576020818051810103126102ec5760200151976001985b98156108f1575b610e106107b98a4261234d565b116108b6578015968780156108a5575b61086057816107d8868961251f565b526107f3600184015495866107ed828a61251f565b5261228b565b9782868102048614171561084a578461081561081b9261083597850290612533565b99612533565b9860038301556004820155600681015461083d575061228b565b9493926105e5565b60066000910155876102a7565b634e487b7160e01b600052601160045260246000fd5b97985050506108359194506108a060405161087a816120fb565b6013815272496e76616c69642070726963652076616c756560681b602082015282612590565b61228b565b50670de0b6b3a764000082116107c9565b505061083591949596506108a06040516108cf816120fb565b600f81526e507269636520746f6f207374616c6560881b602082015282612590565b4298506107ac565b50600097426107a5565b5060208151101561078a565b5061083591949596506108a0604051610927816120fb565b601281527113dc9858db194818d85b1b0819985a5b195960721b602082015282612590565b50600096876106ca565b506020815110156106b0565b610835919495965061228b565b9193905061098b816002841015946109868661246b565b612540565b9360005b838110610bfd57505060148202821583820460141481171561084a5760508211610bf4575b8215610be6576064840290848204606414171561084a576109e7926109d891612540565b905b60148211610bdd57612533565b6064811115610bd357506109fc60649261246b565b806060604051610a0b816120df565b85815242602082015284604082015201528260065542600755816008558060095560405192610a39846120df565b80845242602085015282604085015281606085015260059384546064811015600014610b69575060005b8554600019810190811161084a57811015610af1576001810180821161084a57610a8c90612004565b5090610a9781612004565b610adb5782610aae938203610ab3575b505061228b565b610a63565b8054825560018101546001830155600281015460028301556003809101549101558780610aa7565b634e487b7160e01b600052600060045260246000fd5b50909192935493600019850194851161084a57610b3b610b6092610b357f2ce533425095d06292e65c708e4dbd78ba0e82d03aa7affbbd3453d9439d4cb097612004565b90612643565b6040519384934290859094939260609260808301968352602083015260408201520152565b0390a160018055005b68010000000000000000819693949592961015610bbd577f2ce533425095d06292e65c708e4dbd78ba0e82d03aa7affbbd3453d9439d4cb095610b3582610b60956001610bb895019055612004565b610b3b565b634e487b7160e01b600052604160045260246000fd5b6109fc909261246b565b60149150612533565b506109e791506000906109da565b605091506109b4565b85610c08828461251f565b511115610cec57610c2386610c1d838561251f565b5161234d565b906127109182810292818404149015171561084a57610c4587610c5493612540565b6101f48111610c59575061228b565b61098f565b6103e8610c66838661251f565b5191604051928352837fc584b1b0341f9ff40b6cae2974007f878f1735038aa0e777fdd58dbee169972c60606020958d87820152846040820152a211156102a757610ce6907f507269636520646576696174696f6e20746f6f2068696768000000000000000060405191610cd9836120fb565b6018835282015282612590565b876102a7565b610d00610cf9828461251f565b518761234d565b906127109182810292818404149015171561084a57610d2287610c5493612540565b610c45565b60405162461bcd60e51b8152602060048201526015602482015274456d657267656e6379206d6f64652061637469766560581b6044820152606490fd5b604051633ee5aeb560e01b8152600490fd5b346102ec5760003660031901126102ec576020610d9161266d565b604051908152f35b346102ec5760003660031901126102ec576020600454604051908152f35b346102ec5760003660031901126102ec57602060405160648152f35b346102ec5760003660031901126102ec576020600b54604051908152f35b346102ec5760003660031901126102ec57600454610e0e816124d5565b610e1b6040519182612117565b818152610e27826124d5565b602082019290601f19908101368537610e3f826124d5565b90610e4d6040519283612117565b82825280610e5a846124d5565b0160005b8181106110ed575050610e70836124ed565b91610e7a846124d5565b90610e886040519283612117565b848252610e94856124d5565b9383602084019501368637610ea8866124ed565b93610eb2876124ed565b9660005b818110610fe757505060405198899860c08a019060c08b525180915260e08a01919060005b818110610fc55750505088810360208a0152835190818152602081016020808460051b8401019601936000925b848410610f8a5750505050505081610f2991886020940360408a0152612054565b918683036060880152519182815201929060005b818110610f6f5750505081610f5d9185610f6b9594036080870152612054565b9083820360a0850152612054565b0390f35b82511515855287965060209485019490920191600101610f3d565b9193959681939598999a9b9c506020610fae82600194868495030188528b51611f93565b990194019401918c9b9a9998979695939491610f08565b82516001600160a01b031684528c9b5060209384019390920191600101610edb565b806110df9160009c95969798999a9b9c52600360205260c060406000208d60405191611012836120c2565b80546001600160a01b03168352600181015460208401908152600282015460ff16151560408501908152600383015460608601526004830154608086015292909190600761106260058401612139565b60a0870190815260068401548888015292015460e086015284516001600160a01b03169061109190889061251f565b525161109d868d61251f565b526110a8858c61251f565b50516110b4858b61251f565b525115156110c2848c61251f565b5260808101516110d2848d61251f565b5201516107ed828d61251f565b999897969594939299610eb6565b6060602082860181019190915201610e5e565b346102ec5760003660031901126102ec576020610d91612455565b346102ec5760203660031901126102ec576004356005548110156102ec5761114290612004565b508054610f6b6001830154926003600282015491015490604051948594859094939260609260808301968352602083015260408201520152565b346102ec5760403660031901126102ec5767ffffffffffffffff6004358181116102ec576111ae903690600401611fd3565b916024359081116102ec576111c7903690600401611fd3565b6111cf6121df565b80840361133b5760005b8481106111e257005b806111f16112059287876126db565b35600454118061130f575b61120a5761228b565b6111d9565b61121d6112188285876126db565b6126eb565b6112288288886126db565b356000526000805160206126f983398151915260039161126060209184835260026040600020019060ff801983541691151516179055565b61126e61121885888a6126db565b6112ee575b61127e848a8a6126db565b359261128e61121886898b6126db565b9061129a868c8c6126db565b35600052808352600160406000200154926112b6878d8d6126db565b35600052526112e66007604060002001546040519384938460409194939260608201951515825260208201520152565b0390a261228b565b6112f9848a8a6126db565b3560005282815260006006604082200155611273565b5061131b8187876126db565b356000908152600360205260409020546001600160a01b031615156111fc565b60405162461bcd60e51b8152602060048201526015602482015274082e4e4c2f240d8cadccee8d040dad2e6dac2e8c6d605b1b6044820152606490fd5b346102ec5760003660031901126102ec57600654600754600854600954604080519485526020850193909352918301526060820152608090f35b346102ec5760003660031901126102ec5760c06004546113d061266d565b60ff600c541660075460065491600854936040519586526020860152151560408501526060840152608083015260a0820152f35b346102ec5760003660031901126102ec5761141d6121df565b600c5460ff8116156114b557600261143361266d565b106114705760ff1916600c556000600a556000600b557f70b9dcaceb756943ac1a4f48b8df9ea82fbb012b87e405cba4ed77d1ded0df41600080a1005b60405162461bcd60e51b815260206004820152601b60248201527f496e73756666696369656e742061637469766520736f757263657300000000006044820152606490fd5b60405162461bcd60e51b815260206004820152601960248201527f456d657267656e6379206d6f6465206e6f7420616374697665000000000000006044820152606490fd5b346102ec5760003660031901126102ec5760206040516101f48152f35b346102ec5760003660031901126102ec576020610d9161235a565b346102ec5760003660031901126102ec576000546040516001600160a01b039091168152602090f35b346102ec5760003660031901126102ec576115746121df565b61157c6124b7565b600160ff1960025416176002557f62e78cea01bee320cd4e420270b5ea74000d11b0c9f74754ebdbfc544b05a2586020604051338152a1005b346102ec5760203660031901126102ec576004356115d660045482106122cd565b6000818152600360205260409020546001600160a01b0391906115fc908316151561230d565b6000526003602052604060002060405191611616836120c2565b8082541683526001820154926020810193845260ff60028401541691604082019215158352600384015490606083019182526004850154946080840195865261166160058201612139565b9560a08501968752600760068301549260c0870193845201549260e0860193845284519586151596876116ea575b50511696519751955115159351905191519251936116bf604051998a998a526101208060208c01528a0190611f93565b9660408901526060880152608087015260a086015260c085015260e084015215156101008301520390f35b610e109197506116fa904261234d565b11958a61168f565b346102ec5760003660031901126102ec576020600a54604051908152f35b346102ec5760803660031901126102ec57611739611f7d565b67ffffffffffffffff602435116102ec573660236024350112156102ec5767ffffffffffffffff60243560040135116102ec5736602480356004013581350101116102ec576117866121df565b6001600160a01b03168015611b8f576044908135151580611b83575b6117ab9061220b565b6024356004013515611b4f5760045491600a831015611b18576064928335151580611b0c575b6117da90612248565b60005b818110611aa25750604051936117f2856120c2565b838552823560208601526001604086015260006060860152600060808601526118206024356004013561229a565b61182d6040519182612117565b6024803560048101358084529101602083013760006020602435600401358301015260a0860152600060c08601523560e08501526000526003602052604060002060018060a01b038451166bffffffffffffffffffffffff60a01b825416178155602084015160018201556118b760408501511515600283019060ff801983541691151516179055565b6060840151600382015560808401516004820155600581019360a081015194855167ffffffffffffffff8111610bbd576118f18254612088565b601f8111611a65575b506020601f82116001146119d157926007928260e0937f38ff127da5d2ff076d16b67a13448a98e15bae5648c6029d8df54246f37961b2999897966119c19b6000926119c6575b50508160011b916000199060031b1c19161790555b60c08101516006850155015191015560045492839160405190604082526024356004013560408301526024356004013560248035016060840137600060606024356004013584010152356020820152606081601f19601f6024356004013501168101030190a361228b565b600455005b015190508b80611941565b601f198216978360005260206000209860005b818110611a4d5750836119c19a7f38ff127da5d2ff076d16b67a13448a98e15bae5648c6029d8df54246f37961b29a999897946007979460e09760019510611a34575b505050811b019055611956565b015160001960f88460031b161c191690558b8080611a27565b838301518b556001909a0199602093840193016119e4565b611a9290836000526020600020601f840160051c81019160208510611a98575b601f0160051c01906122b6565b876118fa565b9091508190611a85565b6000818152600360209081526040909120546001600160a01b03168514611ad25750611acd9061228b565b6117dd565b8590744f7261636c6520616c72656164792065786973747360581b856040519262461bcd60e51b8452600484015260156024840152820152fd5b50600a843511156117d1565b6064906f546f6f206d616e7920736f757263657360801b6040519162461bcd60e51b83526020600484015260106024840152820152fd5b60405162461bcd60e51b815260206004820152600d60248201526c13985b59481c995c5d5a5c9959609a1b81840152606490fd5b506064823511156117a2565b60405162461bcd60e51b8152602060048201526016602482015275496e76616c6964206f7261636c65206164647265737360501b6044820152606490fd5b346102ec5760003660031901126102ec57611be66121df565b600080546001600160a01b0319811682556001600160a01b03167f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e08280a3005b346102ec5760003660031901126102ec576020604051600a8152f35b346102ec5760003660031901126102ec57602060ff600254166040519015158152f35b346102ec5760803660031901126102ec5760043560243580151581036102ec576000805160206126f98339815191529060443590611d54606435611ca76121df565b611cb460045487106122cd565b600086815260036020526040902054611cd7906001600160a01b0316151561230d565b83151580611d71575b611ce99061220b565b80151580611d66575b611cfb90612248565b8560005260036020526040600020611d2284600283019060ff801983541691151516179055565b84600182015583826007830155611d59575b506040519384938460409194939260608201951515825260208201520152565b0390a2005b6006600091015586611d34565b50600a811115611cf2565b506064841115611ce0565b346102ec5760003660031901126102ec57611d956121df565b60025460ff811615611dd45760ff19166002557f5db9ee0a495bf2e6ff9c91a7834c1ba4fdd244a5e8aa4e537bd38aeae4b073aa6020604051338152a1005b604051638dfc202b60e01b8152600490fd5b346102ec5760003660031901126102ec57602060405160028152f35b346102ec5760203660031901126102ec57600435611e1e6121df565b611e2b60045482106122cd565b600081815260036020526040902054611e4e906001600160a01b0316151561230d565b6002611e5861266d565b1115611f0d57806000526003602052600060076040822082815582600182015582600282015582600382015582600482015560058101611e988154612088565b9081611ed0575b505082600682015501557fc3117ef59eb45536ec6ac8c0952fe2229522738d184fc1b0fc43d0de078f35e9600080a2005b81601f869311600114611ee75750555b8480611e9f565b908083918252611f06601f60208420940160051c8401600185016122b6565b5555611ee0565b60405162461bcd60e51b815260206004820152602260248201527f43616e6e6f742072656d6f766520736f757263652062656c6f77206d696e696d604482015261756d60f01b6064820152608490fd5b346102ec5760003660031901126102ec5760209060ff600c541615158152f35b600435906001600160a01b03821682036102ec57565b919082519283825260005b848110611fbf575050826000602080949584010152601f8019910116010190565b602081830181015184830182015201611f9e565b9181601f840112156102ec5782359167ffffffffffffffff83116102ec576020808501948460051b0101116102ec57565b60055481101561203e57600560005260021b7f036b6384b5eca791c62761152d0c79bb0604c104a5fb6f4eb0703f3154bb3db00190600090565b634e487b7160e01b600052603260045260246000fd5b90815180825260208080930193019160005b828110612074575050505090565b835185529381019392810192600101612066565b90600182811c921680156120b8575b60208310146120a257565b634e487b7160e01b600052602260045260246000fd5b91607f1691612097565b610100810190811067ffffffffffffffff821117610bbd57604052565b6080810190811067ffffffffffffffff821117610bbd57604052565b6040810190811067ffffffffffffffff821117610bbd57604052565b90601f8019910116810190811067ffffffffffffffff821117610bbd57604052565b906040519182600082549261214d84612088565b9081845260019485811690816000146121bc5750600114612179575b505061217792500383612117565b565b9093915060005260209081600020936000915b8183106121a457505061217793508201013880612169565b8554888401850152948501948794509183019161218c565b91505061217794506020925060ff191682840152151560051b8201013880612169565b6000546001600160a01b031633036121f357565b60405163118cdaa760e01b8152336004820152602490fd5b1561221257565b60405162461bcd60e51b815260206004820152600e60248201526d125b9d985b1a59081dd95a59da1d60921b6044820152606490fd5b1561224f57565b60405162461bcd60e51b8152602060048201526014602482015273496e76616c6964206d6178206661696c7572657360601b6044820152606490fd5b600019811461084a5760010190565b67ffffffffffffffff8111610bbd57601f01601f191660200190565b8181106122c1575050565b600081556001016122b6565b156122d457565b60405162461bcd60e51b8152602060048201526011602482015270125b9d985b1a59081cdbdd5c98d9481251607a1b6044820152606490fd5b1561231457565b60405162461bcd60e51b8152602060048201526011602482015270536f75726365206e6f742065786973747360781b6044820152606490fd5b9190820391821161084a57565b60ff600c54166123f65760075480156123bc5761237a610e10914261234d565b116123855760065490565b60405162461bcd60e51b815260206004820152600f60248201526e507269636520746f6f207374616c6560881b6044820152606490fd5b60405162461bcd60e51b81526020600482015260126024820152714e6f20707269636520617661696c61626c6560701b6044820152606490fd5b611c20612405600b544261234d565b1161241057600a5490565b60405162461bcd60e51b815260206004820152601960248201527f456d657267656e637920707269636520746f6f207374616c65000000000000006044820152606490fd5b60ff600c54166124655760075490565b600b5490565b1561247257565b60405162461bcd60e51b815260206004820152601a60248201527f496e73756666696369656e742076616c696420736f75726365730000000000006044820152606490fd5b60ff600254166124c357565b60405163d93c066560e01b8152600490fd5b67ffffffffffffffff8111610bbd5760051b60200190565b906124f7826124d5565b6125046040519182612117565b8281528092612515601f19916124d5565b0190602036910137565b805182101561203e5760209160051b010190565b9190820180921161084a57565b811561254a570490565b634e487b7160e01b600052601260045260246000fd5b3d1561258b573d906125718261229a565b9161257f6040519384612117565b82523d6000602084013e565b606090565b9081600052600360205260408060002090837f4ff232af2cb70106bbe8aba00f6b6406f745624a38b53a602099fcecbef91fcc6125e960068501956125d5875461228b565b809755845191829186835286830190611f93565b8760208301520390a2600782015480931015612606575b50505050565b6000805160206126f98339815191529260018360026060950160ff198154169055015491805192600084526020840152820152a238808080612600565b9190610adb5760608160039251845560208101516001850155604081015160028501550151910155565b600080600454905b81811061268157505090565b806000526003602052604060002060ff60028201541690816126c7575b506126b2575b6126ad9061228b565b612675565b916126bf6126ad9161228b565b9290506126a4565b546001600160a01b0316151590503861269e565b919081101561203e5760051b0190565b3580151581036102ec579056feb24c93250d6e35f918ef7da3c0874442b61a1f8c3d15f3888ac41639f51ecd1ba26469706673582212206552d7f28a438ef2b5942ab7f29e78d17d741178c73388d9ae237d268ff2354864736f6c63430008140033", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}