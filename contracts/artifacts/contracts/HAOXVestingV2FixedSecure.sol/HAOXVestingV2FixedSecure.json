{"_format": "hh-sol-artifact-1", "contractName": "HAOXVestingV2FixedSecure", "sourceName": "contracts/HAOXVestingV2FixedSecure.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_haoxToken", "type": "address"}, {"internalType": "address", "name": "_priceOracle", "type": "address"}, {"internalType": "address", "name": "_projectWallet", "type": "address"}, {"internalType": "address", "name": "_communityWallet", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "signer", "type": "address"}], "name": "EmergencySignerAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "signer", "type": "address"}], "name": "EmergencySignerRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "approver", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "approvalCount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "requiredSignatures", "type": "uint256"}], "name": "EmergencyWithdrawApproved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "canceller", "type": "address"}, {"indexed": false, "internalType": "string", "name": "reason", "type": "string"}], "name": "EmergencyWithdrawCancelled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}], "name": "EmergencyWithdrawExecuted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "reason", "type": "string"}, {"indexed": true, "internalType": "address", "name": "requester", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "requestTime", "type": "uint256"}], "name": "EmergencyWithdrawRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "currentPrice", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "targetPrice", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "conditionMet", "type": "bool"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "PriceChecked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "price", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "PriceConditionMet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "price", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "PriceConditionReset", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldRequired", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newRequired", "type": "uint256"}], "name": "RequiredSignaturesUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "triggerPrice", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "projectTokens", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "communityTokens", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "RoundUnlocked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "string", "name": "walletType", "type": "string"}, {"indexed": false, "internalType": "address", "name": "oldWallet", "type": "address"}, {"indexed": false, "internalType": "address", "name": "newWallet", "type": "address"}], "name": "WalletUpdated", "type": "event"}, {"inputs": [], "name": "BASE_PRICE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "COMMUNITY_SHARE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "EMERGENCY_DELAY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "FIRST_ROUND_TOKENS", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_EMERGENCY_AMOUNT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_EMERGENCY_PERCENTAGE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PRICE_MAINTENANCE_PERIOD", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PROJECT_SHARE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "ROUND_12_21_INCREASE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "ROUND_22_31_INCREASE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "ROUND_2_11_INCREASE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOKENS_PER_ROUND", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOTAL_ROUNDS", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}], "name": "addEmergencySigner", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "approvalCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "requestId", "type": "bytes32"}], "name": "approveEmergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32[]", "name": "requestIds", "type": "bytes32[]"}], "name": "batchApproveRequests", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "requestId", "type": "bytes32"}], "name": "canExecuteRequest", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"internalType": "string", "name": "reason", "type": "string"}], "name": "cancelEmergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "checkPriceCondition", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "communityWallet", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "currentRound", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}, {"internalType": "address", "name": "", "type": "address"}], "name": "emergencyApprovals", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "emergencyPause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "emergencyRequests", "outputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "requestTime", "type": "uint256"}, {"internalType": "bool", "name": "executed", "type": "bool"}, {"internalType": "bool", "name": "cancelled", "type": "bool"}, {"internalType": "string", "name": "reason", "type": "string"}, {"internalType": "address", "name": "requester", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "emergencySigners", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "emergencyWithdraw", "outputs": [], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "requestId", "type": "bytes32"}], "name": "executeEmergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getEmergencyStatistics", "outputs": [{"internalType": "uint256", "name": "totalRequests", "type": "uint256"}, {"internalType": "uint256", "name": "pendingCount", "type": "uint256"}, {"internalType": "uint256", "name": "executedCount", "type": "uint256"}, {"internalType": "uint256", "name": "cancelledCount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getPendingRequests", "outputs": [{"internalType": "bytes32[]", "name": "", "type": "bytes32[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"internalType": "uint256", "name": "limit", "type": "uint256"}], "name": "getPriceCheckHistory", "outputs": [{"components": [{"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "uint256", "name": "targetPrice", "type": "uint256"}, {"internalType": "bool", "name": "conditionMet", "type": "bool"}], "internalType": "struct HAOXVestingV2Fixed.PriceCheck[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "requestId", "type": "bytes32"}], "name": "getRequestDetails", "outputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "requestTime", "type": "uint256"}, {"internalType": "bool", "name": "executed", "type": "bool"}, {"internalType": "bool", "name": "cancelled", "type": "bool"}, {"internalType": "string", "name": "reason", "type": "string"}, {"internalType": "address", "name": "requester", "type": "address"}, {"internalType": "uint256", "name": "timeRemaining", "type": "uint256"}, {"internalType": "uint256", "name": "currentApprovals", "type": "uint256"}, {"internalType": "uint256", "name": "requiredApprovals", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "roundNumber", "type": "uint256"}], "name": "getRoundInfo", "outputs": [{"internalType": "uint256", "name": "triggerPrice", "type": "uint256"}, {"internalType": "bool", "name": "priceConditionMet", "type": "bool"}, {"internalType": "bool", "name": "unlocked", "type": "bool"}, {"internalType": "uint256", "name": "priceReachedTime", "type": "uint256"}, {"internalType": "uint256", "name": "unlockTime", "type": "uint256"}, {"internalType": "uint256", "name": "projectTokens", "type": "uint256"}, {"internalType": "uint256", "name": "communityTokens", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getSecurityStatus", "outputs": [{"internalType": "bool", "name": "isPaused", "type": "bool"}, {"internalType": "uint256", "name": "pendingEmergencyRequests", "type": "uint256"}, {"internalType": "uint256", "name": "activeSigners", "type": "uint256"}, {"internalType": "uint256", "name": "requiredSigs", "type": "uint256"}, {"internalType": "uint256", "name": "oldestPendingRequest", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getSignerCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getUnlockProgress", "outputs": [{"internalType": "uint256", "name": "currentPrice", "type": "uint256"}, {"internalType": "uint256", "name": "nextRoundNumber", "type": "uint256"}, {"internalType": "uint256", "name": "nextRoundTriggerPrice", "type": "uint256"}, {"internalType": "bool", "name": "nextRoundPriceConditionMet", "type": "bool"}, {"internalType": "uint256", "name": "timeRemaining", "type": "uint256"}, {"internalType": "uint256", "name": "priceReachedTime", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getUnlockStatistics", "outputs": [{"internalType": "uint256", "name": "totalUnlockedRounds", "type": "uint256"}, {"internalType": "uint256", "name": "totalUnlockedTokens", "type": "uint256"}, {"internalType": "uint256", "name": "totalProjectTokens", "type": "uint256"}, {"internalType": "uint256", "name": "totalCommunityTokens", "type": "uint256"}, {"internalType": "uint256", "name": "remainingTokens", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "haoxToken", "outputs": [{"internalType": "contract HAOXTokenV2", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"internalType": "address", "name": "signer", "type": "address"}], "name": "hasApproved", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "pendingRequests", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "priceCheckHistory", "outputs": [{"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "uint256", "name": "targetPrice", "type": "uint256"}, {"internalType": "bool", "name": "conditionMet", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "priceOracle", "outputs": [{"internalType": "contract HAOXPriceOracleV2", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "projectWallet", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}], "name": "remove<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "string", "name": "reason", "type": "string"}], "name": "requestEmergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "requiredSignatures", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_required", "type": "uint256"}], "name": "setRequiredSignatures", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "totalEmergencyRequests", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "unlockRounds", "outputs": [{"internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"internalType": "uint256", "name": "triggerPrice", "type": "uint256"}, {"internalType": "uint256", "name": "priceReachedTime", "type": "uint256"}, {"internalType": "bool", "name": "priceConditionMet", "type": "bool"}, {"internalType": "bool", "name": "unlocked", "type": "bool"}, {"internalType": "uint256", "name": "unlockTime", "type": "uint256"}, {"internalType": "uint256", "name": "projectTokens", "type": "uint256"}, {"internalType": "uint256", "name": "communityTokens", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_newWallet", "type": "address"}], "name": "updateCommunityWallet", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_newO<PERSON>le", "type": "address"}], "name": "updatePriceOracle", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_newWallet", "type": "address"}], "name": "updateProjectWallet", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x60a034620004a157601f62003a3638819003828101601f1916840193916001600160401b03851183861017620004a6578160809284926040978852833981010312620004a1576200005081620004dd565b60209062000060828401620004dd565b936200007c606062000074888701620004dd565b9501620004dd565b94600194600096868855331562000489578654610100600160a81b0319811633600881811b610100600160a81b0316929092178a558b51956001600160a01b039390921c83167f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e08c80a3816004978a895516948515620004485750811692831562000405578116918215620003c257169283156200037f57899586948a948a948a9460805260018060a01b03199182600254161760025581600554161760055560065416176006556204a3e49562000153620004bc565b84815260068585888185018c81528b8087014281526060880192868452608089019287845260a08a0195428752600360c08c01986aa56fa5b99019a5c80000008a5260e08d019a6af8277896582678ac0000008c528352522098518955518c890155516002880155620001d96003880192511515839060ff801983541691151516179055565b51815461ff00191690151560081b61ff001617905551848701555160058401555191015560025b600b811115620003435750600c5b60158111156200030757506016915b81831115620002b0575050507fe10a705edb4af55d916591ab3ae6d73d13195159890a14a90b10ae1117f9ea379450338352600b81528383208260ff1982541617905581600c55835192337fc10a20171bb773348d4407b0ed548c3a68f0c94958d92651ba3885111617686c8280a28352820152a15161345d9081620005d98239608051818181611ee80152612cf40152f35b91939550919395607890818102918183041490151715620002f4576064620002e6910493620002e0858262000518565b620004f2565b90918795939694926200021d565b634e487b7160e01b875260118252602487fd5b939550919395609690818102918183041490151715620002f457606462000336910493620002e0858262000518565b918795939694926200020e565b93955091939560c890818102918183041490151715620002f457606462000372910493620002e0858262000518565b9187959396949262000200565b895162461bcd60e51b8152808701889052601860248201527f496e76616c696420636f6d6d756e6974792077616c6c657400000000000000006044820152606490fd5b8a5162461bcd60e51b8152808801899052601660248201527f496e76616c69642070726f6a6563742077616c6c6574000000000000000000006044820152606490fd5b8a5162461bcd60e51b8152808801899052601660248201527f496e76616c6964206f7261636c652061646472657373000000000000000000006044820152606490fd5b62461bcd60e51b8152878101899052601560248201527f496e76616c696420746f6b656e206164647265737300000000000000000000006044820152606490fd5b8851631e4fbdf760e01b815260048101899052602490fd5b600080fd5b634e487b7160e01b600052604160045260246000fd5b6040519061010082016001600160401b03811183821017620004a657604052565b51906001600160a01b0382168203620004a157565b6000198114620005025760010190565b634e487b7160e01b600052601160045260246000fd5b60069062000525620004bc565b928184526020840190815260408401906000918281526060860190838252608087019084825260a0880193858552604060c08a01966a31a17e847807b1bc000000885260e08b01986a4a723dc6b40b8a9a0000008a52815260036020522098518955516001890155516002880155620005b16003880192511515839060ff801983541691151516179055565b51815461ff00191690151560081b61ff00161790555160048501555160058401555191015556fe6080604052600436101561001257600080fd5b60e0600035811c80630557ac41146127475780630b045c671461272b57806311defc3a1461270f5780631766ef7e1461260e578063231bf84814611f1757806323cdc3f11461182257806325dbec9d14611ed25780632630c12f14611ea9578063309bfd9514611cae57806339d1fc8214611bd35780633c652a9a14611b9c5780633d718a7f14611b765780633f4ba83a14611b1f57806349b3f7e014611a9f5780634a126ad914611a835780634c99c54814611a5b57806351858e27146119b05780635c975abb1461198d578063617258201461143f578063687404b21461196a578063715018a614611909578063719276281461186f57806372bf3bbc1461182257806373efc9f21461172b578063792c2cd21461160b5780637d2b9cc0146114fa57806380a1f7121461144457806382944e2d1461143f5780638456cb59146113e657806384eb60921461138e57806388c3ffb0146112bd5780638a19c8bc1461129f5780638d068043146112815780638d1ba346146112555780638da5cb5b146112285780639574ab4d1461120157806395ccea67146111965780639ad3ec4814610e54578063a6ce120a14610db8578063a8a76f8f14610d9a578063b715be8114610d7e578063beb08ab914610d55578063c757483914610d2c578063ce8dc38814610d10578063cf9d226e14610ceb578063d0f694cb14610cac578063d2391aa914610c90578063d480b60814610c74578063db77c2a814610af2578063dffd261a14610ad6578063e19df5c6146109b5578063e4b1a79514610842578063ee6bce70146106e4578063f2fde38b1461064b578063f328b00d146104c9578063f4d8345514610467578063f86325ed146104495763ff5f1e911461029b57600080fd5b3461043157600036600319011261043157600254604051638e15f47360e01b8152906000908190819081906020908190879060049082906001600160a01b03165afa95861561043d57600096610409575b5060045496600188018098116103ea57601f88111561032c575b509560c096604051968752860152604085015215156060840152608083015260a0820152f35b9250935050846000526003835260406000209260405161034b81612850565b845481526001850154908183820152600286015493846040830152600660038801549760ff808a161515998a606087015260081c1615156080850152600481015460a0850152600581015460c08501520154910152908492809580610400575b15610306576103bc9194504261309c565b62093a8096908781106103d9575060c0965060005b939096610306565b87039687116103ea5760c0966103d1565b634e487b7160e01b600052601160045260246000fd5b508015156103ab565b9080965081813d8311610436575b610421818361286d565b81010312610431575194386102ec565b600080fd5b503d610417565b6040513d6000823e3d90fd5b346104315760003660031901126104315760206040516204a3e48152f35b3461043157610475366129cf565b906000526007602052604060002080548210156104315760809161049891612a3a565b5080549060018101549060ff6003600283015492015416916040519384526020840152604083015215156060820152f35b346104315760403660031901126104315760043560243567ffffffffffffffff8111610431576104fd9036906004016129a1565b919033600052602092600b845261051b60ff60406000205416613148565b82600052600880855261053760026040600020015415156131b5565b8360005280855261055360ff60036040600020015416156131f5565b8360005280855261057160ff600360406000200154831c1615613241565b8360005280855260406000209060018060a01b03806005840154163314918215610639575b5050156105f457600301805461ff0019166101001790557fc64f4a591aff10c4e310f6173212619124bf31596b830ecc095d410ae28d79fc91906105d984613281565b6105ef6040519283928784523397840191613194565b0390a3005b60405162461bcd60e51b815260048101869052601860248201527f4e6f7420617574686f72697a656420746f2063616e63656c00000000000000006044820152606490fd5b909150600154901c1633148680610596565b3461043157602036600319011261043157610664612975565b61066c612a56565b6001600160a01b038181169182156106cb5760018054610100600160a81b03198116600893841b610100600160a81b031617909155901c167f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e0600080a3005b604051631e4fbdf760e01b815260006004820152602490fd5b34610431576020806003193601126104315760043533600052600b825261071260ff60406000205416613148565b806000526008825261072d60026040600020015415156131b5565b806000526008825261074a60ff60036040600020015416156131f5565b806000526008825261076a60ff60036040600020015460081c1615613241565b80600052600d8252604060002033600052825260ff6040600020541661080a5780600052600d825260406000203360005282526040600020600160ff1982541617905580600052600e825260406000206107c481546130c1565b905580600052600e825260406000205491600c54906040519384528301527f67c5271c716d80ee3f0dbfee15f6a5bb4bca9834f6aee131b4d02ac60497a84160403393a3005b60405162461bcd60e51b815260048101839052601060248201526f105b1c9958591e48185c1c1c9bdd995960821b6044820152606490fd5b5034610431576020366003190112610431576004358060005260086020526040600020906040519161087383612818565b60018060a01b03938482541684526001820154936020810194855260028301549560408201968752600384015492606083019460ff85161515865260ff608085019560081c16151585528260056108cc6004840161288f565b9260a087019384520154169060c085019182526000938a51801515806109ac575b806109a3575b61096d575b5080610954949596511699519a519751151596511515915192511697600052600e60205260406000205495600c54976040519b8c9b8c5260208c015260408b015260608a015260808901526101408060a08a0152880190612935565b9460c08701528501526101008401526101208301520390f35b610977904261309c565b62093a809081811061098a575b506108f8565b9080959650039384116103ea5780610954949594610984565b508751156108f3565b508851156108ed565b34610431576020366003190112610431576109ce612975565b6109d6612a56565b6001600160a01b03166000818152600b602052604090205460ff1615610a9957600c5460031115610a3e5780600052600b602052604060002060ff1981541690557f40f1de544c351355c8d7c00dbf2f5e8319f1992e72d2e81f1786badecfadd208600080a2005b60405162461bcd60e51b815260206004820152602d60248201527f43616e6e6f742072656d6f7665207369676e65722062656c6f7720726571756960448201526c1c9959081d1a1c995cda1bdb19609a1b6064820152608490fd5b60405162461bcd60e51b815260206004820152601560248201527414da59db995c88191bd95cc81b9bdd08195e1a5cdd605a1b6044820152606490fd5b3461043157600036600319011261043157602060405160788152f35b3461043157602080600319360112610431576004359067ffffffffffffffff9081831161043157366023840112156104315782600401359182116104315760059260243684861b83018201116104315733600052600b835260ff91610b5d8360406000205416613148565b60005b858110610b6957005b8083610bad92891b8401013580600052600880885286604060002091600283015415159283610c65575b83610c53575b50505080610c35575b610bb2575b506130c1565b610b60565b80600052600d875260406000203360005287526040600020600160ff1982541617905580600052600e8088526040600020610bed81546130c1565b905581600052875260406000205490600c54604051928352888301527f67c5271c716d80ee3f0dbfee15f6a5bb4bca9834f6aee131b4d02ac60497a84160403393a388610ba7565b50600d87526040600020336000528752856040600020541615610ba2565b60030154901c16159050868b80610b99565b60038101548316159350610b93565b3461043157600036600319011261043157602060405160c88152f35b3461043157600036600319011261043157602060405160288152f35b34610431576020366003190112610431576001600160a01b03610ccd612975565b16600052600b602052602060ff604060002054166040519015158152f35b3461043157600036600319011261043157602060405169d3c21bcecceda10000008152f35b34610431576000366003190112610431576020604051601f8152f35b34610431576000366003190112610431576006546040516001600160a01b039091168152602090f35b34610431576000366003190112610431576005546040516001600160a01b039091168152602090f35b3461043157600036600319011261043157602060405160038152f35b34610431576000366003190112610431576020600a54604051908152f35b34610431576020366003190112610431577fd8e98bff5ae8522235ef48daecff7488b367200bea03a4e8cb4bc98108c6a49260a0610df4612975565b610dfc612a56565b600180831b03809116610e108115156130e4565b60065491816001600160601b03851b84161760065560405192606084526009606085015268636f6d6d756e69747960b81b60808501521660208301526040820152a1005b3461043157602080600319360112610431576004359033600052600b8152610e8360ff60406000205416613148565b81600052600891828252610ea060026040600020015415156131b5565b80600052828252610ebc60ff60036040600020015416156131f5565b80600052828252610eda60ff600360406000200154851c1615613241565b80600052828252604060002091600283015462093a8081018091116103ea57421061115a5781600052600e8152604060002054600c541161111d5760ff60015416156110d95782546040516370a0823160e01b81523060048201526001600160a01b0391821694908381602481895afa90811561043d576000916110ac575b506001820195865480921061107057600383018054600160ff1990911681179091555460405163a9059cbb60e01b815290891c85166001600160a01b03166004820152602481019290925284908290816000816044810103925af190811561043d57600091611043575b501561100c5790807f059bc073c455073e0814af252930152c0d60fe4592345221d6701b249827b3609392610ff786613281565b5416945495600154901c1694604051908152a4005b60405162461bcd60e51b815260048101849052600f60248201526e151c985b9cd9995c8819985a5b1959608a1b6044820152606490fd5b6110639150843d8611611069575b61105b818361286d565b810190613041565b87610fc3565b503d611051565b60405162461bcd60e51b8152600481018690526014602482015273496e73756666696369656e742062616c616e636560601b6044820152606490fd5b90508381813d83116110d2575b6110c3818361286d565b81010312610431575187610f59565b503d6110b9565b6064906040519062461bcd60e51b82526004820152601760248201527f436f6e7472616374206d757374206265207061757365640000000000000000006044820152fd5b6064906040519062461bcd60e51b825260048201526016602482015275496e73756666696369656e7420617070726f76616c7360501b6044820152fd5b6064906040519062461bcd60e51b825260048201526015602482015274151a5b59481b1bd8dac81b9bdd08195e1c1a5c9959605a1b6044820152fd5b34610431576040366003190112610431576111af612975565b5060405162461bcd60e51b8152602060048201526024808201527f5573652072657175657374456d657267656e6379576974686472617720696e736044820152631d19585960e21b6064820152608490fd5b346104315760003660031901126104315760206040516b019d971e4fe8401e740000008152f35b346104315760003660031901126104315760015460405160089190911c6001600160a01b03168152602090f35b3461043157602036600319011261043157600435600052600e6020526020604060002054604051908152f35b34610431576000366003190112610431576020600c54604051908152f35b34610431576000366003190112610431576020600454604051908152f35b503461043157602036600319011261043157600435600181101580611383575b6112e690613059565b60005260036020526040600020816040519161130183612850565b805483526001810154928360208201526002820154806040830152600383015460ff80821615159182606086015260081c161515908160808501526004850154938460a082015260066005870154968760c084015201549687910152604051968752602087015260408601526060850152608084015260a083015260c0820152f35b50601f8111156112dd565b3461043157600036600319011261043157600a546009549060005b8181106113d85750816113be6080938361309c565b906040519283526020830152604082015260006060820152f35b6113e1906130c1565b6113a9565b34610431576000366003190112610431576113ff612a56565b611407613023565b600160ff19815416176001557f62e78cea01bee320cd4e420270b5ea74000d11b0c9f74754ebdbfc544b05a2586020604051338152a1005b612a1c565b3461043157600036600319011261043157604051806009548083526020809301809160096000527f6e1540171b6c0c960b71a7020d9f60077f6af931a8bbf590da0223dacf75c7af9060005b868282106114e65786866114a68288038361286d565b604051928392818401908285525180915260408401929160005b8281106114cf57505050500390f35b8351855286955093810193928101926001016114c0565b835485529093019260019283019201611490565b3461043157602036600319011261043157600435611516612a56565b80156115b3576003811161155d5760407fe10a705edb4af55d916591ab3ae6d73d13195159890a14a90b10ae1117f9ea3791600c549080600c5582519182526020820152a1005b60405162461bcd60e51b815260206004820152602860248201527f5265717569726564207369676e6174757265732065786365656473207369676e604482015267195c8818dbdd5b9d60c21b6064820152608490fd5b60405162461bcd60e51b815260206004820152602a60248201527f5265717569726564207369676e617475726573206d75737420626520677265616044820152690746572207468616e20360b41b6064820152608490fd5b34610431576000366003190112610431576000806000806001805b601f8111156116685750506b1027e72f1f12813088000000918483039283116103ea5760a0946040519485526020850152604084015260608301526080820152f35b80600052600360209080825260ff816040600020015460081c16611697575b5050611692906130c1565b611626565b9193956116a790979195976130c1565b95848403611705576b019d971e4fe8401e7400000082018092116103ea57611692926116e86116fd93995b8760005282895260056040600020015490612a85565b96866000525260066040600020015490612a85565b929087611687565b6a7c13bc4b2c133c5600000082018092116103ea57611692926116e86116fd93996116d2565b346104315760003660031901126104315760ff6001541660095490600c5460009083158015611779575b5060a093604051931515845260208401526003604084015260608301526080820152f35b90915061180c577f6e1540171b6c0c960b71a7020d9f60077f6af931a8bbf590da0223dacf75c7af5460005260089060209382855260029283604060002001549360015b8381106117cd5750505093611755565b6117d6816129e5565b90549060031b1c6000528288528160406000200154868110611802575b506117fd906130c1565b6117bd565b95506117fd6117f3565b634e487b7160e01b600052603260045260246000fd5b346104315760403660031901126104315761183b61298b565b600435600052600d60205260406000209060018060a01b0316600052602052602060ff604060002054166040519015158152f35b34610431576020366003190112610431577fd8e98bff5ae8522235ef48daecff7488b367200bea03a4e8cb4bc98108c6a49260a06118ab612975565b6118b3612a56565b600180831b038091166118c78115156130e4565b60055491816001600160601b03851b841617600555604051926060845260076060850152661c1c9bda9958dd60ca1b60808501521660208301526040820152a1005b3461043157600036600319011261043157611922612a56565b60018054610100600160a81b0319811690915560009060081c6001600160a01b03167f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e08280a3005b3461043157600036600319011261043157611983613023565b61198b612a92565b005b3461043157600036600319011261043157602060ff600154166040519015158152f35b346104315760003660031901126104315733600052600b6020526119db60ff60406000205416613148565b60015460ff8116611a25576001906119f1613023565b60ff1916176001557f62e78cea01bee320cd4e420270b5ea74000d11b0c9f74754ebdbfc544b05a2586020604051338152a1005b60405162461bcd60e51b815260206004820152600e60248201526d105b1c9958591e481c185d5cd95960921b6044820152606490fd5b34610431576020366003190112610431576020611a79600435613322565b6040519015158152f35b34610431576000366003190112610431576020604051600a8152f35b5034610431576020366003190112610431576101009060043560005260036020526040600020805491600182015491600281015460ff60038301546004840154926006600586015495015496604051988952602089015260408801528181161515606088015260081c161515608086015260a085015260c0840152820152f35b3461043157600036600319011261043157611b38612a56565b611b40613129565b60ff19600154166001557f5db9ee0a495bf2e6ff9c91a7834c1ba4fdd244a5e8aa4e537bd38aeae4b073aa6020604051338152a1005b346104315760003660031901126104315760206040516a7c13bc4b2c133c560000008152f35b346104315760203660031901126104315760043560095481101561043157611bc56020916129e5565b90546040519160031b1c8152f35b3461043157602036600319011261043157611bec612975565b611bf4612a56565b6001600160a01b039081168015611c70577fd8e98bff5ae8522235ef48daecff7488b367200bea03a4e8cb4bc98108c6a4929160a09160025491816001600160601b03851b8416176002556040519260608452600b60608501526a70726963654f7261636c6560a81b60808501521660208301526040820152a1005b60405162461bcd60e51b8152602060048201526016602482015275496e76616c6964206f7261636c65206164647265737360501b6044820152606490fd5b3461043157611cbc366129cf565b6001918281101580611e9e575b611cd290613059565b81906000526020916007835260406000208054611cee816130a9565b91611cfc604051938461286d565b8183526000908152858120878785015b88858510611e5a57505050505050805191828115918215611e50575b5050611e47575b611d3a8395936130a9565b94611d48604051968761286d565b808652601f19611d57826130a9565b0160005b818110611e0f57505060005b818110611dce5750505050604051918083018184528451809152816040850195019160005b828110611d995785870386f35b835180518852828101518389015260408082015190890152606090810151151590880152608090960195928101928401611d8c565b80611df1611deb611e0793611de686899c9a9c61309c565b612a85565b856130d0565b51611dfc82886130d0565b52610ba781876130d0565b959395611d67565b8690604098969851611e2081612834565b60008152600083820152600060408201526000606082015282828901015201969496611d5b565b91508091611d2f565b1190508287611d28565b600491604051611e6981612834565b8554815284860154838201526002860154604082015260ff600387015416151560608201528152019201920191908890611d0c565b50601f811115611cc9565b34610431576000366003190112610431576002546040516001600160a01b039091168152602090f35b34610431576000366003190112610431576040517f00000000000000000000000000000000000000000000000000000000000000006001600160a01b03168152602090f35b3461043157606036600319011261043157611f30612975565b604490813567ffffffffffffffff811161043157611f529036906004016129a1565b909233600052600b602052611f6e60ff60406000205416613148565b611f76613129565b6001600160a01b038316156125d2576024351561258e5769d3c21bcecceda10000006024351161254a578115612514576101f482116124de576040516370a0823160e01b81523060048201526020816024816001600160a01b0388165afa90811561043d576000916124ac575b50602435811061246857600a810290808204600a14901517156103ea5760649004602435116124245750600a54926040519360208501906001600160601b03198560601b16825260243560348701524260548701523360601b607487015260888601526088855260c085019085821067ffffffffffffffff83111761230e578160405285519020948560005260086020526002604060002001546123e957505060405161208f81612818565b6001600160a01b03841681526024356020820152426040820152600060608201819052608082015267ffffffffffffffff831161230e576040516120dd601f8501601f19166020018261286d565b8381523684840111610431578383602083013760006020858301015260a08201523360c0820152846000526008602052604060002060018060a01b038251166001600160601b0360a01b82541617815560208201516001820155604082015160028201556003810161216160608401511515829060ff801983541691151516179055565b6080830151815461ff00191690151560081b61ff001617905560a082015180519067ffffffffffffffff821161230e5761219e60048401546127de565b601f81116123a2575b50602090601f831160011461232f57600593929160009183612324575b50508160011b916000199060031b1c19161760048201555b019060c060018060a01b03910151166001600160601b0360a01b82541617905560095490600160401b82101561230e576122bd7f1e0a207e99b9b2c2178d8ad92278f6ad994dcda1854f7342cbebf44c2e5441f6918661225d6122468660018498016009556129e5565b819391549060031b91821b91600019901b19161790565b905561226a600a546130c1565b600a5583600052600d6020526040600020336000526020526040600020600160ff1982541617905583600052600e6020526001604060002055604051946024358652606060208701526060860191613194565b42604085015233946001600160a01b0316939081900390a4600c5490604051916001835260208301527f67c5271c716d80ee3f0dbfee15f6a5bb4bca9834f6aee131b4d02ac60497a84160403393a3005b634e487b7160e01b600052604160045260246000fd5b0151905089806121c4565b906004840160005260206000209160005b601f198516811061238a575091839160019360059695601f19811610612371575b505050811b0160048201556121dc565b015160001960f88460031b161c19169055898080612361565b91926020600181928685015181550194019201612340565b600484016000526020600020601f840160051c8101602085106123e2575b601f830160051c820181106123d65750506121a7565b600081556001016123c0565b50806123c0565b62461bcd60e51b8252602060c4820152601460e4820152732932b8bab2b9ba1024a21031b7b63634b9b4b7b760611b61010490910152606490fd5b6064907f416d6f756e7420657863656564732070657263656e74616765206c696d6974006040519162461bcd60e51b835260206004840152601f6024840152820152fd5b60405162461bcd60e51b815260206004820152601a60248201527f496e73756666696369656e7420746f6b656e2062616c616e636500000000000081840152606490fd5b90506020813d6020116124d6575b816124c76020938361286d565b81010312610431575185611fe3565b3d91506124ba565b6064906e526561736f6e20746f6f206c6f6e6760881b6040519162461bcd60e51b835260206004840152600f6024840152820152fd5b6064906e1499585cdbdb881c995c5d5a5c9959608a1b6040519162461bcd60e51b835260206004840152600f6024840152820152fd5b6064907f416d6f756e742065786365656473206d6178696d756d206c696d6974000000006040519162461bcd60e51b835260206004840152601c6024840152820152fd5b6064907f416d6f756e74206d7573742062652067726561746572207468616e20300000006040519162461bcd60e51b835260206004840152601d6024840152820152fd5b60649074496e76616c696420746f6b656e206164647265737360581b6040519162461bcd60e51b83526020600484015260156024840152820152fd5b3461043157602036600319011261043157612627612975565b61262f612a56565b6001600160a01b031680156126d15780600052600b60205260ff604060002054166126945780600052600b6020526040600020600160ff198254161790557fc10a20171bb773348d4407b0ed548c3a68f0c94958d92651ba3885111617686c600080a2005b60405162461bcd60e51b81526020600482015260156024820152745369676e657220616c72656164792065786973747360581b6044820152606490fd5b60405162461bcd60e51b8152602060048201526016602482015275496e76616c6964207369676e6572206164647265737360501b6044820152606490fd5b3461043157600036600319011261043157602060405160968152f35b34610431576000366003190112610431576020604051603c8152f35b5034610431576020366003190112610431576004356000526008602052604060002060018060a01b036127d4818354169260018101549460ff600283015494600384015490600561279a6004870161288f565b95015416956040519889988952602089015260408801528181161515606088015260081c16151560808601528060a0860152840190612935565b9060c08301520390f35b90600182811c9216801561280e575b60208310146127f857565b634e487b7160e01b600052602260045260246000fd5b91607f16916127ed565b60e0810190811067ffffffffffffffff82111761230e57604052565b6080810190811067ffffffffffffffff82111761230e57604052565b610100810190811067ffffffffffffffff82111761230e57604052565b90601f8019910116810190811067ffffffffffffffff82111761230e57604052565b90604051918260008254926128a3846127de565b90818452600194858116908160001461291257506001146128cf575b50506128cd9250038361286d565b565b9093915060005260209081600020936000915b8183106128fa5750506128cd935082010138806128bf565b855488840185015294850194879450918301916128e2565b9150506128cd94506020925060ff191682840152151560051b82010138806128bf565b919082519283825260005b848110612961575050826000602080949584010152601f8019910116010190565b602081830181015184830182015201612940565b600435906001600160a01b038216820361043157565b602435906001600160a01b038216820361043157565b9181601f840112156104315782359167ffffffffffffffff8311610431576020838186019501011161043157565b6040906003190112610431576004359060243590565b60095481101561180c5760096000527f6e1540171b6c0c960b71a7020d9f60077f6af931a8bbf590da0223dacf75c7af0190600090565b3461043157600036600319011261043157602060405162093a808152f35b805482101561180c5760005260206000209060021b0190600090565b60015460081c6001600160a01b03163303612a6d57565b60405163118cdaa760e01b8152336004820152602490fd5b919082018092116103ea57565b6004546001810181116103ea57601f60018201116130205760018101600052600360205260406000209060ff600383015460081c1661301c57600254604051638e15f47360e01b815292906001600160a01b03906020908590600490829085165afa93841561043d57600094612fe8575b506001820154600184016000526007602052604060002060405190612b2782612834565b4282528660208301528260408301528287101560608301528054600160401b81101561230e57612b5c91600182018155612a3a565b612fd2576003606083612b979451845560208101516001850155604081015160028501550151151591019060ff801983541691151516179055565b60018301546040519086825260208201528186101560408201524260608201527f70e38768a25c5cf7aaa6b419f8e816c8ee9edd7a660f11963cc646855655108160806001870192a28410612f6f57600382015460ff16612c40575060017f7d3286e30b82fedc38ef2ba29d6d3e5ccc6dbd65373b1492379ea18eccc9258e926040928260ff1960038301541617600382015560024291015582519485524260208601520192a2565b92506002015462093a80908181018091116103ea57421015612c6157505050565b600182016000526003602052604060002090600382019081549060ff8260081c16612f315760ff821615612eec5760028401549081018091116103ea574210612e985761010061ff0019919091161790554260048281019190915560018301815560058054908301805460405163a9059cbb60e01b8082529388166001600160a01b0316948101949094526024840152947f0000000000000000000000000000000000000000000000000000000000000000811693926020816044816000895af190811561043d57600091612e79575b5015612e34576006805490840180546040519485526001600160a01b0393909216929092166004840152602483015292602090829060449082906000905af190811561043d57600091612e15575b5015612dd0577f219e0c2358bbd0ac84dc3ea4ce7d0dc9cb1eac750ee2f1e15927a0627fa00b8b926080926001809301549554905490604051968752602087015260408601524260608601520192a2565b60405162461bcd60e51b815260206004820152601960248201527f436f6d6d756e697479207472616e73666572206661696c6564000000000000006044820152606490fd5b612e2e915060203d6020116110695761105b818361286d565b38612d7f565b60405162461bcd60e51b815260206004820152601760248201527f50726f6a656374207472616e73666572206661696c65640000000000000000006044820152606490fd5b612e92915060203d6020116110695761105b818361286d565b38612d31565b60405162461bcd60e51b815260206004820152602660248201527f5072696365206d61696e74656e616e636520706572696f64206e6f7420636f6d6044820152651c1b195d195960d21b6064820152608490fd5b60405162461bcd60e51b815260206004820152601760248201527f507269636520636f6e646974696f6e206e6f74206d65740000000000000000006044820152606490fd5b60405162461bcd60e51b8152602060048201526016602482015275149bdd5b9908185b1c9958591e481d5b9b1bd8dad95960521b6044820152606490fd5b50600381015460ff8116612f84575b50505050565b7f8a82bdd88cae1a98297cd0cc6e0ff3e71f88cbf11cc22b15fa0202e6443b4a2f926000600260409460019460ff19166003820155015582519485524260208601520192a238808080612f7e565b634e487b7160e01b600052600060045260246000fd5b9093506020813d602011613014575b816130046020938361286d565b8101031261043157519238612b03565b3d9150612ff7565b5050565b50565b60ff6001541661302f57565b60405163d93c066560e01b8152600490fd5b90816020910312610431575180151581036104315790565b1561306057565b60405162461bcd60e51b815260206004820152601460248201527324b73b30b634b2103937bab73210373ab6b132b960611b6044820152606490fd5b919082039182116103ea57565b67ffffffffffffffff811161230e5760051b60200190565b60001981146103ea5760010190565b805182101561180c5760209160051b010190565b156130eb57565b60405162461bcd60e51b8152602060048201526016602482015275496e76616c69642077616c6c6574206164647265737360501b6044820152606490fd5b60ff600154161561313657565b604051638dfc202b60e01b8152600490fd5b1561314f57565b60405162461bcd60e51b815260206004820152601760248201527f4e6f7420616e20656d657267656e6379207369676e65720000000000000000006044820152606490fd5b908060209392818452848401376000828201840152601f01601f1916010190565b156131bc57565b60405162461bcd60e51b815260206004820152601160248201527014995c5d595cdd081b9bdd08199bdd5b99607a1b6044820152606490fd5b156131fc57565b60405162461bcd60e51b815260206004820152601860248201527f5265717565737420616c726561647920657865637574656400000000000000006044820152606490fd5b1561324857565b60405162461bcd60e51b815260206004820152601160248201527014995c5d595cdd0818d85b98d95b1b1959607a1b6044820152606490fd5b60005b600980549081831015612f7e57839161329c846129e5565b939054600394851b1c146132bb575050506132b6906130c1565b613284565b91935091600019918281019081116103ea576122466132dc6132e8926129e5565b905490871b1c926129e5565b90558154801561330c578101926132fe846129e5565b81939154921b1b1916905555565b634e487b7160e01b600052603160045260246000fd5b6000908082526008602052604082206040519061333e82612818565b60018060a01b0391828254168152600182015460208201526002820154906040810191825260c06003840154946005606084019560ff88161515875260ff608086019860081c16151588526133956004820161288f565b60a08601520154169101525191821591821561341c575b508115613411575b5061340c5762093a8081018091116133f85742106133f4578152600e6020526040812054600c54116133f15760ff60015416156133f15750600190565b90565b5090565b634e487b7160e01b83526011600452602483fd5b505090565b9050511515386133b4565b5115159150386133ac56fea264697066735822122070c41b7d188dc43169f5b969839aca551fc95ae264159c453f2982f598799ef664736f6c63430008140033", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}