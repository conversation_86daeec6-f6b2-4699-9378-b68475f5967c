{"_format": "hh-sol-artifact-1", "contractName": "HAOXVestingV2Fixed", "sourceName": "contracts/HAOXVestingV2Fixed.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_haoxToken", "type": "address"}, {"internalType": "address", "name": "_priceOracle", "type": "address"}, {"internalType": "address", "name": "_projectWallet", "type": "address"}, {"internalType": "address", "name": "_communityWallet", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "currentPrice", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "targetPrice", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "conditionMet", "type": "bool"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "PriceChecked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "price", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "PriceConditionMet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "price", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "PriceConditionReset", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "triggerPrice", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "projectTokens", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "communityTokens", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "RoundUnlocked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "string", "name": "walletType", "type": "string"}, {"indexed": false, "internalType": "address", "name": "oldWallet", "type": "address"}, {"indexed": false, "internalType": "address", "name": "newWallet", "type": "address"}], "name": "WalletUpdated", "type": "event"}, {"inputs": [], "name": "BASE_PRICE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "COMMUNITY_SHARE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "FIRST_ROUND_TOKENS", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PRICE_MAINTENANCE_PERIOD", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PROJECT_SHARE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "ROUND_12_21_INCREASE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "ROUND_22_31_INCREASE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "ROUND_2_11_INCREASE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOKENS_PER_ROUND", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOTAL_ROUNDS", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "checkPriceCondition", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "communityWallet", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "currentRound", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "emergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"internalType": "uint256", "name": "limit", "type": "uint256"}], "name": "getPriceCheckHistory", "outputs": [{"components": [{"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "uint256", "name": "targetPrice", "type": "uint256"}, {"internalType": "bool", "name": "conditionMet", "type": "bool"}], "internalType": "struct HAOXVestingV2Fixed.PriceCheck[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "roundNumber", "type": "uint256"}], "name": "getRoundInfo", "outputs": [{"internalType": "uint256", "name": "triggerPrice", "type": "uint256"}, {"internalType": "bool", "name": "priceConditionMet", "type": "bool"}, {"internalType": "bool", "name": "unlocked", "type": "bool"}, {"internalType": "uint256", "name": "priceReachedTime", "type": "uint256"}, {"internalType": "uint256", "name": "unlockTime", "type": "uint256"}, {"internalType": "uint256", "name": "projectTokens", "type": "uint256"}, {"internalType": "uint256", "name": "communityTokens", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getUnlockProgress", "outputs": [{"internalType": "uint256", "name": "currentPrice", "type": "uint256"}, {"internalType": "uint256", "name": "nextRoundNumber", "type": "uint256"}, {"internalType": "uint256", "name": "nextRoundTriggerPrice", "type": "uint256"}, {"internalType": "bool", "name": "nextRoundPriceConditionMet", "type": "bool"}, {"internalType": "uint256", "name": "timeRemaining", "type": "uint256"}, {"internalType": "uint256", "name": "priceReachedTime", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getUnlockStatistics", "outputs": [{"internalType": "uint256", "name": "totalUnlockedRounds", "type": "uint256"}, {"internalType": "uint256", "name": "totalUnlockedTokens", "type": "uint256"}, {"internalType": "uint256", "name": "totalProjectTokens", "type": "uint256"}, {"internalType": "uint256", "name": "totalCommunityTokens", "type": "uint256"}, {"internalType": "uint256", "name": "remainingTokens", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "haoxToken", "outputs": [{"internalType": "contract HAOXTokenV2", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "priceCheckHistory", "outputs": [{"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "uint256", "name": "targetPrice", "type": "uint256"}, {"internalType": "bool", "name": "conditionMet", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "priceOracle", "outputs": [{"internalType": "contract HAOXPriceOracleV2", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "projectWallet", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "unlockRounds", "outputs": [{"internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"internalType": "uint256", "name": "triggerPrice", "type": "uint256"}, {"internalType": "uint256", "name": "priceReachedTime", "type": "uint256"}, {"internalType": "bool", "name": "priceConditionMet", "type": "bool"}, {"internalType": "bool", "name": "unlocked", "type": "bool"}, {"internalType": "uint256", "name": "unlockTime", "type": "uint256"}, {"internalType": "uint256", "name": "projectTokens", "type": "uint256"}, {"internalType": "uint256", "name": "communityTokens", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_newWallet", "type": "address"}], "name": "updateCommunityWallet", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_newO<PERSON>le", "type": "address"}], "name": "updatePriceOracle", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_newWallet", "type": "address"}], "name": "updateProjectWallet", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x60a034620003fb57601f62001cd438819003828101601f191684019392916001600160401b0385118486101762000400578160809285926040978852833981010312620003fb57620000518262000437565b6020906200006182850162000437565b916200007d60606200007588880162000437565b960162000437565b926001956000948786553315620003e3578754610100600160a81b0319811633600881811b610100600160a81b0316929092178b558b51956001600160a01b039390921c83167f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e08a80a3816004978b895516948515620003a2575081169283156200035f5781169182156200031c5716928315620002d95760805260018060a01b03199182600254161760025581600554161760055560065416176006556006836204a3e4966200014d62000416565b9381859283528a828401918b83528185019042825260608601938185526080870193828552600360a08901974289528460e060c08c019b6aa56fa5b99019a5c80000008d52019b6af8277896582678ac0000008d5252528c2099518a55519089015551600288015560038701915115159060ff61ff0084549251151560081b1692169061ffff19161717905551858501555160058401555191015560025b600b811115620002a95750600c5b60158111156200027957506016935b83851115620002305785516117b09081620005248239608051818181610f4801526113370152f35b607890818102918183041490151715620002665760646200025f91049462000259868262000472565b6200044c565b9362000208565b634e487b7160e01b835260118252602483fd5b9360969081810291818304149015171562000266576064620002a391049462000259868262000472565b620001f9565b9360c89081810291818304149015171562000266576064620002d391049462000259868262000472565b620001eb565b895162461bcd60e51b8152808701869052601860248201527f496e76616c696420636f6d6d756e6974792077616c6c657400000000000000006044820152606490fd5b8a5162461bcd60e51b8152808801879052601660248201527f496e76616c69642070726f6a6563742077616c6c6574000000000000000000006044820152606490fd5b8a5162461bcd60e51b8152808801879052601660248201527f496e76616c6964206f7261636c652061646472657373000000000000000000006044820152606490fd5b62461bcd60e51b8152878101879052601560248201527f496e76616c696420746f6b656e206164647265737300000000000000000000006044820152606490fd5b8851631e4fbdf760e01b815260048101879052602490fd5b600080fd5b634e487b7160e01b600052604160045260246000fd5b6040519061010082016001600160401b038111838210176200040057604052565b51906001600160a01b0382168203620003fb57565b60001981146200045c5760010190565b634e487b7160e01b600052601160045260246000fd5b6006906200047f62000416565b928184526020840190815260408401906000918281526060860190838252608087019084825260a0880193858552604060c08a01966a31a17e847807b1bc000000885260e08b01986a4a723dc6b40b8a9a0000008a5281526003602052209851895551600189015551600288015560038701915115159060ff61ff0084549251151560081b1692169061ffff1916171790555160048501555160058401555191015556fe60806040908082526004918236101561001757600080fd5b600091823560e01c9081630b045c6714610f925750806311defc3a14610f7757806325dbec9d14610f345780632630c12f14610f0c578063309bfd9514610d0357806339d1fc8214610c295780633d718a7f14610c045780633f4ba83a14610b9a57806349b3f7e014610b205780635c975abb14610afd5780636172582014610ae0578063687404b214610abc578063715018a614610a5b57806371927628146109ba578063792c2cd2146108795780638456cb591461082057806388c3ffb0146107535780638a19c8bc146107355780638da5cb5b146107095780639574ab4d146106e357806395ccea67146105aa578063a6ce120a14610507578063beb08ab9146104df578063c7574839146104b7578063ce8dc3881461049c578063d2391aa914610481578063d480b60814610466578063dffd261a1461044b578063f2fde38b146103aa578063f4d8345514610346578063f86325ed146103295763ff5f1e911461018557600080fd5b346103145781600319360112610314576002548151638e15f47360e01b815292918291829182918290602090819089908b9082906001600160a01b03165afa97881561031f5782986102ec575b50885491600183018093116102d057601f83111561020e575b5060c09850825197885287015285015215156060840152608083015260a0820152f35b8281526003825283812084519098509550935061022a85611094565b8654855260018701548082870152600288015495868582015260e0600660038b01549a60ff808d1615159c8d606087015260081c16151560808501528d81015460a0850152600581015460c085015201549101529387958098806102e3575b610294575b506101eb565b6102a191929750426116ed565b62093a80908181106102bc57505060c098505b94388061028e565b8103919082116102d0575060c098506102b4565b634e487b7160e01b815260118a52602490fd5b50801515610289565b9080985081813d8311610318575b61030481836110b1565b81010312610314575196386101d2565b5080fd5b503d6102fa565b83513d84823e3d90fd5b5034610314578160031936011261031457602090516204a3e48152f35b50346103145761035536610fad565b9290815260076020528181209081548410156103a7575060809261037891610fde565b509081549160018101549160ff6003600284015493015416928151948552602085015283015215156060820152f35b80fd5b508234610447576020366003190112610447576103c5610fc8565b6103cd611010565b6001600160a01b0381811693909290841561043157505060018054610100600160a81b03198116600893841b610100600160a81b031617909155901c167f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e08380a380f35b51631e4fbdf760e01b8152908101859052602490fd5b8280fd5b50346103145781600319360112610314576020905160788152f35b50346103145781600319360112610314576020905160c88152f35b50346103145781600319360112610314576020905160288152f35b503461031457816003193601126103145760209051601f8152f35b503461031457816003193601126103145760065490516001600160a01b039091168152602090f35b503461031457816003193601126103145760055490516001600160a01b039091168152602090f35b50346103145760203660031901126103145760a07fd8e98bff5ae8522235ef48daecff7488b367200bea03a4e8cb4bc98108c6a49291610545610fc8565b9061054e611010565b600180841b0380921690610563821515611735565b60065492826bffffffffffffffffffffffff861b851617600655815193606085526009606086015268636f6d6d756e69747960b81b6080860152166020840152820152a180f35b508290346104475780600319360112610447576105c5610fc8565b6105cd611010565b6001600160a01b039081169081156106a857600154835163a9059cbb60e01b81526001600160a01b0360089290921c909216168482019081526024356020828101919091529192839182908890829060400103925af190811561069e578491610670575b501561063b578280f35b906020606492519162461bcd60e51b8352820152600f60248201526e151c985b9cd9995c8819985a5b1959608a1b6044820152fd5b610691915060203d8111610697575b61068981836110b1565b810190611692565b84610631565b503d61067f565b82513d86823e3d90fd5b825162461bcd60e51b81526020818601526015602482015274496e76616c696420746f6b656e206164647265737360581b6044820152606490fd5b5034610314578160031936011261031457602090516b019d971e4fe8401e740000008152f35b5034610314578160031936011261031457600154905160089190911c6001600160a01b03168152602090f35b50823461044757826003193601126104475760209250549051908152f35b509190346103a75760203660031901126103a7578260e09384928435600181101580610815575b610783906116aa565b81526003602052209080519061079882611094565b825482526001830154948560208401526002840154918281850152600385015460ff80821615159182606088015260081c16151592836080870152860154948560a082015260066005880154978860c084015201549788910152815197885260208801528601526060850152608084015260a083015260c0820152f35b50601f81111561077a565b503461031457816003193601126103145760207f62e78cea01bee320cd4e420270b5ea74000d11b0c9f74754ebdbfc544b05a2589161085d611010565b610865611674565b600160ff198154161760015551338152a180f35b5090346103a757806003193601126103a75780918190829083926001805b601f8111156108eb5750506b1027e72f1f12813088000000948186039586116108d8575060a096508151958652602086015284015260608301526080820152f35b634e487b7160e01b815260118852602490fd5b808752600360209080825260ff81878b20015460081c16610917575b505061091290611712565b610897565b9095939861092790979297611712565b98878403610993576b019d971e4fe8401e74000000810180911161098057916109789161096661091294965b8a8c52828a526005898d2001549061103f565b97898b52526006868a2001549061103f565b959038610907565b634e487b7160e01b895260118b52602489fd5b6a7c13bc4b2c133c5600000081018091116109805791610978916109666109129496610953565b50346103145760203660031901126103145760a07fd8e98bff5ae8522235ef48daecff7488b367200bea03a4e8cb4bc98108c6a492916109f8610fc8565b90610a01611010565b600180841b0380921690610a16821515611735565b60055492826bffffffffffffffffffffffff861b8516176005558151936060855260076060860152661c1c9bda9958dd60ca1b6080860152166020840152820152a180f35b82346103a757806003193601126103a757610a74611010565b60018054610100600160a81b03198116909155819060081c6001600160a01b03167f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e08280a380f35b82346103a757806003193601126103a757610ad5611674565b610add6110d3565b80f35b50346103145781600319360112610314576020905162093a808152f35b503461031457816003193601126103145760209060ff6001541690519015158152f35b5082346104475760203660031901126104475781610100938235815260036020522080549260ff6001830154936002840154600385015491850154936006600587015496015496815198895260208901528701528181161515606087015260081c161515608085015260a084015260c083015260e0820152f35b508234610447578260031936011261044757610bb4611010565b6001549060ff821615610bf6575060ff1916600155513381527f5db9ee0a495bf2e6ff9c91a7834c1ba4fdd244a5e8aa4e537bd38aeae4b073aa90602090a180f35b8251638dfc202b60e01b8152fd5b5034610314578160031936011261031457602090516a7c13bc4b2c133c560000008152f35b50823461044757602036600319011261044757610c44610fc8565b91610c4d611010565b6001600160a01b03928316918215610cc85750600280546001600160a01b03198116841790915581516060808252600b908201526a70726963654f7261636c6560a81b6080820152931660208401528201527fd8e98bff5ae8522235ef48daecff7488b367200bea03a4e8cb4bc98108c6a4929060a090a180f35b6020606492519162461bcd60e51b83528201526016602482015275496e76616c6964206f7261636c65206164647265737360501b6044820152fd5b50913461031457610d1336610fad565b9390916001948584101580610f01575b610d2c906116aa565b809385526020916007835283862090815490610d47826116fa565b92610d54875194856110b1565b828452885284882088908a8786015b88868510610ec05750505050505050805191828115918215610eb6575b5050610ead575b610d96859795969493966116fa565b96610da3845198896110b1565b808852601f19610db2826116fa565b01855b818110610e74575050845b818110610e25575050505080519380850191818652865180935281818701970193905b838210610df05786880387f35b84518051895283810151848a015281810151828a01526060908101511515908901526080909701969382019390850190610de3565b80610e4c610e46610e6893610e4186899e9c9e9d9a9b9d6116ed565b61103f565b85611721565b51610e57828a611721565b52610e628189611721565b50611712565b97959796949396610dc0565b9785969881969a989a51610e8781611062565b8a81528a838201528a898201528a606082015282828b0101520198969897959497610db5565b93508093610d87565b1190508238610d80565b85918b51610ecd81611062565b85548152848601548382015260028601548d82015260ff600387015416151560608201528152019201920191908b90610d63565b50601f841115610d23565b503461031457816003193601126103145760025490516001600160a01b039091168152602090f35b5034610314578160031936011261031457517f00000000000000000000000000000000000000000000000000000000000000006001600160a01b03168152602090f35b50346103145781600319360112610314576020905160968152f35b83903461031457816003193601126103145780603c60209252f35b6040906003190112610fc3576004359060243590565b600080fd5b600435906001600160a01b0382168203610fc357565b8054821015610ffa5760005260206000209060021b0190600090565b634e487b7160e01b600052603260045260246000fd5b60015460081c6001600160a01b0316330361102757565b60405163118cdaa760e01b8152336004820152602490fd5b9190820180921161104c57565b634e487b7160e01b600052601160045260246000fd5b6080810190811067ffffffffffffffff82111761107e57604052565b634e487b7160e01b600052604160045260246000fd5b610100810190811067ffffffffffffffff82111761107e57604052565b90601f8019910116810190811067ffffffffffffffff82111761107e57604052565b60045460018101811161104c57601f60018201116116715760018101600052600360205260406000209060ff600383015460081c1661166d57600254604051638e15f47360e01b815292906001600160a01b03906020908590600490829085165afa93841561147757600094611639575b506001820154906001840160005260076020526040600020916040519261116a84611062565b42845286602085015281604085015281871015606085015280546801000000000000000081101561107e576111a491600182018155610fde565b6116235760036060918551815560208601516001820155604086015160028201550193015115159260ff199360ff85835416911617905560018401546040519087825260208201528187101560408201524260608201527f70e38768a25c5cf7aaa6b419f8e816c8ee9edd7a660f11963cc646855655108160806001880192a285106115be57600383015460ff1661128257507f7d3286e30b82fedc38ef2ba29d6d3e5ccc6dbd65373b1492379ea18eccc9258e928260018093600360409601541617600382015560024291015582519485524260208601520192a2565b9350506002015462093a809081810180911161104c574210156112a457505050565b600182016000526003602052604060002090600382019081549060ff8260081c166115805760ff82161561153b57600284015490810180911161104c5742106114e75761010061ff0019919091161790554260048281019190915560018301815560058054908301805460405163a9059cbb60e01b8082529388166001600160a01b0316948101949094526024840152947f0000000000000000000000000000000000000000000000000000000000000000811693926020816044816000895af1908115611477576000916114c8575b5015611483576006805490840180546040519485526001600160a01b0393909216929092166004840152602483015292602090829060449082906000905af190811561147757600091611458575b5015611413577f219e0c2358bbd0ac84dc3ea4ce7d0dc9cb1eac750ee2f1e15927a0627fa00b8b926080926001809301549554905490604051968752602087015260408601524260608601520192a2565b60405162461bcd60e51b815260206004820152601960248201527f436f6d6d756e697479207472616e73666572206661696c6564000000000000006044820152606490fd5b611471915060203d6020116106975761068981836110b1565b386113c2565b6040513d6000823e3d90fd5b60405162461bcd60e51b815260206004820152601760248201527f50726f6a656374207472616e73666572206661696c65640000000000000000006044820152606490fd5b6114e1915060203d6020116106975761068981836110b1565b38611374565b60405162461bcd60e51b815260206004820152602660248201527f5072696365206d61696e74656e616e636520706572696f64206e6f7420636f6d6044820152651c1b195d195960d21b6064820152608490fd5b60405162461bcd60e51b815260206004820152601760248201527f507269636520636f6e646974696f6e206e6f74206d65740000000000000000006044820152606490fd5b60405162461bcd60e51b8152602060048201526016602482015275149bdd5b9908185b1c9958591e481d5b9b1bd8dad95960521b6044820152606490fd5b50919060038101549160ff83166115d7575b5050505050565b600060026001937f8a82bdd88cae1a98297cd0cc6e0ff3e71f88cbf11cc22b15fa0202e6443b4a2f96604096166003820155015582519485524260208601520192a238808080806115d0565b634e487b7160e01b600052600060045260246000fd5b9093506020813d602011611665575b81611655602093836110b1565b81010312610fc357519238611144565b3d9150611648565b5050565b50565b60ff6001541661168057565b60405163d93c066560e01b8152600490fd5b90816020910312610fc357518015158103610fc35790565b156116b157565b60405162461bcd60e51b815260206004820152601460248201527324b73b30b634b2103937bab73210373ab6b132b960611b6044820152606490fd5b9190820391821161104c57565b67ffffffffffffffff811161107e5760051b60200190565b600019811461104c5760010190565b8051821015610ffa5760209160051b010190565b1561173c57565b60405162461bcd60e51b8152602060048201526016602482015275496e76616c69642077616c6c6574206164647265737360501b6044820152606490fdfea2646970667358221220a992426efefc73f13c2a1a8bc2922d1f3bb989fe6d1d25dc492525a88a29a7cb64736f6c63430008140033", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}