{"_format": "hh-sol-artifact-1", "contractName": "HAOXVestingV2", "sourceName": "contracts/HAOXVestingV2.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_haoxToken", "type": "address"}, {"internalType": "address", "name": "_priceOracle", "type": "address"}, {"internalType": "address", "name": "_projectWallet", "type": "address"}, {"internalType": "address", "name": "_communityWallet", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "round", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "price", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "PriceConditionMet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "round", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "triggerPrice", "type": "uint256"}], "name": "RoundInitialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "round", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "projectTokens", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "communityTokens", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "RoundUnlocked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "string", "name": "walletType", "type": "string"}, {"indexed": false, "internalType": "address", "name": "oldWallet", "type": "address"}, {"indexed": false, "internalType": "address", "name": "newWallet", "type": "address"}], "name": "WalletUpdated", "type": "event"}, {"inputs": [], "name": "COMMUNITY_SHARE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "INITIAL_TRIGGER_PRICE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PRICE_MAINTENANCE_PERIOD", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PROJECT_SHARE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "ROUND_12_21_INCREASE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "ROUND_22_31_INCREASE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOKENS_PER_ROUND", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOTAL_ROUNDS", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "checkPriceConditions", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "communityWallet", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "currentRound", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "emergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getCurrentStatus", "outputs": [{"internalType": "uint256", "name": "_currentRound", "type": "uint256"}, {"internalType": "uint256", "name": "_totalUnlockedTokens", "type": "uint256"}, {"internalType": "uint256", "name": "_remainingTokens", "type": "uint256"}, {"internalType": "uint256", "name": "_nextTriggerPrice", "type": "uint256"}, {"internalType": "bool", "name": "_nextRoundConditionMet", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "roundNumber", "type": "uint256"}], "name": "getRoundInfo", "outputs": [{"internalType": "uint256", "name": "triggerPrice", "type": "uint256"}, {"internalType": "uint256", "name": "priceReachedTime", "type": "uint256"}, {"internalType": "bool", "name": "priceConditionMet", "type": "bool"}, {"internalType": "bool", "name": "unlocked", "type": "bool"}, {"internalType": "uint256", "name": "unlockTime", "type": "uint256"}, {"internalType": "uint256", "name": "projectTokens", "type": "uint256"}, {"internalType": "uint256", "name": "communityTokens", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "haoxToken", "outputs": [{"internalType": "contract HAOXTokenV2", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "priceOracle", "outputs": [{"internalType": "contract HAOXPriceOracleV2", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "projectWallet", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "roundTriggerPrices", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_communityWallet", "type": "address"}], "name": "setCommunityWallet", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_priceOracle", "type": "address"}], "name": "setPriceO<PERSON>le", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_projectWallet", "type": "address"}], "name": "setProjectWallet", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "totalUnlockedTokens", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "unlockRounds", "outputs": [{"internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"internalType": "uint256", "name": "triggerPrice", "type": "uint256"}, {"internalType": "uint256", "name": "priceReachedTime", "type": "uint256"}, {"internalType": "bool", "name": "priceConditionMet", "type": "bool"}, {"internalType": "bool", "name": "unlocked", "type": "bool"}, {"internalType": "uint256", "name": "unlockTime", "type": "uint256"}, {"internalType": "uint256", "name": "projectTokens", "type": "uint256"}, {"internalType": "uint256", "name": "communityTokens", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "0x6080604081815260048036101561001557600080fd5b600092833560e01c9081630b045c6714610f465750806311defc3a14610f2a57806325dbec9d14610ee65780632630c12f14610ebd5780633d718a7f14610e975780633f4ba83a14610e2e57806349b3f7e014610db5578063530e784f14610d665780635312ea8e14610c735780635c975abb14610c4f5780636172582014610c315780636cd7761e14610c13578063715018a614610bb25780637ee080c4146106aa5780638456cb591461065057806385d6bb81146105af57806388c3ffb01461048a5780638a19c8bc1461046b5780638a780447146103c85780638da5cb5b1461039b5780639eef3c351461037e578063a3dd2619146102d7578063beb08ab9146102ae578063c34e314014610287578063c75748391461025e578063ce8dc38814610242578063d2391aa914610226578063dffd261a146102065763f2fde38b1461016257600080fd5b346102025760203660031901126102025780356001600160a01b03818116939092908483036101fe57610193610f61565b84156101e857505060018054610100600160a81b03198116600893841b610100600160a81b031617909155901c167f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e08380a380f35b51631e4fbdf760e01b8152908101859052602490fd5b8580fd5b8280fd5b5050346102225781600319360112610222576020905160788152f35b5080fd5b5050346102225781600319360112610222576020905160288152f35b50503461022257816003193601126102225760209051601f8152f35b50503461022257816003193601126102225760065490516001600160a01b039091168152602090f35b50346102025760203660031901126102025760209282913581526008845220549051908152f35b50503461022257816003193601126102225760055490516001600160a01b039091168152602090f35b50913461037b578060031936011261037b57808160035493855485601f0392601f8411610368576a7c13bc4b2c133c560000008085029485041487601f1417156103685760a09750601f871115610345575b5081519586526020860152840152606083015215156080820152f35b8681526007602052829020600181015460039091015460ff169550935038610329565b634e487b7160e01b815260118852602490fd5b80fd5b503461020257826003193601126102025760209250549051908152f35b505034610222578160031936011261022257600154905160089190911c6001600160a01b03168152602090f35b503461020257602036600319011261020257356001600160a01b038181169291839003610467577fd8e98bff5ae8522235ef48daecff7488b367200bea03a4e8cb4bc98108c6a4929260a09261041c610f61565b610427821515611003565b60055492826001600160601b03861b8516176005558151936060855260076060860152661c1c9bda9958dd60ca1b6080860152166020840152820152a180f35b8380fd5b5050346102225781600319360112610222576020906003549051908152f35b50913461037b57602036600319011261037b5782356002811015806105a4575b1561057157815260076020528181209082519061010082019082821067ffffffffffffffff83111761055e57509060e094859392855282548252600183015494856020840152600284015490818185015260038501549160ff80841615159384606088015260081c16151593846080870152860154948560a082015260066005880154978860c084015201549788910152815197885260208801528601526060850152608084015260a083015260c0820152f35b634e487b7160e01b815260418652602490fd5b825162461bcd60e51b8152602081860152600d60248201526c125b9d985b1a59081c9bdd5b99609a1b6044820152606490fd5b50601f8111156104aa565b503461020257602036600319011261020257356001600160a01b038181169291839003610467577fd8e98bff5ae8522235ef48daecff7488b367200bea03a4e8cb4bc98108c6a4929260a092610603610f61565b61060e821515611003565b60065492826001600160601b03861b851617600655815193606085526009606086015268636f6d6d756e69747960b81b6080860152166020840152820152a180f35b50503461022257816003193601126102225760207f62e78cea01bee320cd4e420270b5ea74000d11b0c9f74754ebdbfc544b05a2589161068e610f61565b610696610fc8565b600160ff198154161760015551338152a180f35b50346102025782600319360112610202576106c3610fc8565b60025482516316dc213f60e21b81526001600160a01b0391849082908590829086165afa8015610ba85785918691610b6d575b50605011610b2a576003938454601f811115610710578680f35b8652602091600783528187209086820190815460ff81161580610b1c575b610ad1575b5081549060ff821680610ac3575b61074d575b5050508680f35b6002840193845462093a808101809111610ab057908b9594939291421015610777575b5050610746565b6001015411610a9e57505050508454928387526007835281872090868201805460ff8160081c16610a625760ff811615610a1f5761ff001916610100179055428287015585546a7c13bc4b2c133c560000008101908110610a0c578655600580549083018054855163a9059cbb60e01b8082529385166001600160a01b0316818b0190815260208101929092527f0000000000000000000000000000000000000000000000000000000000000000851695929491889082908e908290819060400103918a5af1908115610a02578c916109e5575b50156109a2579060068792815416910194898c87549561088b8a51978896879586948552840160209093929193604081019460018060a01b031681520152565b03925af190811561099857899161096b575b5015610928575490548251948552928401528201524260608201527f66c6a58b8bc7d8872da31ef277b7e3636e5308d1e23b64be08b67716a1791f8090608090a1815490601f82106108fe575b5050505b3880808080858180808080610770565b6000198214610915575060010190553880806108ea565b634e487b7160e01b845260119052602483fd5b825162461bcd60e51b8152808701859052601960248201527f436f6d6d756e697479207472616e73666572206661696c6564000000000000006044820152606490fd5b61098b9150853d8711610991575b6109838183610f90565b810190610fe6565b3861089d565b503d610979565b84513d8b823e3d90fd5b855162461bcd60e51b8152808a01889052601760248201527f50726f6a656374207472616e73666572206661696c65640000000000000000006044820152606490fd5b6109fc9150883d8a11610991576109838183610f90565b3861084b565b87513d8e823e3d90fd5b634e487b7160e01b895260118752602489fd5b845162461bcd60e51b8152808901879052601760248201527f507269636520636f6e646974696f6e206e6f74206d65740000000000000000006044820152606490fd5b845162461bcd60e51b81528089018790526016602482015275149bdd5b9908185b1c9958591e481d5b9b1bd8dad95960521b6044820152606490fd5b60ff1916905555506108ee9350505050565b634e487b7160e01b8c5260118a5260248cfd5b5060ff8260081c1615610741565b60019042600286015560ff19161782557f7d3286e30b82fedc38ef2ba29d6d3e5ccc6dbd65373b1492379ea18eccc9258e60608954865190815283888201524287820152a138610733565b50600184015482101561072e565b835162461bcd60e51b8152602081850152601860248201527f507269636520636f6e666964656e636520746f6f206c6f7700000000000000006044820152606490fd5b809250858092503d8311610ba1575b610b868183610f90565b81010312610b9d57605060208251920151906106f6565b8480fd5b503d610b7c565b84513d87823e3d90fd5b833461037b578060031936011261037b57610bcb610f61565b60018054610100600160a81b03198116909155819060081c6001600160a01b03167f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e08280a380f35b50503461022257816003193601126102225760209051620426808152f35b5050346102225781600319360112610222576020905162093a808152f35b50503461022257816003193601126102225760209060ff6001541690519015158152f35b50903461020257602036600319011261020257610c8e610f61565b600154815163a9059cbb60e01b815260089190911c6001600160a01b03908116848301908152843560208281019190915291839182908890829060400103927f0000000000000000000000000000000000000000000000000000000000000000165af1908115610d5c578491610d3e575b5015610d09578280f35b906020606492519162461bcd60e51b8352820152600f60248201526e151c985b9cd9995c8819985a5b1959608a1b6044820152fd5b610d56915060203d8111610991576109838183610f90565b38610cff565b82513d86823e3d90fd5b83823461022257602036600319011261022257356001600160a01b0381169081900361022257610d94610f61565b610d9f811515611003565b6001600160601b0360a01b600254161760025580f35b50346102025760203660031901126102025781610100938235815260076020522080549260ff6001830154936002840154600385015491850154936006600587015496015496815198895260208901528701528181161515606087015260081c161515608085015260a084015260c083015260e0820152f35b5034610202578260031936011261020257610e47610f61565b6001549060ff821615610e89575060ff1916600155513381527f5db9ee0a495bf2e6ff9c91a7834c1ba4fdd244a5e8aa4e537bd38aeae4b073aa90602090a180f35b8251638dfc202b60e01b8152fd5b505034610222578160031936011261022257602090516a7c13bc4b2c133c560000008152f35b50503461022257816003193601126102225760025490516001600160a01b039091168152602090f35b505034610222578160031936011261022257517f00000000000000000000000000000000000000000000000000000000000000006001600160a01b03168152602090f35b5050346102225781600319360112610222576020905160968152f35b84903461022257816003193601126102225780603c60209252f35b60015460081c6001600160a01b03163303610f7857565b60405163118cdaa760e01b8152336004820152602490fd5b90601f8019910116810190811067ffffffffffffffff821117610fb257604052565b634e487b7160e01b600052604160045260246000fd5b60ff60015416610fd457565b60405163d93c066560e01b8152600490fd5b90816020910312610ffe57518015158103610ffe5790565b600080fd5b1561100a57565b60405162461bcd60e51b815260206004820152600f60248201526e496e76616c6964206164647265737360881b6044820152606490fdfea2646970667358221220833b5694759fa8d48498cc9bc0ebc866d6eb63f79f4381c9e86518e50d2efeb864736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}