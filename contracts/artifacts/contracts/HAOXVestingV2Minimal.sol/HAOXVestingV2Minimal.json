{"_format": "hh-sol-artifact-1", "contractName": "HAOXVestingV2Minimal", "sourceName": "contracts/HAOXVestingV2Minimal.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_haoxToken", "type": "address"}, {"internalType": "address", "name": "_priceOracle", "type": "address"}, {"internalType": "address", "name": "_projectWallet", "type": "address"}, {"internalType": "address", "name": "_communityWallet", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "EmergencyWithdrawExecuted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "requestId", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "requestTime", "type": "uint256"}], "name": "EmergencyWithdrawRequested", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "price", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "PriceConditionMet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "price", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "PriceConditionReset", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "roundNumber", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "triggerPrice", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "projectTokens", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "communityTokens", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "RoundUnlocked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "EMERGENCY_DELAY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_EMERGENCY_AMOUNT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PRICE_MAINTAIN_DURATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOTAL_ROUNDS", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}], "name": "addEmergencySigner", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "checkPriceCondition", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "communityWallet", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "currentRound", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "emergencyPause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "emergencyRequests", "outputs": [{"internalType": "uint128", "name": "amount", "type": "uint128"}, {"internalType": "uint64", "name": "requestTime", "type": "uint64"}, {"internalType": "bool", "name": "executed", "type": "bool"}, {"internalType": "address", "name": "requester", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "emergencySigners", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "requestId", "type": "bytes32"}], "name": "executeEmergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "roundNumber", "type": "uint256"}], "name": "getPriceHistory", "outputs": [{"components": [{"internalType": "uint64", "name": "timestamp", "type": "uint64"}, {"internalType": "uint64", "name": "price", "type": "uint64"}, {"internalType": "bool", "name": "conditionMet", "type": "bool"}], "internalType": "struct HAOXVestingV2Minimal.PriceCheck[10]", "name": "", "type": "tuple[10]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "roundNumber", "type": "uint256"}], "name": "getRoundInfo", "outputs": [{"internalType": "uint256", "name": "triggerPrice", "type": "uint256"}, {"internalType": "bool", "name": "priceConditionMet", "type": "bool"}, {"internalType": "bool", "name": "unlocked", "type": "bool"}, {"internalType": "uint256", "name": "priceReachedTime", "type": "uint256"}, {"internalType": "uint256", "name": "unlockTime", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getUnlockProgress", "outputs": [{"internalType": "uint256", "name": "currentPrice", "type": "uint256"}, {"internalType": "uint256", "name": "nextRound", "type": "uint256"}, {"internalType": "uint256", "name": "targetPrice", "type": "uint256"}, {"internalType": "bool", "name": "conditionMet", "type": "bool"}, {"internalType": "uint256", "name": "timeRemaining", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "haoxToken", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "historyIndex", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "priceHistory", "outputs": [{"internalType": "uint64", "name": "timestamp", "type": "uint64"}, {"internalType": "uint64", "name": "price", "type": "uint64"}, {"internalType": "bool", "name": "conditionMet", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "priceOracle", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "projectWallet", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}], "name": "remove<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "requestEmergencyWithdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "requiredSignatures", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "rounds", "outputs": [{"internalType": "uint128", "name": "triggerPrice", "type": "uint128"}, {"internalType": "uint64", "name": "priceReachedTime", "type": "uint64"}, {"internalType": "uint64", "name": "unlockTime", "type": "uint64"}, {"internalType": "bool", "name": "priceConditionMet", "type": "bool"}, {"internalType": "bool", "name": "unlocked", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}