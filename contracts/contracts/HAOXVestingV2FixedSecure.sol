// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "./HAOXVestingV2Fixed.sol";

/**
 * @title HAOXVestingV2FixedSecure
 * @dev 安全版本的HAOX代币解锁合约
 * 实现带时间锁的紧急提取机制，增强安全性
 */
contract HAOXVestingV2FixedSecure is HAOXVestingV2Fixed {
    
    // 紧急提取相关常量
    uint256 public constant EMERGENCY_DELAY = 7 days;
    uint256 public constant MAX_EMERGENCY_AMOUNT = 1000000 * 10**18; // 最大100万代币
    uint256 public constant MAX_EMERGENCY_PERCENTAGE = 10; // 最大10%的合约余额
    
    // 紧急提取请求结构
    struct EmergencyRequest {
        address token;
        uint256 amount;
        uint256 requestTime;
        bool executed;
        bool cancelled;
        string reason;
        address requester;
    }
    
    // 状态变量
    mapping(bytes32 => EmergencyRequest) public emergencyRequests;
    bytes32[] public pendingRequests;
    uint256 public totalEmergencyRequests;
    
    // 多重签名相关
    mapping(address => bool) public emergencySigners;
    uint256 public requiredSignatures;
    mapping(bytes32 => mapping(address => bool)) public emergencyApprovals;
    mapping(bytes32 => uint256) public approvalCount;
    
    // 事件定义
    event EmergencyWithdrawRequested(
        bytes32 indexed requestId,
        address indexed token,
        uint256 amount,
        string reason,
        address indexed requester,
        uint256 requestTime
    );
    
    event EmergencyWithdrawApproved(
        bytes32 indexed requestId,
        address indexed approver,
        uint256 approvalCount,
        uint256 requiredSignatures
    );
    
    event EmergencyWithdrawExecuted(
        bytes32 indexed requestId,
        address indexed token,
        uint256 amount,
        address indexed recipient
    );
    
    event EmergencyWithdrawCancelled(
        bytes32 indexed requestId,
        address indexed canceller,
        string reason
    );
    
    event EmergencySignerAdded(address indexed signer);
    event EmergencySignerRemoved(address indexed signer);
    event RequiredSignaturesUpdated(uint256 oldRequired, uint256 newRequired);
    
    // 修饰符
    modifier onlyEmergencySigner() {
        require(emergencySigners[msg.sender], "Not an emergency signer");
        _;
    }
    
    modifier validEmergencyRequest(bytes32 requestId) {
        require(emergencyRequests[requestId].requestTime > 0, "Request not found");
        require(!emergencyRequests[requestId].executed, "Request already executed");
        require(!emergencyRequests[requestId].cancelled, "Request cancelled");
        _;
    }

    /**
     * @dev 构造函数
     */
    constructor(
        address _haoxToken,
        address _priceOracle,
        address _projectWallet,
        address _communityWallet
    ) HAOXVestingV2Fixed(_haoxToken, _priceOracle, _projectWallet, _communityWallet) {
        // 初始化紧急签名者（合约所有者）
        emergencySigners[msg.sender] = true;
        requiredSignatures = 1;
        
        emit EmergencySignerAdded(msg.sender);
        emit RequiredSignaturesUpdated(0, 1);
    }

    /**
     * @dev 添加紧急签名者
     */
    function addEmergencySigner(address signer) external onlyOwner {
        require(signer != address(0), "Invalid signer address");
        require(!emergencySigners[signer], "Signer already exists");
        
        emergencySigners[signer] = true;
        emit EmergencySignerAdded(signer);
    }

    /**
     * @dev 移除紧急签名者
     */
    function removeEmergencySigner(address signer) external onlyOwner {
        require(emergencySigners[signer], "Signer does not exist");
        require(getSignerCount() > requiredSignatures, "Cannot remove signer below required threshold");
        
        emergencySigners[signer] = false;
        emit EmergencySignerRemoved(signer);
    }

    /**
     * @dev 设置所需签名数量
     */
    function setRequiredSignatures(uint256 _required) external onlyOwner {
        require(_required > 0, "Required signatures must be greater than 0");
        require(_required <= getSignerCount(), "Required signatures exceeds signer count");
        
        uint256 oldRequired = requiredSignatures;
        requiredSignatures = _required;
        
        emit RequiredSignaturesUpdated(oldRequired, _required);
    }

    /**
     * @dev 获取签名者数量
     */
    function getSignerCount() public view returns (uint256) {
        // 这里简化实现，实际应该维护一个签名者列表
        // 为了演示，我们假设有固定数量的签名者
        return 3; // 示例：3个签名者
    }

    /**
     * @dev 请求紧急提取
     */
    function requestEmergencyWithdraw(
        address token,
        uint256 amount,
        string calldata reason
    ) external onlyEmergencySigner whenPaused {
        require(token != address(0), "Invalid token address");
        require(amount > 0, "Amount must be greater than 0");
        require(amount <= MAX_EMERGENCY_AMOUNT, "Amount exceeds maximum limit");
        require(bytes(reason).length > 0, "Reason required");
        require(bytes(reason).length <= 500, "Reason too long");
        
        // 检查代币余额
        IERC20 tokenContract = IERC20(token);
        uint256 balance = tokenContract.balanceOf(address(this));
        require(balance >= amount, "Insufficient token balance");
        
        // 检查百分比限制
        uint256 maxPercentageAmount = (balance * MAX_EMERGENCY_PERCENTAGE) / 100;
        require(amount <= maxPercentageAmount, "Amount exceeds percentage limit");
        
        // 生成请求ID
        bytes32 requestId = keccak256(
            abi.encodePacked(
                token,
                amount,
                block.timestamp,
                msg.sender,
                totalEmergencyRequests
            )
        );
        
        require(emergencyRequests[requestId].requestTime == 0, "Request ID collision");
        
        // 创建请求
        emergencyRequests[requestId] = EmergencyRequest({
            token: token,
            amount: amount,
            requestTime: block.timestamp,
            executed: false,
            cancelled: false,
            reason: reason,
            requester: msg.sender
        });
        
        pendingRequests.push(requestId);
        totalEmergencyRequests++;
        
        // 自动批准请求者的签名
        emergencyApprovals[requestId][msg.sender] = true;
        approvalCount[requestId] = 1;
        
        emit EmergencyWithdrawRequested(
            requestId,
            token,
            amount,
            reason,
            msg.sender,
            block.timestamp
        );
        
        emit EmergencyWithdrawApproved(requestId, msg.sender, 1, requiredSignatures);
    }

    /**
     * @dev 批准紧急提取请求
     */
    function approveEmergencyWithdraw(bytes32 requestId) 
        external 
        onlyEmergencySigner 
        validEmergencyRequest(requestId) 
    {
        require(!emergencyApprovals[requestId][msg.sender], "Already approved");
        
        emergencyApprovals[requestId][msg.sender] = true;
        approvalCount[requestId]++;
        
        emit EmergencyWithdrawApproved(
            requestId, 
            msg.sender, 
            approvalCount[requestId], 
            requiredSignatures
        );
    }

    /**
     * @dev 执行紧急提取
     */
    function executeEmergencyWithdraw(bytes32 requestId) 
        external 
        onlyEmergencySigner 
        validEmergencyRequest(requestId) 
    {
        EmergencyRequest storage request = emergencyRequests[requestId];
        
        // 检查时间锁
        require(
            block.timestamp >= request.requestTime + EMERGENCY_DELAY,
            "Time lock not expired"
        );
        
        // 检查签名数量
        require(
            approvalCount[requestId] >= requiredSignatures,
            "Insufficient approvals"
        );
        
        // 检查合约状态
        require(paused(), "Contract must be paused");
        
        // 再次检查代币余额
        IERC20 token = IERC20(request.token);
        uint256 balance = token.balanceOf(address(this));
        require(balance >= request.amount, "Insufficient balance");
        
        // 执行转账
        request.executed = true;
        require(token.transfer(owner(), request.amount), "Transfer failed");
        
        // 从待处理列表中移除
        _removePendingRequest(requestId);
        
        emit EmergencyWithdrawExecuted(requestId, request.token, request.amount, owner());
    }

    /**
     * @dev 取消紧急提取请求
     */
    function cancelEmergencyWithdraw(bytes32 requestId, string calldata reason) 
        external 
        onlyEmergencySigner 
        validEmergencyRequest(requestId) 
    {
        EmergencyRequest storage request = emergencyRequests[requestId];
        
        // 只有请求者或所有者可以取消
        require(
            msg.sender == request.requester || msg.sender == owner(),
            "Not authorized to cancel"
        );
        
        request.cancelled = true;
        _removePendingRequest(requestId);
        
        emit EmergencyWithdrawCancelled(requestId, msg.sender, reason);
    }

    /**
     * @dev 获取待处理请求
     */
    function getPendingRequests() external view returns (bytes32[] memory) {
        return pendingRequests;
    }

    /**
     * @dev 获取请求详情
     */
    function getRequestDetails(bytes32 requestId) external view returns (
        address token,
        uint256 amount,
        uint256 requestTime,
        bool executed,
        bool cancelled,
        string memory reason,
        address requester,
        uint256 timeRemaining,
        uint256 currentApprovals,
        uint256 requiredApprovals
    ) {
        EmergencyRequest memory request = emergencyRequests[requestId];
        
        uint256 remaining = 0;
        if (request.requestTime > 0 && !request.executed && !request.cancelled) {
            uint256 elapsed = block.timestamp - request.requestTime;
            if (elapsed < EMERGENCY_DELAY) {
                remaining = EMERGENCY_DELAY - elapsed;
            }
        }
        
        return (
            request.token,
            request.amount,
            request.requestTime,
            request.executed,
            request.cancelled,
            request.reason,
            request.requester,
            remaining,
            approvalCount[requestId],
            requiredSignatures
        );
    }

    /**
     * @dev 检查用户是否已批准请求
     */
    function hasApproved(bytes32 requestId, address signer) external view returns (bool) {
        return emergencyApprovals[requestId][signer];
    }

    /**
     * @dev 获取紧急提取统计
     */
    function getEmergencyStatistics() external view returns (
        uint256 totalRequests,
        uint256 pendingCount,
        uint256 executedCount,
        uint256 cancelledCount
    ) {
        totalRequests = totalEmergencyRequests;
        pendingCount = pendingRequests.length;
        
        // 计算已执行和已取消的请求数量
        executedCount = 0;
        cancelledCount = 0;
        
        // 这里简化实现，实际应该维护计数器
        for (uint256 i = 0; i < totalRequests; i++) {
            // 遍历所有请求统计状态
            // 为了演示，我们使用简化的计算
        }
        
        executedCount = totalRequests - pendingCount - cancelledCount;
    }

    /**
     * @dev 内部函数：从待处理列表中移除请求
     */
    function _removePendingRequest(bytes32 requestId) internal {
        for (uint256 i = 0; i < pendingRequests.length; i++) {
            if (pendingRequests[i] == requestId) {
                pendingRequests[i] = pendingRequests[pendingRequests.length - 1];
                pendingRequests.pop();
                break;
            }
        }
    }

    /**
     * @dev 重写原有的紧急提取函数，使其失效
     */
    function emergencyWithdraw(address, uint256) external pure override {
        revert("Use requestEmergencyWithdraw instead");
    }

    /**
     * @dev 批量批准多个请求
     */
    function batchApproveRequests(bytes32[] calldata requestIds) external onlyEmergencySigner {
        for (uint256 i = 0; i < requestIds.length; i++) {
            bytes32 requestId = requestIds[i];
            
            if (emergencyRequests[requestId].requestTime > 0 && 
                !emergencyRequests[requestId].executed && 
                !emergencyRequests[requestId].cancelled &&
                !emergencyApprovals[requestId][msg.sender]) {
                
                emergencyApprovals[requestId][msg.sender] = true;
                approvalCount[requestId]++;
                
                emit EmergencyWithdrawApproved(
                    requestId, 
                    msg.sender, 
                    approvalCount[requestId], 
                    requiredSignatures
                );
            }
        }
    }

    /**
     * @dev 紧急情况下的快速暂停（无时间锁）
     */
    function emergencyPause() external onlyEmergencySigner {
        require(!paused(), "Already paused");
        _pause();
    }

    /**
     * @dev 检查请求是否可以执行
     */
    function canExecuteRequest(bytes32 requestId) external view returns (bool) {
        EmergencyRequest memory request = emergencyRequests[requestId];
        
        if (request.requestTime == 0 || request.executed || request.cancelled) {
            return false;
        }
        
        if (block.timestamp < request.requestTime + EMERGENCY_DELAY) {
            return false;
        }
        
        if (approvalCount[requestId] < requiredSignatures) {
            return false;
        }
        
        if (!paused()) {
            return false;
        }
        
        return true;
    }

    /**
     * @dev 获取合约安全状态
     */
    function getSecurityStatus() external view returns (
        bool isPaused,
        uint256 pendingEmergencyRequests,
        uint256 activeSigners,
        uint256 requiredSigs,
        uint256 oldestPendingRequest
    ) {
        isPaused = paused();
        pendingEmergencyRequests = pendingRequests.length;
        activeSigners = getSignerCount();
        requiredSigs = requiredSignatures;
        
        // 找到最旧的待处理请求
        oldestPendingRequest = 0;
        if (pendingRequests.length > 0) {
            oldestPendingRequest = emergencyRequests[pendingRequests[0]].requestTime;
            for (uint256 i = 1; i < pendingRequests.length; i++) {
                uint256 requestTime = emergencyRequests[pendingRequests[i]].requestTime;
                if (requestTime < oldestPendingRequest) {
                    oldestPendingRequest = requestTime;
                }
            }
        }
    }
}
