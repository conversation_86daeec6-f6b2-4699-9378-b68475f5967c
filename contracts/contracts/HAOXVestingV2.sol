// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "./HAOXTokenV2.sol";
import "./HAOXPriceOracleV2.sol";

/**
 * @title HAOXVestingV2
 * @dev 代币解锁合约
 * 
 * 解锁机制：
 * - 总共31轮解锁（第1轮已完成）
 * - 第2-11轮：价格达到$0.00272并维持7天
 * - 第12-21轮：在前一轮基础上价格上涨50%并维持7天
 * - 第22-31轮：在前一轮基础上价格上涨20%并维持7天
 * - 每轮解锁1.5亿HAOX（项目方40%，社区60%）
 */
contract HAOXVestingV2 is ReentrancyGuard, Pausable, Ownable {
    
    HAOXTokenV2 public immutable haoxToken;
    HAOXPriceOracleV2 public priceOracle;
    
    // 解锁参数
    uint256 public constant TOTAL_ROUNDS = 31;
    uint256 public constant TOKENS_PER_ROUND = 150_000_000 * 10**18; // 1.5亿HAOX
    uint256 public constant PROJECT_SHARE = 40; // 40%
    uint256 public constant COMMUNITY_SHARE = 60; // 60%
    uint256 public constant PRICE_MAINTENANCE_PERIOD = 7 days; // 价格维持期
    
    // 初始价格触发条件
    uint256 public constant INITIAL_TRIGGER_PRICE = 272000; // $0.00272 (8位小数)
    uint256 public constant ROUND_12_21_INCREASE = 150; // 50% increase (150% of previous)
    uint256 public constant ROUND_22_31_INCREASE = 120; // 20% increase (120% of previous)
    
    // 解锁轮次结构
    struct UnlockRound {
        uint256 roundNumber;
        uint256 triggerPrice; // 触发价格（8位小数）
        uint256 priceReachedTime; // 价格首次达到时间
        bool priceConditionMet; // 价格条件是否满足
        bool unlocked; // 是否已解锁
        uint256 unlockTime; // 解锁时间
        uint256 projectTokens; // 项目方代币数量
        uint256 communityTokens; // 社区代币数量
    }
    
    // 状态变量
    uint256 public currentRound = 1; // 当前轮次（第1轮已完成）
    uint256 public totalUnlockedTokens;
    address public projectWallet;
    address public communityWallet;
    
    // 映射
    mapping(uint256 => UnlockRound) public unlockRounds;
    mapping(uint256 => uint256) public roundTriggerPrices;
    
    // 事件
    event RoundInitialized(uint256 round, uint256 triggerPrice);
    event PriceConditionMet(uint256 round, uint256 price, uint256 timestamp);
    event RoundUnlocked(
        uint256 round,
        uint256 projectTokens,
        uint256 communityTokens,
        uint256 timestamp
    );
    event WalletUpdated(string walletType, address oldWallet, address newWallet);
    
    constructor(
        address _haoxToken,
        address _priceOracle,
        address _projectWallet,
        address _communityWallet
    ) Ownable(msg.sender) {
        require(_haoxToken != address(0), "Invalid token address");
        require(_priceOracle != address(0), "Invalid oracle address");
        require(_projectWallet != address(0), "Invalid project wallet");
        require(_communityWallet != address(0), "Invalid community wallet");
        
        haoxToken = HAOXTokenV2(_haoxToken);
        priceOracle = HAOXPriceOracleV2(_priceOracle);
        projectWallet = _projectWallet;
        communityWallet = _communityWallet;
        
        // 初始化所有解锁轮次
        _initializeRounds();
    }
    
    /**
     * @dev 初始化所有解锁轮次
     */
    function _initializeRounds() internal {
        uint256 triggerPrice = INITIAL_TRIGGER_PRICE;
        
        // 第2-11轮：固定触发价格$0.00272
        for (uint256 i = 2; i <= 11; i++) {
            _createRound(i, triggerPrice);
        }
        
        // 第12-21轮：每轮价格上涨50%
        for (uint256 i = 12; i <= 21; i++) {
            triggerPrice = (triggerPrice * ROUND_12_21_INCREASE) / 100;
            _createRound(i, triggerPrice);
        }
        
        // 第22-31轮：每轮价格上涨20%
        for (uint256 i = 22; i <= 31; i++) {
            triggerPrice = (triggerPrice * ROUND_22_31_INCREASE) / 100;
            _createRound(i, triggerPrice);
        }
    }
    
    /**
     * @dev 创建解锁轮次
     */
    function _createRound(uint256 roundNumber, uint256 triggerPrice) internal {
        uint256 projectTokens = (TOKENS_PER_ROUND * PROJECT_SHARE) / 100;
        uint256 communityTokens = (TOKENS_PER_ROUND * COMMUNITY_SHARE) / 100;
        
        unlockRounds[roundNumber] = UnlockRound({
            roundNumber: roundNumber,
            triggerPrice: triggerPrice,
            priceReachedTime: 0,
            priceConditionMet: false,
            unlocked: false,
            unlockTime: 0,
            projectTokens: projectTokens,
            communityTokens: communityTokens
        });
        
        roundTriggerPrices[roundNumber] = triggerPrice;
        
        emit RoundInitialized(roundNumber, triggerPrice);
    }
    
    /**
     * @dev 检查并更新价格条件
     */
    function checkPriceConditions() external whenNotPaused {
        (uint256 currentPrice, uint256 confidence) = priceOracle.getPriceWithConfidence();
        require(confidence >= 80, "Price confidence too low");
        
        // 检查当前轮次的价格条件
        if (currentRound <= TOTAL_ROUNDS) {
            UnlockRound storage round = unlockRounds[currentRound];
            
            if (!round.priceConditionMet && currentPrice >= round.triggerPrice) {
                // 价格首次达到触发条件
                round.priceReachedTime = block.timestamp;
                round.priceConditionMet = true;
                
                emit PriceConditionMet(currentRound, currentPrice, block.timestamp);
            }
            
            // 检查是否可以解锁
            if (round.priceConditionMet && !round.unlocked) {
                if (block.timestamp >= round.priceReachedTime + PRICE_MAINTENANCE_PERIOD) {
                    // 验证价格是否持续维持
                    if (currentPrice >= round.triggerPrice) {
                        _unlockRound(currentRound);
                    } else {
                        // 价格跌破，重置条件
                        round.priceConditionMet = false;
                        round.priceReachedTime = 0;
                    }
                }
            }
        }
    }
    
    /**
     * @dev 解锁指定轮次
     */
    function _unlockRound(uint256 roundNumber) internal {
        UnlockRound storage round = unlockRounds[roundNumber];
        require(!round.unlocked, "Round already unlocked");
        require(round.priceConditionMet, "Price condition not met");
        
        // 标记为已解锁
        round.unlocked = true;
        round.unlockTime = block.timestamp;
        
        // 更新统计
        totalUnlockedTokens += TOKENS_PER_ROUND;
        
        // 转账代币
        require(
            haoxToken.transfer(projectWallet, round.projectTokens),
            "Project transfer failed"
        );
        require(
            haoxToken.transfer(communityWallet, round.communityTokens),
            "Community transfer failed"
        );
        
        emit RoundUnlocked(
            roundNumber,
            round.projectTokens,
            round.communityTokens,
            block.timestamp
        );
        
        // 移动到下一轮
        if (currentRound < TOTAL_ROUNDS) {
            currentRound++;
        }
    }
    
    /**
     * @dev 获取轮次信息
     */
    function getRoundInfo(uint256 roundNumber) external view returns (
        uint256 triggerPrice,
        uint256 priceReachedTime,
        bool priceConditionMet,
        bool unlocked,
        uint256 unlockTime,
        uint256 projectTokens,
        uint256 communityTokens
    ) {
        require(roundNumber >= 2 && roundNumber <= TOTAL_ROUNDS, "Invalid round");
        
        UnlockRound memory round = unlockRounds[roundNumber];
        return (
            round.triggerPrice,
            round.priceReachedTime,
            round.priceConditionMet,
            round.unlocked,
            round.unlockTime,
            round.projectTokens,
            round.communityTokens
        );
    }
    
    /**
     * @dev 获取当前状态
     */
    function getCurrentStatus() external view returns (
        uint256 _currentRound,
        uint256 _totalUnlockedTokens,
        uint256 _remainingTokens,
        uint256 _nextTriggerPrice,
        bool _nextRoundConditionMet
    ) {
        _currentRound = currentRound;
        _totalUnlockedTokens = totalUnlockedTokens;
        _remainingTokens = (TOTAL_ROUNDS - currentRound) * TOKENS_PER_ROUND;
        
        if (currentRound <= TOTAL_ROUNDS) {
            _nextTriggerPrice = unlockRounds[currentRound].triggerPrice;
            _nextRoundConditionMet = unlockRounds[currentRound].priceConditionMet;
        }
    }
    
    /**
     * @dev 更新项目钱包地址
     */
    function setProjectWallet(address _projectWallet) external onlyOwner {
        require(_projectWallet != address(0), "Invalid address");
        address oldWallet = projectWallet;
        projectWallet = _projectWallet;
        emit WalletUpdated("project", oldWallet, _projectWallet);
    }
    
    /**
     * @dev 更新社区钱包地址
     */
    function setCommunityWallet(address _communityWallet) external onlyOwner {
        require(_communityWallet != address(0), "Invalid address");
        address oldWallet = communityWallet;
        communityWallet = _communityWallet;
        emit WalletUpdated("community", oldWallet, _communityWallet);
    }
    
    /**
     * @dev 更新价格预言机地址
     */
    function setPriceOracle(address _priceOracle) external onlyOwner {
        require(_priceOracle != address(0), "Invalid address");
        priceOracle = HAOXPriceOracleV2(_priceOracle);
    }
    
    /**
     * @dev 紧急暂停
     */
    function pause() external onlyOwner {
        _pause();
    }
    
    /**
     * @dev 恢复运行
     */
    function unpause() external onlyOwner {
        _unpause();
    }
    
    /**
     * @dev 紧急提取代币（仅限管理员）
     */
    function emergencyWithdraw(uint256 amount) external onlyOwner {
        require(haoxToken.transfer(owner(), amount), "Transfer failed");
    }
}
