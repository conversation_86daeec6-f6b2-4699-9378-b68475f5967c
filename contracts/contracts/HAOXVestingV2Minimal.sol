// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";

/**
 * @title HAOXVestingV2Minimal
 * @dev 精简版HAOX代币解锁合约 - 成本优化版本
 * 保留核心安全功能，移除非必要特性以降低部署成本
 */
contract HAOXVestingV2Minimal is Ownable, ReentrancyGuard, Pausable {
    
    // 基础常量
    uint256 public constant TOTAL_ROUNDS = 31;
    uint256 public constant PRICE_MAINTAIN_DURATION = 7 days;
    uint256 public constant EMERGENCY_DELAY = 7 days;
    uint256 public constant MAX_EMERGENCY_AMOUNT = 1000000 * 10**18;
    
    // 核心状态变量
    IERC20 public immutable haoxToken;
    address public immutable priceOracle;
    address public immutable projectWallet;
    address public immutable communityWallet;
    
    uint256 public currentRound = 1;
    
    // 轮次信息结构（优化存储）
    struct Round {
        uint128 triggerPrice;      // 触发价格 (8位小数)
        uint64 priceReachedTime;   // 价格达到时间
        uint64 unlockTime;         // 解锁时间
        bool priceConditionMet;    // 价格条件是否满足
        bool unlocked;             // 是否已解锁
    }
    
    // 紧急提取请求结构（精简版）
    struct EmergencyRequest {
        uint128 amount;
        uint64 requestTime;
        bool executed;
        address requester;
    }
    
    // 状态映射
    mapping(uint256 => Round) public rounds;
    mapping(bytes32 => EmergencyRequest) public emergencyRequests;
    mapping(address => bool) public emergencySigners;
    
    // 精简的价格历史（仅保留最近10条）
    struct PriceCheck {
        uint64 timestamp;
        uint64 price;
        bool conditionMet;
    }
    mapping(uint256 => PriceCheck[10]) public priceHistory;
    mapping(uint256 => uint8) public historyIndex;
    
    uint256 public requiredSignatures = 1;
    
    // 事件定义（精简版）
    event PriceConditionMet(uint256 indexed roundNumber, uint256 price, uint256 timestamp);
    event RoundUnlocked(uint256 indexed roundNumber, uint256 triggerPrice, uint256 projectTokens, uint256 communityTokens, uint256 timestamp);
    event PriceConditionReset(uint256 indexed roundNumber, uint256 price, uint256 timestamp);
    event EmergencyWithdrawRequested(bytes32 indexed requestId, uint256 amount, uint256 requestTime);
    event EmergencyWithdrawExecuted(bytes32 indexed requestId, uint256 amount);

    /**
     * @dev 构造函数
     */
    constructor(
        address _haoxToken,
        address _priceOracle,
        address _projectWallet,
        address _communityWallet
    ) Ownable(msg.sender) {
        require(_haoxToken != address(0), "Invalid token address");
        require(_priceOracle != address(0), "Invalid oracle address");
        require(_projectWallet != address(0), "Invalid project wallet");
        require(_communityWallet != address(0), "Invalid community wallet");
        
        haoxToken = IERC20(_haoxToken);
        priceOracle = _priceOracle;
        projectWallet = _projectWallet;
        communityWallet = _communityWallet;
        
        // 初始化紧急签名者
        emergencySigners[msg.sender] = true;
        
        // 初始化轮次数据（精简版）
        _initializeRounds();
    }

    /**
     * @dev 初始化轮次数据（Gas优化版本）
     */
    function _initializeRounds() internal {
        // 使用紧凑的初始化方式
        uint128[31] memory prices = [
            uint128(0.01 * 10**8), uint128(0.015 * 10**8), uint128(0.02 * 10**8), uint128(0.025 * 10**8), uint128(0.03 * 10**8),
            uint128(0.035 * 10**8), uint128(0.04 * 10**8), uint128(0.045 * 10**8), uint128(0.05 * 10**8), uint128(0.055 * 10**8),
            uint128(0.06 * 10**8), uint128(0.065 * 10**8), uint128(0.07 * 10**8), uint128(0.075 * 10**8), uint128(0.08 * 10**8),
            uint128(0.085 * 10**8), uint128(0.09 * 10**8), uint128(0.095 * 10**8), uint128(0.1 * 10**8), uint128(0.11 * 10**8),
            uint128(0.12 * 10**8), uint128(0.13 * 10**8), uint128(0.14 * 10**8), uint128(0.15 * 10**8), uint128(0.16 * 10**8),
            uint128(0.17 * 10**8), uint128(0.18 * 10**8), uint128(0.19 * 10**8), uint128(0.2 * 10**8), uint128(0.22 * 10**8),
            uint128(0.25 * 10**8)
        ];
        
        for (uint256 i = 0; i < 31; i++) {
            rounds[i + 1].triggerPrice = prices[i];
        }
    }

    /**
     * @dev 检查价格条件（Gas优化版本）
     */
    function checkPriceCondition() external nonReentrant whenNotPaused {
        uint256 roundNumber = currentRound;
        
        if (roundNumber > TOTAL_ROUNDS) {
            return;
        }
        
        // 获取当前价格
        uint256 currentPrice = _getCurrentPrice();
        Round storage round = rounds[roundNumber];
        bool conditionMet = currentPrice >= round.triggerPrice;
        
        // 添加到历史记录（循环覆盖）
        uint8 index = historyIndex[roundNumber];
        priceHistory[roundNumber][index] = PriceCheck({
            timestamp: uint64(block.timestamp),
            price: uint64(currentPrice),
            conditionMet: conditionMet
        });
        historyIndex[roundNumber] = (index + 1) % 10;
        
        if (conditionMet) {
            if (!round.priceConditionMet) {
                round.priceConditionMet = true;
                round.priceReachedTime = uint64(block.timestamp);
                emit PriceConditionMet(roundNumber, currentPrice, block.timestamp);
            } else {
                uint256 maintainedDuration = block.timestamp - round.priceReachedTime;
                if (maintainedDuration >= PRICE_MAINTAIN_DURATION && !round.unlocked) {
                    _unlockRound(roundNumber, currentPrice);
                }
            }
        } else {
            if (round.priceConditionMet && !round.unlocked) {
                round.priceConditionMet = false;
                round.priceReachedTime = 0;
                emit PriceConditionReset(roundNumber, currentPrice, block.timestamp);
            }
        }
    }

    /**
     * @dev 解锁轮次（精简版）
     */
    function _unlockRound(uint256 roundNumber, uint256 triggerPrice) internal {
        Round storage round = rounds[roundNumber];
        round.unlocked = true;
        round.unlockTime = uint64(block.timestamp);
        
        // 计算代币分配（简化计算）
        uint256 projectTokens = 80000000 * 10**18; // 8000万项目代币
        uint256 communityTokens = 80000000 * 10**18; // 8000万社区代币
        
        // 转移代币
        require(haoxToken.transfer(projectWallet, projectTokens), "Project transfer failed");
        require(haoxToken.transfer(communityWallet, communityTokens), "Community transfer failed");
        
        emit RoundUnlocked(roundNumber, triggerPrice, projectTokens, communityTokens, block.timestamp);
        
        // 移动到下一轮
        currentRound = roundNumber + 1;
    }

    /**
     * @dev 获取当前价格（精简版）
     */
    function _getCurrentPrice() internal view returns (uint256) {
        (bool success, bytes memory data) = priceOracle.staticcall(
            abi.encodeWithSignature("getLatestPrice()")
        );
        require(success && data.length >= 32, "Price oracle failed");
        return abi.decode(data, (uint256));
    }

    /**
     * @dev 请求紧急提取（精简版）
     */
    function requestEmergencyWithdraw(uint256 amount) external whenPaused {
        require(emergencySigners[msg.sender], "Not authorized");
        require(amount <= MAX_EMERGENCY_AMOUNT, "Amount too large");
        
        bytes32 requestId = keccak256(abi.encodePacked(amount, block.timestamp, msg.sender));
        
        emergencyRequests[requestId] = EmergencyRequest({
            amount: uint128(amount),
            requestTime: uint64(block.timestamp),
            executed: false,
            requester: msg.sender
        });
        
        emit EmergencyWithdrawRequested(requestId, amount, block.timestamp);
    }

    /**
     * @dev 执行紧急提取（精简版）
     */
    function executeEmergencyWithdraw(bytes32 requestId) external {
        EmergencyRequest storage request = emergencyRequests[requestId];
        require(request.requestTime > 0, "Request not found");
        require(!request.executed, "Already executed");
        require(block.timestamp >= request.requestTime + EMERGENCY_DELAY, "Time lock active");
        require(emergencySigners[msg.sender], "Not authorized");
        
        request.executed = true;
        require(haoxToken.transfer(owner(), request.amount), "Transfer failed");
        
        emit EmergencyWithdrawExecuted(requestId, request.amount);
    }

    /**
     * @dev 获取解锁进度（精简版）
     */
    function getUnlockProgress() external view returns (
        uint256 currentPrice,
        uint256 nextRound,
        uint256 targetPrice,
        bool conditionMet,
        uint256 timeRemaining
    ) {
        currentPrice = _getCurrentPrice();
        nextRound = currentRound;
        
        if (nextRound <= TOTAL_ROUNDS) {
            Round memory round = rounds[nextRound];
            targetPrice = round.triggerPrice;
            conditionMet = round.priceConditionMet;
            
            if (conditionMet && round.priceReachedTime > 0) {
                uint256 elapsed = block.timestamp - round.priceReachedTime;
                timeRemaining = elapsed >= PRICE_MAINTAIN_DURATION ? 0 : PRICE_MAINTAIN_DURATION - elapsed;
            }
        }
    }

    /**
     * @dev 获取轮次信息
     */
    function getRoundInfo(uint256 roundNumber) external view returns (
        uint256 triggerPrice,
        bool priceConditionMet,
        bool unlocked,
        uint256 priceReachedTime,
        uint256 unlockTime
    ) {
        require(roundNumber > 0 && roundNumber <= TOTAL_ROUNDS, "Invalid round");
        Round memory round = rounds[roundNumber];
        
        return (
            round.triggerPrice,
            round.priceConditionMet,
            round.unlocked,
            round.priceReachedTime,
            round.unlockTime
        );
    }

    /**
     * @dev 获取价格历史（精简版）
     */
    function getPriceHistory(uint256 roundNumber) external view returns (PriceCheck[10] memory) {
        return priceHistory[roundNumber];
    }

    /**
     * @dev 管理员功能
     */
    function addEmergencySigner(address signer) external onlyOwner {
        emergencySigners[signer] = true;
    }

    function removeEmergencySigner(address signer) external onlyOwner {
        emergencySigners[signer] = false;
    }

    function pause() external onlyOwner {
        _pause();
    }

    function unpause() external onlyOwner {
        _unpause();
    }

    /**
     * @dev 紧急暂停（无时间锁）
     */
    function emergencyPause() external {
        require(emergencySigners[msg.sender], "Not authorized");
        _pause();
    }
}
