// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";

/**
 * @title HAOXTokenV2Test
 * @dev 测试版HAOX代币合约 - 缩短时间锁用于快速测试
 * 
 * 测试版特性：
 * - 时间锁延迟：5分钟（而非24小时）
 * - 暂停时间：10分钟（而非72小时）
 * - 其他功能与正式版完全相同
 */
contract HAOXTokenV2Test is ERC20, Ownable, Pausable {
    
    // 代币经济学参数
    uint256 public constant TOTAL_SUPPLY = 50_000_000_000 * 10**18; // 500亿代币
    uint256 public constant INITIAL_UNLOCK = 500_000_000 * 10**18;  // 首轮解锁5亿代币 (10%)
    
    // 关联合约地址
    address public presaleContract;
    address public invitationContract;
    address public vestingContract;
    address public priceOracle;
    
    // 时间锁机制 - 测试版缩短时间
    struct TimeLock {
        uint256 unlockTime;
        bool executed;
    }
    
    mapping(bytes32 => TimeLock) public timeLocks;
    uint256 public constant TIMELOCK_DELAY = 5 minutes; // 测试版：5分钟
    uint256 public constant MAX_PAUSE_DURATION = 10 minutes; // 测试版：10分钟
    
    // 暂停相关
    uint256 public pauseStartTime;
    
    // 事件
    event ContractAddressUpdated(string contractType, address oldAddress, address newAddress);
    event TimeLockCreated(bytes32 indexed lockId, uint256 unlockTime);
    event TimeLockExecuted(bytes32 indexed lockId);
    event EmergencyPauseActivated(uint256 duration);
    
    constructor() ERC20("HAOX Token Test", "HAOX-TEST") Ownable(msg.sender) {
        // 铸造初始解锁的代币到合约部署者
        _mint(msg.sender, INITIAL_UNLOCK);
    }
    
    /**
     * @dev 设置预售合约地址（带时间锁）
     */
    function setPresaleContract(address _presaleContract) external onlyOwner {
        bytes32 lockId = keccak256(abi.encodePacked("setPresaleContract", _presaleContract, block.timestamp));
        
        if (timeLocks[lockId].unlockTime == 0) {
            // 创建时间锁
            timeLocks[lockId] = TimeLock({
                unlockTime: block.timestamp + TIMELOCK_DELAY,
                executed: false
            });
            emit TimeLockCreated(lockId, timeLocks[lockId].unlockTime);
        } else {
            // 执行时间锁
            require(block.timestamp >= timeLocks[lockId].unlockTime, "TimeLock not expired");
            require(!timeLocks[lockId].executed, "Already executed");
            
            address oldAddress = presaleContract;
            presaleContract = _presaleContract;
            timeLocks[lockId].executed = true;
            
            emit ContractAddressUpdated("presale", oldAddress, _presaleContract);
            emit TimeLockExecuted(lockId);
        }
    }
    
    /**
     * @dev 设置邀请合约地址（带时间锁）
     */
    function setInvitationContract(address _invitationContract) external onlyOwner {
        bytes32 lockId = keccak256(abi.encodePacked("setInvitationContract", _invitationContract, block.timestamp));
        
        if (timeLocks[lockId].unlockTime == 0) {
            timeLocks[lockId] = TimeLock({
                unlockTime: block.timestamp + TIMELOCK_DELAY,
                executed: false
            });
            emit TimeLockCreated(lockId, timeLocks[lockId].unlockTime);
        } else {
            require(block.timestamp >= timeLocks[lockId].unlockTime, "TimeLock not expired");
            require(!timeLocks[lockId].executed, "Already executed");
            
            address oldAddress = invitationContract;
            invitationContract = _invitationContract;
            timeLocks[lockId].executed = true;
            
            emit ContractAddressUpdated("invitation", oldAddress, _invitationContract);
            emit TimeLockExecuted(lockId);
        }
    }
    
    /**
     * @dev 设置解锁合约地址（带时间锁）
     */
    function setVestingContract(address _vestingContract) external onlyOwner {
        bytes32 lockId = keccak256(abi.encodePacked("setVestingContract", _vestingContract, block.timestamp));
        
        if (timeLocks[lockId].unlockTime == 0) {
            timeLocks[lockId] = TimeLock({
                unlockTime: block.timestamp + TIMELOCK_DELAY,
                executed: false
            });
            emit TimeLockCreated(lockId, timeLocks[lockId].unlockTime);
        } else {
            require(block.timestamp >= timeLocks[lockId].unlockTime, "TimeLock not expired");
            require(!timeLocks[lockId].executed, "Already executed");
            
            address oldAddress = vestingContract;
            vestingContract = _vestingContract;
            timeLocks[lockId].executed = true;
            
            emit ContractAddressUpdated("vesting", oldAddress, _vestingContract);
            emit TimeLockExecuted(lockId);
        }
    }
    
    /**
     * @dev 设置价格预言机地址（带时间锁）
     */
    function setPriceOracle(address _priceOracle) external onlyOwner {
        bytes32 lockId = keccak256(abi.encodePacked("setPriceOracle", _priceOracle, block.timestamp));
        
        if (timeLocks[lockId].unlockTime == 0) {
            timeLocks[lockId] = TimeLock({
                unlockTime: block.timestamp + TIMELOCK_DELAY,
                executed: false
            });
            emit TimeLockCreated(lockId, timeLocks[lockId].unlockTime);
        } else {
            require(block.timestamp >= timeLocks[lockId].unlockTime, "TimeLock not expired");
            require(!timeLocks[lockId].executed, "Already executed");
            
            address oldAddress = priceOracle;
            priceOracle = _priceOracle;
            timeLocks[lockId].executed = true;
            
            emit ContractAddressUpdated("priceOracle", oldAddress, _priceOracle);
            emit TimeLockExecuted(lockId);
        }
    }
    
    /**
     * @dev 紧急暂停（最长10分钟 - 测试版）
     */
    function emergencyPause() external onlyOwner {
        require(!paused(), "Already paused");
        pauseStartTime = block.timestamp;
        _pause();
        emit EmergencyPauseActivated(MAX_PAUSE_DURATION);
    }
    
    /**
     * @dev 恢复运行
     */
    function unpause() external onlyOwner {
        require(paused(), "Not paused");
        _unpause();
        pauseStartTime = 0;
    }
    
    /**
     * @dev 自动解除暂停（10分钟后任何人都可以调用）
     */
    function autoUnpause() external {
        require(paused(), "Not paused");
        require(pauseStartTime > 0, "Invalid pause state");
        require(block.timestamp >= pauseStartTime + MAX_PAUSE_DURATION, "Pause duration not exceeded");
        
        _unpause();
        pauseStartTime = 0;
    }
    
    /**
     * @dev 放弃所有权（不可逆）
     */
    function renounceOwnership() public override onlyOwner {
        super.renounceOwnership();
    }
    
    /**
     * @dev 重写转账函数以支持暂停机制
     */
    function transfer(address to, uint256 amount) public override whenNotPaused returns (bool) {
        return super.transfer(to, amount);
    }
    
    /**
     * @dev 重写授权转账函数以支持暂停机制
     */
    function transferFrom(address from, address to, uint256 amount) public override whenNotPaused returns (bool) {
        return super.transferFrom(from, to, amount);
    }
    
    /**
     * @dev 获取合约状态
     */
    function getContractStatus() external view returns (
        bool isPaused,
        uint256 pauseDuration,
        uint256 remainingPauseTime,
        address presale,
        address invitation,
        address vesting,
        address oracle
    ) {
        uint256 remaining = 0;
        if (paused() && pauseStartTime > 0) {
            uint256 elapsed = block.timestamp - pauseStartTime;
            remaining = elapsed < MAX_PAUSE_DURATION ? MAX_PAUSE_DURATION - elapsed : 0;
        }
        
        return (
            paused(),
            pauseStartTime > 0 ? block.timestamp - pauseStartTime : 0,
            remaining,
            presaleContract,
            invitationContract,
            vestingContract,
            priceOracle
        );
    }
}
