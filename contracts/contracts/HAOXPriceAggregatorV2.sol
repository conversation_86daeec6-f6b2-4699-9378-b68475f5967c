// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";

/**
 * @title HAOXPriceAggregatorV2
 * @dev 多预言机价格聚合器
 * 支持多个价格源，实现价格偏差检测和自动故障转移
 */
contract HAOXPriceAggregatorV2 is Ownable, ReentrancyGuard, Pausable {
    
    // 价格源结构
    struct PriceSource {
        address oracle;
        uint256 weight;
        bool active;
        uint256 lastUpdate;
        uint256 lastPrice;
        string name;
        uint256 failureCount;
        uint256 maxFailures;
    }
    
    // 价格数据结构
    struct PriceData {
        uint256 price;
        uint256 timestamp;
        uint256 confidence;
        uint256 sourceCount;
    }
    
    // 状态变量
    mapping(uint256 => PriceSource) public priceSources;
    uint256 public sourceCount;
    
    // 聚合配置
    uint256 public constant MAX_PRICE_DEVIATION = 500; // 5% (基点)
    uint256 public constant PRICE_STALENESS_THRESHOLD = 3600; // 1小时
    uint256 public constant MIN_SOURCES_REQUIRED = 2;
    uint256 public constant MAX_SOURCES = 10;
    
    // 价格历史
    PriceData[] public priceHistory;
    uint256 public constant MAX_HISTORY_SIZE = 100;
    
    // 最新聚合价格
    PriceData public latestPrice;
    
    // 故障转移配置
    uint256 public emergencyPrice;
    uint256 public emergencyPriceTimestamp;
    bool public emergencyMode;
    
    // 事件定义
    event PriceSourceAdded(
        uint256 indexed sourceId,
        address indexed oracle,
        string name,
        uint256 weight
    );
    
    event PriceSourceUpdated(
        uint256 indexed sourceId,
        bool active,
        uint256 weight,
        uint256 maxFailures
    );
    
    event PriceSourceRemoved(uint256 indexed sourceId);
    
    event PriceAggregated(
        uint256 price,
        uint256 timestamp,
        uint256 sourceCount,
        uint256 confidence
    );
    
    event PriceSourceFailed(
        uint256 indexed sourceId,
        string reason,
        uint256 failureCount
    );
    
    event EmergencyModeActivated(uint256 price, uint256 timestamp);
    event EmergencyModeDeactivated();
    
    event PriceDeviationDetected(
        uint256 indexed sourceId,
        uint256 sourcePrice,
        uint256 aggregatedPrice,
        uint256 deviation
    );

    // 修饰符
    modifier validSourceId(uint256 sourceId) {
        require(sourceId < sourceCount, "Invalid source ID");
        require(priceSources[sourceId].oracle != address(0), "Source not exists");
        _;
    }

    /**
     * @dev 构造函数
     */
    constructor() Ownable(msg.sender) {
        // 初始化默认值
        emergencyMode = false;
    }

    /**
     * @dev 添加价格源
     */
    function addPriceSource(
        address oracle,
        string calldata name,
        uint256 weight,
        uint256 maxFailures
    ) external onlyOwner {
        require(oracle != address(0), "Invalid oracle address");
        require(weight > 0 && weight <= 100, "Invalid weight");
        require(bytes(name).length > 0, "Name required");
        require(sourceCount < MAX_SOURCES, "Too many sources");
        require(maxFailures > 0 && maxFailures <= 10, "Invalid max failures");
        
        // 检查是否已存在
        for (uint256 i = 0; i < sourceCount; i++) {
            require(priceSources[i].oracle != oracle, "Oracle already exists");
        }
        
        priceSources[sourceCount] = PriceSource({
            oracle: oracle,
            weight: weight,
            active: true,
            lastUpdate: 0,
            lastPrice: 0,
            name: name,
            failureCount: 0,
            maxFailures: maxFailures
        });
        
        emit PriceSourceAdded(sourceCount, oracle, name, weight);
        sourceCount++;
    }

    /**
     * @dev 更新价格源配置
     */
    function updatePriceSource(
        uint256 sourceId,
        bool active,
        uint256 weight,
        uint256 maxFailures
    ) external onlyOwner validSourceId(sourceId) {
        require(weight > 0 && weight <= 100, "Invalid weight");
        require(maxFailures > 0 && maxFailures <= 10, "Invalid max failures");
        
        PriceSource storage source = priceSources[sourceId];
        source.active = active;
        source.weight = weight;
        source.maxFailures = maxFailures;
        
        // 如果重新激活，重置失败计数
        if (active) {
            source.failureCount = 0;
        }
        
        emit PriceSourceUpdated(sourceId, active, weight, maxFailures);
    }

    /**
     * @dev 移除价格源
     */
    function removePriceSource(uint256 sourceId) external onlyOwner validSourceId(sourceId) {
        // 确保至少保留最小数量的源
        uint256 activeCount = getActiveSourceCount();
        require(activeCount > MIN_SOURCES_REQUIRED, "Cannot remove source below minimum");
        
        delete priceSources[sourceId];
        emit PriceSourceRemoved(sourceId);
    }

    /**
     * @dev 获取聚合价格
     */
    function getLatestPrice() external view returns (uint256) {
        if (emergencyMode) {
            require(
                block.timestamp - emergencyPriceTimestamp <= PRICE_STALENESS_THRESHOLD * 2,
                "Emergency price too stale"
            );
            return emergencyPrice;
        }
        
        require(latestPrice.timestamp > 0, "No price available");
        require(
            block.timestamp - latestPrice.timestamp <= PRICE_STALENESS_THRESHOLD,
            "Price too stale"
        );
        
        return latestPrice.price;
    }

    /**
     * @dev 获取最后更新时间
     */
    function getLastUpdateTime() external view returns (uint256) {
        if (emergencyMode) {
            return emergencyPriceTimestamp;
        }
        return latestPrice.timestamp;
    }

    /**
     * @dev 更新聚合价格
     */
    function updateAggregatedPrice() external nonReentrant whenNotPaused {
        require(!emergencyMode, "Emergency mode active");
        
        (uint256 aggregatedPrice, uint256 confidence, uint256 validSources) = _calculateAggregatedPrice();
        
        require(validSources >= MIN_SOURCES_REQUIRED, "Insufficient valid sources");
        
        // 更新最新价格
        latestPrice = PriceData({
            price: aggregatedPrice,
            timestamp: block.timestamp,
            confidence: confidence,
            sourceCount: validSources
        });
        
        // 添加到历史记录
        _addToHistory(latestPrice);
        
        emit PriceAggregated(aggregatedPrice, block.timestamp, validSources, confidence);
    }

    /**
     * @dev 计算聚合价格
     */
    function _calculateAggregatedPrice() internal returns (
        uint256 aggregatedPrice,
        uint256 confidence,
        uint256 validSources
    ) {
        uint256 totalWeight = 0;
        uint256 weightedSum = 0;
        uint256[] memory prices = new uint256[](sourceCount);
        uint256[] memory weights = new uint256[](sourceCount);
        validSources = 0;
        
        // 收集所有有效价格
        for (uint256 i = 0; i < sourceCount; i++) {
            PriceSource storage source = priceSources[i];
            if (!source.active) continue;

            // 使用低级调用替代try-catch
            (bool priceSuccess, uint256 price) = _tryGetPriceFromSource(i);
            if (!priceSuccess) {
                _handleSourceFailure(i, "Oracle call failed");
                continue;
            }

            (bool timeSuccess, uint256 updateTime) = _tryGetUpdateTimeFromSource(i);
            if (!timeSuccess) {
                updateTime = block.timestamp; // 使用当前时间作为默认值
            }

            // 检查价格是否过期
            if (block.timestamp - updateTime > PRICE_STALENESS_THRESHOLD) {
                _handleSourceFailure(i, "Price too stale");
                continue;
            }

            // 检查价格是否合理（不能为0或过大）
            if (price == 0 || price > 1e18) { // 假设最大价格为1 ETH
                _handleSourceFailure(i, "Invalid price value");
                continue;
            }

            prices[validSources] = price;
            weights[validSources] = source.weight;
            validSources++;

            weightedSum += price * source.weight;
            totalWeight += source.weight;

            // 更新源信息
            source.lastUpdate = updateTime;
            source.lastPrice = price;

            // 重置失败计数（成功获取价格）
            if (source.failureCount > 0) {
                source.failureCount = 0;
            }
        }
        
        require(validSources >= MIN_SOURCES_REQUIRED, "Insufficient valid sources");
        
        aggregatedPrice = weightedSum / totalWeight;
        
        // 验证价格偏差
        _validatePriceDeviation(prices, weights, validSources, aggregatedPrice);
        
        // 计算置信度（基于源数量和权重分布）
        confidence = _calculateConfidence(validSources, totalWeight);
        
        return (aggregatedPrice, confidence, validSources);
    }

    /**
     * @dev 从价格源获取价格
     */
    function _getPriceFromSource(uint256 sourceId) internal view returns (uint256) {
        PriceSource memory source = priceSources[sourceId];
        
        // 调用预言机接口
        (bool success, bytes memory data) = source.oracle.staticcall(
            abi.encodeWithSignature("getLatestPrice()")
        );
        
        require(success, "Oracle call failed");
        require(data.length >= 32, "Invalid response");
        
        return abi.decode(data, (uint256));
    }

    /**
     * @dev 从价格源获取更新时间
     */
    function _getUpdateTimeFromSource(uint256 sourceId) internal view returns (uint256) {
        PriceSource memory source = priceSources[sourceId];

        // 尝试获取更新时间
        (bool success, bytes memory data) = source.oracle.staticcall(
            abi.encodeWithSignature("getLastUpdateTime()")
        );

        if (success && data.length >= 32) {
            return abi.decode(data, (uint256));
        }

        // 如果没有更新时间接口，使用当前时间
        return block.timestamp;
    }

    /**
     * @dev 尝试从价格源获取价格（安全版本）
     */
    function _tryGetPriceFromSource(uint256 sourceId) internal view returns (bool success, uint256 price) {
        PriceSource memory source = priceSources[sourceId];

        (bool callSuccess, bytes memory data) = source.oracle.staticcall(
            abi.encodeWithSignature("getLatestPrice()")
        );

        if (callSuccess && data.length >= 32) {
            price = abi.decode(data, (uint256));
            success = true;
        } else {
            success = false;
            price = 0;
        }
    }

    /**
     * @dev 尝试从价格源获取更新时间（安全版本）
     */
    function _tryGetUpdateTimeFromSource(uint256 sourceId) internal view returns (bool success, uint256 updateTime) {
        PriceSource memory source = priceSources[sourceId];

        (bool callSuccess, bytes memory data) = source.oracle.staticcall(
            abi.encodeWithSignature("getLastUpdateTime()")
        );

        if (callSuccess && data.length >= 32) {
            updateTime = abi.decode(data, (uint256));
            success = true;
        } else {
            success = false;
            updateTime = block.timestamp;
        }
    }

    /**
     * @dev 验证价格偏差
     */
    function _validatePriceDeviation(
        uint256[] memory prices,
        uint256[] memory weights,
        uint256 count,
        uint256 aggregatedPrice
    ) internal {
        for (uint256 i = 0; i < count; i++) {
            uint256 deviation = prices[i] > aggregatedPrice
                ? (prices[i] - aggregatedPrice) * 10000 / aggregatedPrice
                : (aggregatedPrice - prices[i]) * 10000 / aggregatedPrice;
                
            if (deviation > MAX_PRICE_DEVIATION) {
                emit PriceDeviationDetected(i, prices[i], aggregatedPrice, deviation);
                
                // 如果偏差过大，降低该源的权重或标记为失败
                if (deviation > MAX_PRICE_DEVIATION * 2) {
                    _handleSourceFailure(i, "Price deviation too high");
                }
            }
        }
    }

    /**
     * @dev 计算置信度
     */
    function _calculateConfidence(uint256 validSourceCount, uint256 totalWeight) internal pure returns (uint256) {
        // 基础置信度基于源数量
        uint256 baseConfidence = validSourceCount * 20; // 每个源贡献20%
        if (baseConfidence > 80) baseConfidence = 80;

        // 权重分布奖励（权重越分散，置信度越高）
        uint256 weightBonus = totalWeight > 0 ? (validSourceCount * 100) / totalWeight : 0;
        if (weightBonus > 20) weightBonus = 20;
        
        uint256 confidence = baseConfidence + weightBonus;
        return confidence > 100 ? 100 : confidence;
    }

    /**
     * @dev 处理源失败
     */
    function _handleSourceFailure(uint256 sourceId, string memory reason) internal {
        PriceSource storage source = priceSources[sourceId];
        source.failureCount++;
        
        emit PriceSourceFailed(sourceId, reason, source.failureCount);
        
        // 如果失败次数过多，自动禁用
        if (source.failureCount >= source.maxFailures) {
            source.active = false;
            emit PriceSourceUpdated(sourceId, false, source.weight, source.maxFailures);
        }
    }

    /**
     * @dev 添加到历史记录
     */
    function _addToHistory(PriceData memory priceData) internal {
        if (priceHistory.length >= MAX_HISTORY_SIZE) {
            // 移除最旧的记录
            for (uint256 i = 0; i < priceHistory.length - 1; i++) {
                priceHistory[i] = priceHistory[i + 1];
            }
            priceHistory[priceHistory.length - 1] = priceData;
        } else {
            priceHistory.push(priceData);
        }
    }

    /**
     * @dev 激活紧急模式
     */
    function activateEmergencyMode(uint256 price) external onlyOwner {
        require(price > 0, "Invalid emergency price");
        
        emergencyMode = true;
        emergencyPrice = price;
        emergencyPriceTimestamp = block.timestamp;
        
        emit EmergencyModeActivated(price, block.timestamp);
    }

    /**
     * @dev 停用紧急模式
     */
    function deactivateEmergencyMode() external onlyOwner {
        require(emergencyMode, "Emergency mode not active");
        require(getActiveSourceCount() >= MIN_SOURCES_REQUIRED, "Insufficient active sources");
        
        emergencyMode = false;
        emergencyPrice = 0;
        emergencyPriceTimestamp = 0;
        
        emit EmergencyModeDeactivated();
    }

    /**
     * @dev 获取活跃源数量
     */
    function getActiveSourceCount() public view returns (uint256) {
        uint256 count = 0;
        for (uint256 i = 0; i < sourceCount; i++) {
            if (priceSources[i].active && priceSources[i].oracle != address(0)) {
                count++;
            }
        }
        return count;
    }

    /**
     * @dev 获取所有价格源信息
     */
    function getAllSources() external view returns (
        address[] memory oracles,
        string[] memory names,
        uint256[] memory weights,
        bool[] memory activeStates,
        uint256[] memory lastPrices,
        uint256[] memory failureCounts
    ) {
        oracles = new address[](sourceCount);
        names = new string[](sourceCount);
        weights = new uint256[](sourceCount);
        activeStates = new bool[](sourceCount);
        lastPrices = new uint256[](sourceCount);
        failureCounts = new uint256[](sourceCount);
        
        for (uint256 i = 0; i < sourceCount; i++) {
            PriceSource memory source = priceSources[i];
            oracles[i] = source.oracle;
            names[i] = source.name;
            weights[i] = source.weight;
            activeStates[i] = source.active;
            lastPrices[i] = source.lastPrice;
            failureCounts[i] = source.failureCount;
        }
    }

    /**
     * @dev 获取价格历史
     */
    function getPriceHistory(uint256 limit) external view returns (PriceData[] memory) {
        uint256 length = priceHistory.length;
        if (limit > length) limit = length;
        
        PriceData[] memory result = new PriceData[](limit);
        for (uint256 i = 0; i < limit; i++) {
            result[i] = priceHistory[length - limit + i];
        }
        
        return result;
    }

    /**
     * @dev 获取聚合器状态
     */
    function getAggregatorStatus() external view returns (
        uint256 totalSources,
        uint256 activeSources,
        bool isEmergencyMode,
        uint256 lastUpdateTime,
        uint256 latestPriceValue,
        uint256 confidence
    ) {
        return (
            sourceCount,
            getActiveSourceCount(),
            emergencyMode,
            latestPrice.timestamp,
            latestPrice.price,
            latestPrice.confidence
        );
    }

    /**
     * @dev 暂停合约
     */
    function pause() external onlyOwner {
        _pause();
    }

    /**
     * @dev 恢复合约
     */
    function unpause() external onlyOwner {
        _unpause();
    }

    /**
     * @dev 批量更新价格源状态
     */
    function batchUpdateSources(
        uint256[] calldata sourceIds,
        bool[] calldata activeStates
    ) external onlyOwner {
        require(sourceIds.length == activeStates.length, "Array length mismatch");

        for (uint256 i = 0; i < sourceIds.length; i++) {
            if (sourceIds[i] < sourceCount && priceSources[sourceIds[i]].oracle != address(0)) {
                priceSources[sourceIds[i]].active = activeStates[i];

                // 如果重新激活，重置失败计数
                if (activeStates[i]) {
                    priceSources[sourceIds[i]].failureCount = 0;
                }

                emit PriceSourceUpdated(
                    sourceIds[i],
                    activeStates[i],
                    priceSources[sourceIds[i]].weight,
                    priceSources[sourceIds[i]].maxFailures
                );
            }
        }
    }

    /**
     * @dev 重置源失败计数
     */
    function resetSourceFailures(uint256 sourceId) external onlyOwner validSourceId(sourceId) {
        priceSources[sourceId].failureCount = 0;

        // 如果源被禁用，重新激活
        if (!priceSources[sourceId].active) {
            priceSources[sourceId].active = true;
            emit PriceSourceUpdated(
                sourceId,
                true,
                priceSources[sourceId].weight,
                priceSources[sourceId].maxFailures
            );
        }
    }

    /**
     * @dev 获取详细的源状态
     */
    function getSourceDetails(uint256 sourceId) external view validSourceId(sourceId) returns (
        address oracle,
        string memory name,
        uint256 weight,
        bool active,
        uint256 lastUpdate,
        uint256 lastPrice,
        uint256 failureCount,
        uint256 maxFailures,
        bool isStale
    ) {
        PriceSource memory source = priceSources[sourceId];
        bool stale = source.lastUpdate > 0 &&
                    (block.timestamp - source.lastUpdate) > PRICE_STALENESS_THRESHOLD;

        return (
            source.oracle,
            source.name,
            source.weight,
            source.active,
            source.lastUpdate,
            source.lastPrice,
            source.failureCount,
            source.maxFailures,
            stale
        );
    }
}
